# MySQL数据库 'walrus' 结构分析报告

**分析时间**: 2025/6/19 21:44:58
**数据库**: walrus
**总表数**: 87

## 📊 数据库概览

| 表名 | 记录数 | 字段数 | 主要用途推测 |
|------|--------|--------|----------------|
| aicc_call_text | 9 | 13 | 待分析 |
| aics_kb | 6 | 11 | 待分析 |
| aics_kb_allow_list | 7 | 5 | 待分析 |
| aics_kb_rel | 667 | 9 | 待分析 |
| aics_kb_update | 27 | 16 | 待分析 |
| analysis_results | 3 | 4 | 待分析 |
| annot_annotation | 68 | 6 | 对话/聊天记录 |
| annot_annotation_tags | 86 | 6 | 待分析 |
| annot_conversation | 1,704 | 10 | 对话/聊天记录 |
| annot_conversation_evaluation | 25 | 8 | 对话/聊天记录 |
| annot_message | 0 | 6 | 待分析 |
| annot_reviewer | 11 | 9 | 待分析 |
| annot_task | 142 | 6 | 待分析 |
| annot_task_assignment | 577 | 7 | 待分析 |
| annot_task_conversation | 1,640 | 4 | 对话/聊天记录 |
| announcement | 15,686 | 32 | 对话/聊天记录 |
| announcement_project | 15,713 | 8 | 项目信息 |
| announcement_type | 112 | 29 | 对话/聊天记录 |
| announcement_type_classification | 7 | 6 | 待分析 |
| authority_check_house | 43 | 11 | 待分析 |
| authority_check_robin | 99 | 11 | 待分析 |
| callback_log | 359,984 | 8 | 日志/审计信息 |
| chat_clue | 342 | 31 | 对话/聊天记录 |
| chat_customer | 0 | 11 | 对话/聊天记录 |
| chat_label_records | 218 | 8 | 对话/聊天记录 |
| chat_list | 2,530 | 33 | 对话/聊天记录 |
| chat_list_event | 18 | 10 | 对话/聊天记录 |
| chat_messages | 10 | 6 | 待分析 |
| chat_msg | 29,053 | 39 | 对话/聊天记录 |
| chat_msg_event | 2,350,497 | 8 | 对话/聊天记录 |
| chat_msg_log | 58 | 17 | 对话/聊天记录 |
| chat_notice_record | 8 | 10 | 待分析 |
| chat_progress | 0 | 8 | 对话/聊天记录 |
| chat_records | 0 | 10 | 对话/聊天记录 |
| chat_records_details | 0 | 9 | 待分析 |
| chat_request | 1,005 | 32 | 对话/聊天记录 |
| chat_request_event | 1,833 | 16 | 待分析 |
| chuan_class | 4,704 | 51 | 待分析 |
| chuan_class_prod | 8,309 | 50 | 待分析 |
| daily_message_stats | 1 | 3 | 对话/聊天记录 |
| fm_business_types | 96 | 10 | 待分析 |
| kdb_main_question | 900,915 | 20 | 待分析 |
| message_extend | 0 | 18 | 待分析 |
| out_user | 16 | 17 | 用户/成员信息 |
| prompt_notice | 23 | 12 | 待分析 |
| rm_role_config | 4 | 10 | 配置/设置信息 |
| sys_active_org | 139 | 10 | 待分析 |
| sys_agent | 63 | 10 | 待分析 |
| sys_app_config | 4 | 16 | 配置/设置信息 |
| sys_bot | 72 | 34 | 待分析 |
| sys_bot_agent_rel | 92 | 6 | 待分析 |
| sys_bot_config | 265 | 12 | 配置/设置信息 |
| sys_bot_scenario | 2 | 9 | 待分析 |
| sys_bot_status | 82 | 9 | 待分析 |
| sys_dict | 57 | 8 | 待分析 |
| sys_download_task | 252 | 13 | 待分析 |
| sys_knowledge | 913 | 16 | 待分析 |
| sys_knowledge_audit | 20 | 9 | 日志/审计信息 |
| sys_knowledge_audit_detail | 35 | 14 | 日志/审计信息 |
| sys_knowledge_category | 11 | 9 | 待分析 |
| sys_knowledge_template | 117 | 11 | 待分析 |
| sys_project_config | 60 | 13 | 项目信息 |
| sys_role | 15 | 11 | 待分析 |
| sys_role_route | 905 | 8 | 待分析 |
| sys_route | 56 | 14 | 待分析 |
| sys_user | 66 | 20 | 用户/成员信息 |
| sys_user_org | 67 | 9 | 用户/成员信息 |
| sys_user_role | 126 | 6 | 用户/成员信息 |
| sys_visit | 0 | 8 | 待分析 |
| sys_visit_statistics | 3,985 | 8 | 待分析 |
| user_activity_stats | 5 | 5 | 对话/聊天记录 |
| user_opinion | 17 | 8 | 用户/成员信息 |
| users | 5 | 4 | 用户/成员信息 |
| wechat_app_config | 1 | 7 | 配置/设置信息 |
| work_class | 385 | 9 | 待分析 |
| work_order | 5 | 20 | 工单信息 |
| wx_mini_prgm_config | 1 | 12 | 配置/设置信息 |
| wxkf_cursor | 3 | 8 | 待分析 |
| wxkf_msg | 12,615 | 5 | 对话/聊天记录 |
| xxl_job_group | 2 | 6 | 待分析 |
| xxl_job_info | 30 | 24 | 待分析 |
| xxl_job_lock | 1 | 1 | 待分析 |
| xxl_job_log | 3,959,350 | 15 | 日志/审计信息 |
| xxl_job_log_report | 304 | 6 | 日志/审计信息 |
| xxl_job_logglue | 0 | 7 | 日志/审计信息 |
| xxl_job_registry | 1 | 5 | 待分析 |
| xxl_job_user | 5 | 5 | 用户/成员信息 |

## 🗂️ 详细表结构

### aicc_call_text

**记录数**: 9
**字段数**: 13

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| call_index | int(11) | YES |  |  |  |
| text | varchar(255) | YES |  |  |  |
| talker_type | varchar(50) | YES |  |  |  |
| from_num | varchar(50) | YES |  |  |  |
| to_num | varchar(50) | YES |  |  |  |
| speed | varchar(50) | YES |  |  |  |
| start | int(11) | YES |  |  |  |
| end | int(11) | YES |  |  |  |
| send_time | bigint(20) | YES |  |  |  |
| deleted | tinyint(4) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

#### 样本数据
| id | call_index | text | talker_type | from_num | to_num | speed | start | end | send_time | deleted | create_time | update_time |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 2 | 嗯 | 88880001 | 13579 | 88880001 | 39.47 | 5240 | 12000 | 1724314042519 | 0 | Wed Sep 04 2024 14:27:52 GMT+0800 (中国标准时间) | Wed Sep 04 2024 14:27:52 GMT+0800 (中国标准时间) |
| 2 | 1 | 你好你好 | 88887777 | 88887777 | 8001 | 1818.18 | 496 | 628 | 1726123683636 | 0 | Thu Sep 12 2024 14:49:56 GMT+0800 (中国标准时间) | Thu Sep 12 2024 14:49:56 GMT+0800 (中国标准时间) |
| 3 | 2 | 。您好您好 | 88887777 | 88887777 | 8001 | 2272.73 | 685 | 817 | 1726123685457 | 0 | Thu Sep 12 2024 14:49:57 GMT+0800 (中国标准时间) | Thu Sep 12 2024 14:49:57 GMT+0800 (中国标准时间) |

---

### aics_kb

**记录数**: 6
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| bus_id | int(11) | NO |  |  |  |
| bot_id | varchar(64) | NO |  |  |  |
| project_code | varchar(32) | NO | MUL |  |  |
| create_time | datetime | NO |  |  |  |
| update_time | datetime | NO |  |  |  |
| create_user_id | varchar(164) | NO |  |  |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| collect_id | varchar(64) | NO |  |  |  |
| secret | varchar(100) | YES |  |  |  |

#### 样本数据
| id | bus_id | bot_id | project_code | create_time | update_time | create_user_id | update_user_id | deleted | collect_id | secret |
|---|---|---|---|---|---|---|---|---|---|---|
| 1907342963420012500 | 480031136 | 66c6b191e4b03b463a4dd0c2 | 42010169 | Thu Apr 03 2025 18:37:16 GMT+0800 (中国标准时间) | Thu Apr 03 2025 18:37:17 GMT+0800 (中国标准时间) | 00837187 | 00837187 | 0 | 480035923 | YYwdVCfVSmedenMDOGdLMMIBJAYBXZ... |
| 1909449413780639700 | 480031306 | 6746dbcfe4b003bca96ec382 | 44030016 | Tue Apr 08 2025 11:33:11 GMT+0800 (中国标准时间) | Tue Apr 08 2025 11:33:11 GMT+0800 (中国标准时间) | luoqi |  | 0 | 480032765 | insHtowASYegtmMPmdhJCYFntZkLRc... |
| 1909902734885073000 | 480031310 | 67f63572e4b0b393d4553fe0 | 44030032 | Wed Apr 09 2025 17:34:31 GMT+0800 (中国标准时间) | Wed Apr 09 2025 17:34:31 GMT+0800 (中国标准时间) | luoqi |  | 0 | 480032781 | eGYsDONmFWfeNdQDilvhOcAPBYIVtq... |

---

### aics_kb_allow_list

**记录数**: 7
**字段数**: 5

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| question | varchar(256) | YES | MUL |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | question | create_time | update_time | deleted |
|---|---|---|---|---|
| 1 | 月租车办理习惯 | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | 0 |
| 2 | 车位办理规则 | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | 0 |
| 3 | 装修延期办理流程 | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | Thu Apr 03 2025 18:48:42 GMT+0800 (中国标准时间) | 0 |

---

### aics_kb_rel

**记录数**: 667
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| knowledge_id | varchar(64) | YES | MUL |  |  |
| aics_knowledge_id | varchar(64) | YES | MUL |  |  |
| source_knowledge_id | varchar(64) | YES | MUL |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | knowledge_id | aics_knowledge_id | source_knowledge_id | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|
| 1000000 | 381697 | 381697 | 381697 | Mon Mar 31 2025 12:19:12 GMT+0800 (中国标准时间) |  | Mon Mar 31 2025 12:19:12 GMT+0800 (中国标准时间) |  | 0 |
| 1000001 | 381615 | 381615 | 381615 | Mon Mar 31 2025 12:19:27 GMT+0800 (中国标准时间) |  | Mon Mar 31 2025 12:19:27 GMT+0800 (中国标准时间) |  | 0 |
| 1000503 | 480777 | 480777 | 261535 | Wed Apr 02 2025 16:53:18 GMT+0800 (中国标准时间) |  | Wed Apr 02 2025 16:53:18 GMT+0800 (中国标准时间) |  | 0 |

---

### aics_kb_update

**记录数**: 27
**字段数**: 16

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| aics_knowledge_id | varchar(64) | YES | MUL |  |  |
| knowledge_id | varchar(64) | YES | MUL |  |  |
| source_knowledge_id | varchar(64) | YES | MUL |  |  |
| project_code | varchar(64) | NO |  |  |  |
| source_type | tinyint(4) | NO |  | 0 |  |
| answer | longtext | YES |  |  |  |
| remark | varchar(200) | YES |  |  |  |
| submit_mobile | varchar(16) | NO |  |  |  |
| submit_name | varchar(64) | YES |  |  |  |
| update_status | tinyint(4) | NO |  | 0 |  |
| create_user_id | varchar(64) | NO |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | aics_knowledge_id | knowledge_id | source_knowledge_id | project_code | source_type | answer | remark | submit_mobile | submit_name | update_status | create_user_id | create_time | update_user_id | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 381697 | 381697 | 381697 |  | 0 | 这是新的答案 | 测试 | 18827513332 | 桑葚(王志远) | 0 | aqhiIii | Mon Mar 31 2025 15:23:29 GMT+0800 (中国标准时间) | aqhiIii | Mon Mar 31 2025 15:23:29 GMT+0800 (中国标准时间) | 0 |
| 3 | 481007 | 481007 | 21200551 |  | 0 | 项目无泳池，后期有再通知 |  | 13728696572 | 罗祺 | 0 | luoqi | Wed Apr 02 2025 17:42:58 GMT+0800 (中国标准时间) | luoqi | Wed Apr 02 2025 17:42:58 GMT+0800 (中国标准时间) | 0 |
| 4 | 480932 | 480932 | 559188 |  | 0 | 物业是否能清洗客户家的暖气过滤网：不涉及哦 |  | 18827513332 | 桑葚(王志远) | 0 | aqhiIii | Wed Apr 02 2025 18:35:29 GMT+0800 (中国标准时间) | aqhiIii | Wed Apr 02 2025 18:35:29 GMT+0800 (中国标准时间) | 0 |

---

### analysis_results

**记录数**: 3
**字段数**: 4

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | varchar(36) | NO | PRI |  |  |
| analysis_type | varchar(50) | NO | MUL |  |  |
| result_data | json | NO |  |  |  |
| created_at | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |

#### 样本数据
| id | analysis_type | result_data | created_at |
|---|---|---|---|
| analysis-001 | sentiment_analysis | [object Object] | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |
| analysis-002 | activity_analysis | [object Object] | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |
| analysis-003 | keyword_analysis | [object Object] | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |

---

### annot_annotation

**记录数**: 68
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| message_id | varchar(64) | NO | MUL |  |  |
| reviewer_id | varchar(64) | NO | MUL |  |  |
| annotated_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| final_mark | tinyint(4) | NO | MUL | 0 |  |

#### 样本数据
| id | message_id | reviewer_id | annotated_time | deleted | final_mark |
|---|---|---|---|---|---|
| 1 | 7193fac8aa48058b276dd7637f9982... | luoqi | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | 0 | 0 |
| 2 | 06335b5dea6b5e436115b88a2b6748... | luoqi | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | 0 | 0 |
| 3 | d7c50f8e58e7fe99a5abc3347d50cd... | luoqi | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | 0 | 0 |

---

### annot_annotation_tags

**记录数**: 86
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| annotation_id | bigint(20) unsigned | NO | MUL |  |  |
| tag | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | YES |  |  |  |
| description | varchar(500) | YES |  |  |  |

#### 样本数据
| id | annotation_id | tag | deleted | create_time | description |
|---|---|---|---|---|---|
| 1 | 1 | no_issue | 0 | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | NULL |
| 2 | 2 | no_issue | 0 | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | NULL |
| 3 | 3 | answer_error | 0 | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | NULL |

---

### annot_conversation

**记录数**: 1,704
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| chat_id | varchar(64) | NO | UNI |  |  |
| create_time | datetime | NO |  |  |  |
| update_time | datetime | YES |  |  |  |
| topic | text | YES |  |  |  |
| stored_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| audit_status | tinyint(1) | NO |  | 0 |  |
| project_code | varchar(50) | YES | MUL |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| audit_user_id | varchar(64) | YES |  |  |  |

#### 样本数据
| id | chat_id | create_time | update_time | topic | stored_time | audit_status | project_code | deleted | audit_user_id |
|---|---|---|---|---|---|---|---|---|---|
| 1047 | deed12dde626427cb850a7e5a41ff1... | Tue Nov 12 2024 09:24:51 GMT+0800 (中国标准时间) | Tue Nov 12 2024 10:06:33 GMT+0800 (中国标准时间) | 住户：我想询问停水和停电的时间及送水服务。
AI：我提供了停... | Tue Nov 12 2024 15:31:42 GMT+0800 (中国标准时间) | 0 | 44030011 | 0 | NULL |
| 1048 | 6b5c3a40304b4e2897daae5ef15e53... | Mon Nov 11 2024 18:07:14 GMT+0800 (中国标准时间) | Mon Nov 11 2024 18:07:14 GMT+0800 (中国标准时间) | 住户：我想请求帮助，但表达不清楚。
AI：我询问您提供更多详... | Tue Nov 12 2024 15:31:42 GMT+0800 (中国标准时间) | 0 | 44030011 | 0 | NULL |
| 1049 | 9d3d0a788b014b7382a68a8dd51540... | Mon Nov 11 2024 18:05:16 GMT+0800 (中国标准时间) | Mon Nov 11 2024 18:05:16 GMT+0800 (中国标准时间) | 住户：我想知道有什么可以帮忙的。
AI：我询问您需要什么帮助... | Tue Nov 12 2024 15:31:42 GMT+0800 (中国标准时间) | 0 | 44030011 | 0 | NULL |

---

### annot_conversation_evaluation

**记录数**: 25
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| conversation_id | bigint(20) | NO | MUL |  |  |
| reviewer_id | varchar(64) | NO | MUL |  |  |
| overall_rating | tinyint(1) | YES |  |  |  |
| comments | text | YES |  |  |  |
| evaluated_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| final_mark | tinyint(4) | NO |  |  |  |

#### 样本数据
| id | conversation_id | reviewer_id | overall_rating | comments | evaluated_time | deleted | final_mark |
|---|---|---|---|---|---|---|---|
| 1 | 1131 | luoqi | 2 | NULL | Thu Dec 05 2024 11:18:34 GMT+0800 (中国标准时间) | 0 | 0 |
| 2 | 1129 | luoqi | 1 | NULL | Thu Dec 05 2024 11:18:53 GMT+0800 (中国标准时间) | 0 | 0 |
| 3 | 1130 | luoqi | 4 | NULL | Thu Dec 05 2024 11:19:38 GMT+0800 (中国标准时间) | 0 | 0 |

---

### annot_message

**记录数**: 0
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| conversation_id | bigint(20) | NO | MUL |  |  |
| sender | tinyint(1) | NO |  |  |  |
| content | text | NO |  |  |  |
| send_time | datetime | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

---

### annot_reviewer

**记录数**: 11
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | UNI |  |  |
| create_user_id | varchar(64) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| default_reviewer | tinyint(1) | NO |  | 1 |  |
| name | varchar(64) | NO |  |  |  |
| mobile | varchar(32) | YES |  |  |  |

#### 样本数据
| id | user_id | create_user_id | create_time | update_time | deleted | default_reviewer | name | mobile |
|---|---|---|---|---|---|---|---|---|
| 1059 | ZengXiang | ZengXiang | Fri Nov 15 2024 14:51:43 GMT+0800 (中国标准时间) | Thu Nov 28 2024 17:38:16 GMT+0800 (中国标准时间) | 0 | 1 | 小曾 | 13414892567 |
| 1060 | luoqi | ZengXiang | Thu Nov 21 2024 10:09:43 GMT+0800 (中国标准时间) | Thu Dec 05 2024 15:15:57 GMT+0800 (中国标准时间) | 0 | 1 | luoqi | 13728696572 |
| 1061 | mazheng | mazheng | Thu Nov 21 2024 14:19:53 GMT+0800 (中国标准时间) | Thu Nov 28 2024 17:39:43 GMT+0800 (中国标准时间) | 0 | 1 | 马正 | 16620982629 |

---

### annot_task

**记录数**: 142
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| name | varchar(255) | NO |  |  |  |
| type | varchar(100) | NO |  |  |  |
| created_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| active | tinyint(1) | NO |  | 1 |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | name | type | created_time | active | deleted |
|---|---|---|---|---|---|
| 1024 | 标注任务2024-11-11 | auto | Mon Nov 11 2024 23:59:59 GMT+0800 (中国标准时间) | 1 | 0 |
| 1027 | 标注任务2024-11-20 | auto | Wed Nov 20 2024 23:59:59 GMT+0800 (中国标准时间) | 1 | 0 |
| 1028 | 标注任务2024-11-25 | auto | Mon Nov 25 2024 23:59:59 GMT+0800 (中国标准时间) | 1 | 0 |

---

### annot_task_assignment

**记录数**: 577
**字段数**: 7

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| task_id | bigint(20) | NO | MUL |  |  |
| reviewer_id | varchar(64) | NO | MUL |  |  |
| assigned_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| status | tinyint(1) | NO |  | 0 |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| completed_time | datetime | YES |  |  |  |

#### 样本数据
| id | task_id | reviewer_id | assigned_time | status | deleted | completed_time |
|---|---|---|---|---|---|---|
| 3072 | 1024 | ZengXiang | Fri Nov 15 2024 14:52:20 GMT+0800 (中国标准时间) | 0 | 0 | NULL |
| 3073 | 1024 | luoqi | Thu Nov 21 2024 10:10:03 GMT+0800 (中国标准时间) | 0 | 0 | NULL |
| 3074 | 1024 | t2 | Thu Nov 21 2024 10:25:06 GMT+0800 (中国标准时间) | 0 | 0 | NULL |

---

### annot_task_conversation

**记录数**: 1,640
**字段数**: 4

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| task_id | bigint(20) | NO | MUL |  |  |
| conversation_id | bigint(20) | NO | MUL |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | task_id | conversation_id | deleted |
|---|---|---|---|
| 1024 | 1024 | 1048 | 0 |
| 1025 | 1024 | 1049 | 0 |
| 1026 | 1024 | 1050 | 0 |

---

### announcement

**记录数**: 15,686
**字段数**: 32

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| staff_id | int(11) | NO |  |  |  |
| title | text | YES |  |  |  |
| push_title | text | YES |  |  |  |
| abstract | text | YES |  |  |  |
| content | text | YES |  |  |  |
| type | int(11) | YES |  |  |  |
| expired | datetime | NO |  |  |  |
| images | text | NO |  |  |  |
| share_text | text | NO |  |  |  |
| url | varchar(256) | YES |  |  |  |
| project_code | varchar(64) | YES | MUL |  |  |
| created | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| updated | datetime | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| business_type | varchar(64) | YES |  |  |  |
| fullname | varchar(32) | NO |  |  |  |
| pop_start_time | datetime | YES |  |  |  |
| pop_end_time | datetime | YES |  |  |  |
| grids | text | NO |  |  |  |
| is_global | tinyint(4) | NO |  | 0 |  |
| publish_status | smallint(6) | NO | MUL | 1 |  |
| common_video | varchar(256) | NO |  |  |  |
| wechat_video | varchar(256) | NO |  |  |  |
| comment_count | int(11) | NO |  | 0 |  |
| up_count | int(11) | NO |  | 0 |  |
| read_count | int(11) | NO |  | 0 |  |
| share_count | int(11) | NO |  | 0 |  |
| official_content_data | text | YES |  |  |  |
| official_template_name | varchar(255) | YES |  |  |  |
| serv_notice_title_data | varchar(128) | YES |  |  |  |
| serv_notice_content_data | text | YES |  |  |  |

#### 样本数据
| id | staff_id | title | push_title | abstract | content | type | expired | images | share_text | url | project_code | created | updated | deleted | business_type | fullname | pop_start_time | pop_end_time | grids | is_global | publish_status | common_video | wechat_video | comment_count | up_count | read_count | share_count | official_content_data | official_template_name | serv_notice_title_data | serv_notice_content_data |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1613802 | 2095594 | 文明养宠 | 文明养宠 | 文明养宠 | 文明养宠 | 41 | Fri Mar 07 2025 00:00:00 GMT+0800 (中国标准时间) | [{"url":"https://img2.40095151... | 文明养宠（登录住这儿APP，查看公告详情） | https://enterprise.4009515151.... | 32060054 | Wed Mar 05 2025 10:19:02 GMT+0800 (中国标准时间) | Thu Mar 06 2025 15:20:12 GMT+0800 (中国标准时间) | 0 | NULL | 徐刘松 | Wed Mar 05 2025 10:19:02 GMT+0800 (中国标准时间) | Fri Mar 07 2025 00:00:00 GMT+0800 (中国标准时间) | [{"grid_name": "南通上湖春晓雅苑网格A", ... | 0 | 1 |  |  | 0 | 0 | 1 | 0 | [] |  |  |  |
| 1613803 | 2281213 | 管家播报 | 管家播报 | 【管家播报-周检巡查篇】 | 一次检查、一次提升，一次督导，一次成长;迎检是常态，做好日常... | 96 | Thu Mar 06 2025 00:00:00 GMT+0800 (中国标准时间) | [{"url":"https://img2.40095151... | 【管家播报-周检巡查篇】（登录住这儿APP，查看公告详情） | https://enterprise.4009515151.... | 33020233 | Wed Mar 05 2025 10:19:20 GMT+0800 (中国标准时间) | Mon Mar 10 2025 17:40:15 GMT+0800 (中国标准时间) | 0 | NULL | 韩街琴 | Wed Mar 05 2025 10:19:20 GMT+0800 (中国标准时间) | Thu Mar 06 2025 00:00:00 GMT+0800 (中国标准时间) | [{"grid_name": "宁波朗拾里筹备组网格A", ... | 0 | 1 |  |  | 0 | 0 | 5 | 0 | [] |  |  |  |
| 1613804 | 1573657 | 电梯维保 | 电梯维保 | 万科物业A72阵地（星海城）字【2025】016号 关于3月... |   | 98 | Mon Mar 31 2025 00:00:00 GMT+0800 (中国标准时间) | [{"url":"https://img2.40095151... | 万科物业A72阵地（星海城）字【2025】016号 关于3月... | https://enterprise.4009515151.... | 35030003 | Wed Mar 05 2025 10:19:27 GMT+0800 (中国标准时间) | Sun Mar 09 2025 12:00:37 GMT+0800 (中国标准时间) | 0 | NULL | 胡德明 | Wed Mar 05 2025 10:19:27 GMT+0800 (中国标准时间) | Mon Mar 31 2025 00:00:00 GMT+0800 (中国标准时间) | [{"grid_name": "莆田星海城网格C", "gr... | 0 | 1 |  |  | 0 | 0 | 3 | 0 | [] |  |  |  |

---

### announcement_project

**记录数**: 15,713
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| announcement_id | int(11) | NO | MUL |  |  |
| project_code | varchar(64) | NO | MUL |  |  |
| project_name | varchar(32) | NO |  |  |  |
| grids | text | NO |  |  |  |
| created | datetime | NO |  |  |  |
| updated | datetime | NO |  |  |  |
| deleted | tinyint(1) | NO |  |  |  |

#### 样本数据
| id | announcement_id | project_code | project_name | grids | created | updated | deleted |
|---|---|---|---|---|---|---|---|
| 2231754 | 1613875 | 31000056 | 上海万科海尚华庭 | [{"grid_name": "上海万科海尚华庭网格A", ... | Wed Mar 05 2025 10:37:19 GMT+0800 (中国标准时间) | Wed Mar 05 2025 10:37:19 GMT+0800 (中国标准时间) | 0 |
| 2231755 | 1613876 | 32060001 | 南通金域蓝湾 | [{"grid_name": "南通金域蓝湾网格A", "g... | Wed Mar 05 2025 10:37:50 GMT+0800 (中国标准时间) | Wed Mar 05 2025 10:37:50 GMT+0800 (中国标准时间) | 0 |
| 2231756 | 1613877 | 44190224 | 东莞山水华庭 | [{"grid_name": "Q东莞富盈山水华庭网格A",... | Wed Mar 05 2025 10:38:50 GMT+0800 (中国标准时间) | Wed Mar 05 2025 10:38:50 GMT+0800 (中国标准时间) | 0 |

---

### announcement_type

**记录数**: 112
**字段数**: 29

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| staff_id | int(11) | NO |  |  |  |
| name | varchar(128) | NO |  |  |  |
| push_title_template | text | YES |  |  |  |
| detail_template | text | NO |  |  |  |
| abstract_template | text | NO |  |  |  |
| image_url | varchar(256) | NO |  |  |  |
| thumb_url | varchar(256) | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| created | datetime | NO |  | CURRENT_TIMESTAMP |  |
| updated | datetime | NO |  | CURRENT_TIMESTAMP |  |
| tags | text | YES |  |  |  |
| images | tinyint(1) | NO |  | 0 |  |
| share_text | varchar(256) | NO |  |  |  |
| fullname | varchar(32) | NO |  |  |  |
| operator_id | int(11) | NO |  | 0 |  |
| operator_name | varchar(32) | NO |  |  |  |
| cards | text | NO |  |  |  |
| frontline_visible | tinyint(4) | NO |  | 1 |  |
| keeper_empowerment | tinyint(4) | NO |  | 1 |  |
| classification_id | int(11) | NO |  | 6 |  |
| videos | tinyint(1) | NO |  | 0 |  |
| neighbor_official | tinyint(4) | NO |  | 0 |  |
| wechat_msg_type | tinyint(4) | NO |  | 0 |  |
| official_template_id | varchar(64) | YES |  |  |  |
| official_template_name | varchar(255) | YES |  |  |  |
| official_content | text | YES |  |  |  |
| serv_notice_title | varchar(128) | YES |  |  |  |
| serv_notice_content | text | YES |  |  |  |

#### 样本数据
| id | staff_id | name | push_title_template | detail_template | abstract_template | image_url | thumb_url | deleted | created | updated | tags | images | share_text | fullname | operator_id | operator_name | cards | frontline_visible | keeper_empowerment | classification_id | videos | neighbor_official | wechat_msg_type | official_template_id | official_template_name | official_content | serv_notice_title | serv_notice_content |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 1498751 | 停水通知 | 停水通知 | <详情> | <概述> | https://vanke-app.oss-cn-shenz... | https://vanke-app.oss-cn-shenz... | 0 | Sun May 05 2019 17:59:56 GMT+0800 (中国标准时间) | Thu Jan 09 2025 10:04:30 GMT+0800 (中国标准时间) | [{"prop":"param0","name":"投放项目... | 1 | <概述>（登录住这儿APP，查看公告详情） | 王静艺 | 1498751 | 王静艺 | [{"url": "https://mj.400951515... | 1 | 1 | 1 | 1 | 0 | 0 | NULL | NULL | NULL | NULL | NULL |
| 2 | 1498751 | 停电通知 | 停电通知 | <详情> | <概述> | https://vanke-app.oss-cn-shenz... | https://vanke-app.oss-cn-shenz... | 0 | Sun May 05 2019 18:02:07 GMT+0800 (中国标准时间) | Thu Jan 09 2025 10:04:30 GMT+0800 (中国标准时间) | [{"prop":"param0","name":"投放项目... | 1 | <概述>（登录住这儿APP，查看公告详情） | 王静艺 | 1498751 | 王静艺 | [{"url": "https://mj.400951515... | 1 | 1 | 1 | 0 | 0 | 0 | NULL | NULL | NULL | NULL | NULL |
| 3 | 1498751 | 停梯通知 | 停梯通知 | <详情> | <概述> | https://vanke-app.oss-cn-shenz... | https://vanke-app.oss-cn-shenz... | 0 | Sun May 05 2019 18:06:10 GMT+0800 (中国标准时间) | Thu Jan 09 2025 10:04:30 GMT+0800 (中国标准时间) | [{"prop":"param0","name":"投放项目... | 1 | <概述>（登录住这儿APP，查看公告详情） | 王静艺 | 1498751 | 王静艺 | [{"url": "https://mj.400951515... | 1 | 1 | 1 | 1 | 0 | 0 | NULL | NULL | NULL | NULL | NULL |

---

### announcement_type_classification

**记录数**: 7
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| name | varchar(128) | NO |  |  |  |
| sort | int(11) | NO |  | 1 |  |
| created | datetime | NO |  |  |  |
| updated | datetime | NO |  |  |  |
| deleted | tinyint(1) | NO |  |  |  |

#### 样本数据
| id | name | sort | created | updated | deleted |
|---|---|---|---|---|---|
| 1 | 紧急公告 | 1 | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | 0 |
| 2 | 临时公告 | 1 | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | 0 |
| 3 | 温馨提示 | 1 | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | Thu Dec 15 2022 19:21:37 GMT+0800 (中国标准时间) | 0 |

---

### authority_check_house

**记录数**: 43
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| external_user_id | varchar(64) | NO | MUL |  |  |
| owner_name | varchar(32) | NO |  |  |  |
| house_code | varchar(64) | NO |  |  |  |
| project_code | varchar(64) | NO |  |  |  |
| project_name | varchar(64) | NO |  |  |  |
| grid_code | varchar(64) | NO |  |  |  |
| grid_name | varchar(64) | NO |  |  |  |
| create_time | datetime | YES |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | YES |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | external_user_id | owner_name | house_code | project_code | project_name | grid_code | grid_name | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|
| 17 | wm0fChEAAAwLJbybeKAMCA3BJegzMk... | 吕江霞 | 4403040132000110000060103021 | 44030011 | 深圳金色家园 | 44030011B | 深圳金色家园网格B | Tue Apr 22 2025 17:29:53 GMT+0800 (中国标准时间) | Tue Apr 29 2025 17:12:51 GMT+0800 (中国标准时间) | 0 |
| 18 | wm0fChEAAAwLJbybeKAMCA3BJegzMk... | 罗勇 | 4403040132000110000020104023 | 44030011 | 深圳金色家园 | 44030011A | 深圳金色家园网格A | Tue Apr 22 2025 17:58:15 GMT+0800 (中国标准时间) | Tue Apr 29 2025 17:12:51 GMT+0800 (中国标准时间) | 0 |
| 19 | wm0fChEAAAIPkWmIW_vPD59K80QOxZ... | 朱文有 | 4403040132000110000010111019 | 44030011 | 深圳金色家园 | 44030011A | 深圳金色家园网格A | Wed Apr 23 2025 09:32:27 GMT+0800 (中国标准时间) | Wed Apr 30 2025 09:32:18 GMT+0800 (中国标准时间) | 1 |

---

### authority_check_robin

**记录数**: 99
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| external_user_id | varchar(64) | NO | MUL |  |  |
| project_code | varchar(64) | NO |  |  |  |
| project_name | varchar(64) | NO |  |  |  |
| grid_code | varchar(64) | NO |  |  |  |
| grid_name | varchar(64) | NO |  |  |  |
| wecom_user_id | varchar(64) | NO |  |  |  |
| wecom_user_name | varchar(64) | NO |  |  |  |
| rel_order | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | external_user_id | project_code | project_name | grid_code | grid_name | wecom_user_id | wecom_user_name | rel_order | create_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|
| 221 | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... | 44030022 | 深圳第五园 | 44030022A | 深圳第五园网格A | 17620456677 | 卢銮 | 0 | Thu Apr 17 2025 13:49:42 GMT+0800 (中国标准时间) | 0 |
| 222 | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... | 44030042 | 深圳八意府 | 44030042E | 深圳八意府网格E | 13928443253 | 胡文豪 | 0 | Thu Apr 17 2025 13:49:42 GMT+0800 (中国标准时间) | 0 |
| 223 | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... | 44030042 | 深圳八意府 |  |  | v0382402 | 李瑞芳 | 0 | Thu Apr 17 2025 13:49:42 GMT+0800 (中国标准时间) | 0 |

---

### callback_log

**记录数**: 359,984
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| biz_id | varchar(64) | NO | MUL |  |  |
| biz_sources | varchar(16) | NO |  |  |  |
| callback_msg | longtext | YES |  |  |  |
| handle_status | tinyint(4) | NO |  | 0 |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | biz_id | biz_sources | callback_msg | handle_status | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|
| 1 |  | chuan | {"nonce":"203457","msg_signatu... | 1 | Thu Aug 15 2024 16:30:03 GMT+0800 (中国标准时间) | Thu Aug 15 2024 16:30:03 GMT+0800 (中国标准时间) | 0 |
| 2 |  | chuan | {"nonce":"232894","msg_signatu... | 1 | Thu Aug 15 2024 16:30:06 GMT+0800 (中国标准时间) | Thu Aug 15 2024 16:30:06 GMT+0800 (中国标准时间) | 0 |
| 3 |  | chuan | {"nonce":"931503","msg_signatu... | 1 | Thu Aug 15 2024 16:30:07 GMT+0800 (中国标准时间) | Thu Aug 15 2024 16:30:07 GMT+0800 (中国标准时间) | 0 |

---

### chat_clue

**记录数**: 342
**字段数**: 31

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO |  |  |  |
| clue_id | bigint(20) | YES | MUL |  |  |
| bot_id | varchar(64) | NO |  |  |  |
| clue_status | int(11) | NO |  | 0 |  |
| message_id | varchar(64) | NO | MUL |  |  |
| customer_name | varchar(64) | NO |  |  |  |
| customer_mobile | varchar(64) | NO | MUL |  |  |
| sex | varchar(2) | NO |  |  |  |
| city_name | varchar(64) | NO |  |  |  |
| city_code | varchar(32) | NO |  |  |  |
| project_name | varchar(64) | NO |  |  |  |
| requirement_type | varchar(64) | NO |  |  |  |
| rent_price | varchar(64) | YES |  |  |  |
| sale_price | varchar(64) | YES |  |  |  |
| purpose | varchar(64) | YES |  |  |  |
| origin_desc | text | YES |  |  |  |
| customer_id | varchar(64) | NO |  |  |  |
| project_code | varchar(32) | NO |  |  |  |
| matter_type | varchar(64) | NO |  |  |  |
| matter_type_name | varchar(64) | NO |  |  |  |
| custom_address | varchar(255) | NO |  |  |  |
| entrust_name | varchar(64) | NO |  |  |  |
| entrust_mobile | varchar(64) | NO |  |  |  |
| house_code | varchar(64) | YES |  |  |  |
| house_name | varchar(255) | YES |  |  |  |
| belong_type | varchar(64) | YES |  |  |  |
| report_remarks | text | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | user_id | clue_id | bot_id | clue_status | message_id | customer_name | customer_mobile | sex | city_name | city_code | project_name | requirement_type | rent_price | sale_price | purpose | origin_desc | customer_id | project_code | matter_type | matter_type_name | custom_address | entrust_name | entrust_mobile | house_code | house_name | belong_type | report_remarks | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 |  | NULL |  | 3 | 7403a387ee594b96b7042284eca08c... | 罗祺 | 13728696572 | 1 | 深圳 | 440300 | 深圳金色家园 | 求租 | 3000 |  | 住宅 | 租赁2房，预算3000元 |  | 44030011 |  |  |  | 人生果 | 13728696572 | NULL | NULL | NULL | NULL | Thu Nov 14 2024 11:58:15 GMT+0800 (中国标准时间) | Thu Dec 05 2024 14:03:14 GMT+0800 (中国标准时间) | 0 |
| 2 |  | NULL |  | -1 | 7403a387ee594b96b7042284eca08c... | 罗祺 | 13728696572 | 1 | 深圳 | 440300 | 深圳金色家园 | 求租 | 3000 |  | 住宅 | 租赁2房，预算3000元 |  | 44030011 |  |  |  | 人生果 | 13728696572 | NULL | NULL | NULL | NULL | Thu Nov 14 2024 11:58:20 GMT+0800 (中国标准时间) | Thu Nov 14 2024 11:58:20 GMT+0800 (中国标准时间) | 0 |
| 3 |  | NULL |  | -1 | 7403a387ee594b96b7042284eca08c... | 罗祺 | 13728696572 | 1 | 深圳 | 440300 | 深圳金色家园 | 求租 | 3000 |  | 住宅 | 租赁2房，预算3000元 |  | 44030011 |  |  |  | 人生果 | 13728696572 | NULL | NULL | NULL | NULL | Thu Nov 14 2024 11:59:58 GMT+0800 (中国标准时间) | Thu Nov 14 2024 11:59:58 GMT+0800 (中国标准时间) | 0 |

---

### chat_customer

**记录数**: 0
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| chat_id | varchar(64) | NO |  |  |  |
| union_id | varchar(64) | NO |  |  |  |
| external_user_id | varchar(64) | NO |  |  |  |
| user_id | varchar(64) | NO | MUL |  |  |
| name | varchar(255) | NO |  |  |  |
| avatar | text | YES |  |  |  |
| wxid | varchar(64) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

---

### chat_label_records

**记录数**: 218
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| message_id | varchar(64) | NO | MUL |  |  |
| content | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| creator | varchar(20) | YES |  |  |  |
| updator | varchar(20) | YES |  |  |  |

#### 样本数据
| id | message_id | content | deleted | create_time | update_time | creator | updator |
|---|---|---|---|---|---|---|---|
| 1 | ae2bca4211db76d7d7dd2c3a9666fc... | 误报工单 | 1 | Thu Aug 15 2024 10:03:55 GMT+0800 (中国标准时间) | Thu Aug 15 2024 10:12:36 GMT+0800 (中国标准时间) | LiZhiPeng | LiZhiPeng |
| 2 | ae2bca4211db76d7d7dd2c3a9666fc... | 回复太长 | 1 | Thu Aug 15 2024 10:03:55 GMT+0800 (中国标准时间) | Thu Aug 15 2024 10:05:53 GMT+0800 (中国标准时间) | LiZhiPeng | LiZhiPeng |
| 3 | ae2bca4211db76d7d7dd2c3a9666fc... | 回复错误 | 1 | Thu Aug 15 2024 10:05:53 GMT+0800 (中国标准时间) | Thu Aug 15 2024 11:38:28 GMT+0800 (中国标准时间) | 00837187 | LiZhiPeng |

---

### chat_list

**记录数**: 2,530
**字段数**: 33

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| chat_id | varchar(64) | NO | UNI |  |  |
| check_chat_id | varchar(64) | YES |  |  |  |
| bot_id | varchar(64) | NO | MUL |  |  |
| bot_user_id | varchar(64) | YES | MUL |  |  |
| bot_user_name | varchar(64) | YES | MUL |  |  |
| type | tinyint(3) unsigned | NO | PRI | 0 |  |
| im_contact_id | varchar(64) | YES |  |  |  |
| external_user_id | varchar(64) | YES |  |  |  |
| external_user_name | varchar(64) | YES | MUL |  |  |
| im_room_id | varchar(64) | NO |  |  |  |
| room_wecom_chat_id | varchar(64) | NO | MUL |  |  |
| avatar | text | YES |  |  |  |
| name | varchar(256) | NO |  |  |  |
| project_name | varchar(64) | YES |  |  |  |
| project_code | varchar(64) | YES |  |  |  |
| summary | text | YES |  |  |  |
| emotion_type | varchar(32) | YES |  |  |  |
| platform | tinyint(3) unsigned | NO |  | 0 |  |
| current_progress | varchar(20) | NO |  |  |  |
| satisfaction | varchar(12) | NO |  |  |  |
| unsatisfactory_reason | varchar(512) | YES |  |  |  |
| details_count | int(11) | NO |  | 0 |  |
| trigger_buz_chance | tinyint(1) | YES |  | 0 |  |
| trigger_event | tinyint(1) | YES |  | 0 |  |
| trigger_emotion | tinyint(1) | YES |  | 0 |  |
| trigger_wy_mini | tinyint(1) | YES |  | 0 |  |
| trigger_to_person | tinyint(1) | YES |  | 0 |  |
| extra_data | longtext | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| customer_deleted | tinyint(1) | YES |  | 0 |  |

#### 样本数据
| id | chat_id | check_chat_id | bot_id | bot_user_id | bot_user_name | type | im_contact_id | external_user_id | external_user_name | im_room_id | room_wecom_chat_id | avatar | name | project_name | project_code | summary | emotion_type | platform | current_progress | satisfaction | unsatisfactory_reason | details_count | trigger_buz_chance | trigger_event | trigger_emotion | trigger_wy_mini | trigger_to_person | extra_data | create_time | update_time | deleted | customer_deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 65eff7dfbd25acec2ff9b441 | NULL | 65eff157a2e175c60c892ff8 | 13385263619 | 客服11 | 0 | 7881300444907885 | 111 | 老李 |  |  | https://wx.qlogo.cn/mmhead/Q3a... | aqHi | 哈哈哈 | hhh | 住户：无具体诉求，仅打招呼
AI：提供系统版本信息和各类新闻... | 无聊 | 0 |  |  | NULL | 27 | 1 | 0 | 0 | 0 | 0 | NULL | Fri Jul 19 2024 11:07:55 GMT+0800 (中国标准时间) | Fri Jul 19 2024 11:07:55 GMT+0800 (中国标准时间) | 0 | 0 |
| 2 | 660ab580bd25acec2fbabb72 | NULL | 65eff157a2e175c60c892ff8 | 13385263619 | NULL | 0 | 1688854848505367 | wo0fChEAAALgJHlOsol3gJXOsglMpA... | NULL |  |  | https://wework.qpic.cn/wwpic/2... | 吴致远 | NULL | NULL | 住户：添加我的企业微信与我联系吧。
AI：内测期间，需要使用... |  | 0 |  |  | NULL | 180 | 0 | 1 | 0 | 0 | 0 | NULL | Fri Jul 19 2024 18:15:47 GMT+0800 (中国标准时间) | Fri Jul 19 2024 18:15:47 GMT+0800 (中国标准时间) | 0 | 0 |
| 3 | 6699dc7fbd25acec2f5b6307 | NULL | 668fe074496ee0c5d905f027 | 18025489654 | NULL | 0 | 1688857178640228 | 00837187 | NULL |  |  | https://wework.qpic.cn/wwpic3a... | 捕蝇草 | NULL | NULL | 住户：你好
AI：你好！有什么我可以帮你的吗？
住户：你是谁... | 无语 | 0 |  |  | NULL | 66 | 0 | 0 | 1 | 0 | 0 | NULL | Tue Jul 23 2024 14:38:48 GMT+0800 (中国标准时间) | Thu Jul 25 2024 19:49:21 GMT+0800 (中国标准时间) | 0 | 0 |

---

### chat_list_event

**记录数**: 18
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| chat_id | varchar(64) | NO | MUL |  |  |
| type | varchar(64) | NO |  |  |  |
| external_userid | varchar(64) | YES |  |  |  |
| kf_id | varchar(64) | YES |  |  |  |
| kf_name | varchar(64) | YES |  |  |  |
| kf_avatar | varchar(256) | YES |  |  |  |
| start_time | datetime | YES |  |  |  |
| end_time | datetime | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | chat_id | type | external_userid | kf_id | kf_name | kf_avatar | start_time | end_time | deleted |
|---|---|---|---|---|---|---|---|---|---|
| 3 | 43f56989d04042c88b00465784e273... | person | wm0fChEAAAIPkWmIW_vPD59K80QOxZ... | 05026606 | 刘彭博 | https://wework.qpic.cn/wwpic3a... | Fri Nov 22 2024 16:20:38 GMT+0800 (中国标准时间) | NULL | 0 |
| 4 | 43f56989d04042c88b00465784e273... | person | wm0fChEAAAIPkWmIW_vPD59K80QOxZ... | 05026606 | 刘彭博 | https://wework.qpic.cn/wwpic3a... | Fri Nov 22 2024 16:22:28 GMT+0800 (中国标准时间) | NULL | 0 |
| 5 | d807ce8886f94d52907dd926a5034c... | person | wm0fChEAAAIPkWmIW_vPD59K80QOxZ... | 05026606 | 刘彭博 | https://wework.qpic.cn/wwpic3a... | Mon Nov 25 2024 09:28:11 GMT+0800 (中国标准时间) | Mon Nov 25 2024 09:30:55 GMT+0800 (中国标准时间) | 0 |

---

### chat_messages

**记录数**: 10
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | varchar(36) | NO | PRI |  |  |
| content | text | NO | MUL |  |  |
| user_id | varchar(36) | NO | MUL |  |  |
| timestamp | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |
| metadata | json | YES |  |  |  |
| created_at | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |

#### 样本数据
| id | content | user_id | timestamp | metadata | created_at |
|---|---|---|---|---|---|
| msg-001 | 大家好，这是第一条测试消息！ | user-001 | Mon Jan 01 2024 10:00:00 GMT+0800 (中国标准时间) | [object Object] | Mon Jan 01 2024 10:00:00 GMT+0800 (中国标准时间) |
| msg-002 | 今天天气真不错，适合出去走走。 | user-002 | Mon Jan 01 2024 10:05:00 GMT+0800 (中国标准时间) | [object Object] | Mon Jan 01 2024 10:05:00 GMT+0800 (中国标准时间) |
| msg-003 | 有人在吗？我遇到了一个技术问题。 | user-003 | Mon Jan 01 2024 10:10:00 GMT+0800 (中国标准时间) | [object Object] | Mon Jan 01 2024 10:10:00 GMT+0800 (中国标准时间) |

---

### chat_msg

**记录数**: 29,053
**字段数**: 39

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| session_id | varchar(32) | NO | MUL |  |  |
| chat_id | varchar(64) | NO | MUL |  |  |
| avatar | text | YES |  |  |  |
| bot_id | varchar(64) | NO |  |  |  |
| bot_user_id | varchar(64) | NO |  |  |  |
| contact_name | varchar(64) | NO |  |  |  |
| contact_type | int(11) | YES |  |  |  |
| coworker | tinyint(1) | YES |  |  |  |
| external_user_id | varchar(64) | NO | MUL |  |  |
| customer_external_user_id | varchar(64) | NO | MUL |  |  |
| im_bot_id | varchar(64) | NO |  |  |  |
| im_contact_id | varchar(64) | NO |  |  |  |
| im_room_id | varchar(64) | NO |  |  |  |
| self | tinyint(1) | YES |  |  |  |
| message_id | varchar(64) | YES | UNI |  |  |
| message_type | int(11) | YES |  |  |  |
| org_id | varchar(64) | YES |  |  |  |
| payload | text | YES |  |  |  |
| room_topic | varchar(64) | NO |  |  |  |
| room_wecom_chat_id | varchar(64) | NO |  |  |  |
| send_by | varchar(64) | NO |  |  |  |
| source | int(11) | YES |  |  |  |
| token | varchar(255) | YES |  |  |  |
| timestamp | bigint(20) | YES |  |  |  |
| agent_id | varchar(32) | NO |  |  |  |
| question_id | varchar(64) | NO | MUL |  |  |
| trigger_buz_chance | tinyint(1) | YES |  | 0 |  |
| trigger_event | tinyint(1) | YES |  | 0 |  |
| trigger_emotion | tinyint(1) | YES |  | 0 |  |
| trigger_knowledge | tinyint(1) | NO |  | 0 |  |
| person_kf | tinyint(1) | YES |  | 0 |  |
| exist_label | tinyint(1) | YES |  | 0 |  |
| emotion_type | varchar(32) | YES |  |  |  |
| platform | tinyint(3) unsigned | NO |  | 0 |  |
| deleted | tinyint(4) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| customer_deleted | tinyint(1) | YES |  | 0 |  |

#### 样本数据
| id | session_id | chat_id | avatar | bot_id | bot_user_id | contact_name | contact_type | coworker | external_user_id | customer_external_user_id | im_bot_id | im_contact_id | im_room_id | self | message_id | message_type | org_id | payload | room_topic | room_wecom_chat_id | send_by | source | token | timestamp | agent_id | question_id | trigger_buz_chance | trigger_event | trigger_emotion | trigger_knowledge | person_kf | exist_label | emotion_type | platform | deleted | create_time | update_time | customer_deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 328851fb0b211f210bc1d43098018e... | 65f10e95bd25acec2f54198f | https://wework.qpic.cn/wwhead/... | 65eff157a2e175c60c892ff8 | 13385263619 | 殷政 B10AAFG05 | 3 | 1 | 00768287 |  | 1688857689478191 | 1688850875736434 |  | 0 | be044f95391d2cc37a986414933688... | 7 | 65ea7587ea45ba2d44021639 | {"text":"6月22日不对，重新确认"} |  |  |  | 0 | 51609114101a493b87e84e92ab100a... | 1711699856660 |  |  | 0 | 0 | 0 | 0 | 0 | 0 | NULL | 0 | 0 | Fri Mar 29 2024 16:10:59 GMT+0800 (中国标准时间) | Fri Mar 29 2024 16:10:59 GMT+0800 (中国标准时间) | 0 |
| 2 | be7391120f650f44f933fbf255e22e... | 65f10e95bd25acec2f54198f | https://wework.qpic.cn/wwpic3a... | 65eff157a2e175c60c892ff8 | 13385263619 | 小可AI | 3 | 1 | 13385263619 |  | 1688857689478191 | 1688857689478191 |  | 1 | f6a5608381003565b5039fc9758e68... | 7 | 65ea7587ea45ba2d44021639 | {"text":"我理解您对端午节具体日期的关注。根据我所知... |  |  |  | 6 | 51609114101a493b87e84e92ab100a... | 1711699873406 |  |  | 0 | 0 | 0 | 0 | 0 | 0 | NULL | 0 | 0 | Fri Mar 29 2024 16:11:16 GMT+0800 (中国标准时间) | Fri Mar 29 2024 16:11:16 GMT+0800 (中国标准时间) | 0 |
| 3 | 328851fb0b211f210bc1d43098018e... | 65f10e95bd25acec2f54198f | https://wework.qpic.cn/wwhead/... | 65eff157a2e175c60c892ff8 | 13385263619 | 殷政 B10AAFG05 | 3 | 1 | 00768287 |  | 1688857689478191 | 1688850875736434 |  | 0 | 251a9d816f904855589780afc11eb0... | 7 | 65ea7587ea45ba2d44021639 | {"text":"公历6月22日不对"} |  |  |  | 0 | 51609114101a493b87e84e92ab100a... | 1711699916126 |  |  | 0 | 0 | 0 | 0 | 0 | 0 | NULL | 0 | 0 | Fri Mar 29 2024 16:11:59 GMT+0800 (中国标准时间) | Fri Mar 29 2024 16:11:59 GMT+0800 (中国标准时间) | 0 |

---

### chat_msg_event

**记录数**: 2,350,497
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| message_id | varchar(64) | NO | MUL |  |  |
| biz_id | varchar(64) | NO | MUL |  |  |
| biz_type | varchar(16) | NO |  |  |  |
| biz_result | text | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | message_id | biz_id | biz_type | biz_result | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|
| 1 | 452001192972124161 | test123 | CHANCE | 返回给AI的那句话 | Fri Aug 23 2024 10:56:01 GMT+0800 (中国标准时间) | Fri Aug 23 2024 10:56:01 GMT+0800 (中国标准时间) | 0 |
| 2 | test | test123 | CHANCE | test111 | Fri Aug 23 2024 10:56:25 GMT+0800 (中国标准时间) | Fri Aug 23 2024 10:56:25 GMT+0800 (中国标准时间) | 0 |
| 3 | test222 | test111 | CHANCE | test333 | Fri Aug 23 2024 15:01:34 GMT+0800 (中国标准时间) | Fri Aug 23 2024 15:01:34 GMT+0800 (中国标准时间) | 0 |

---

### chat_msg_log

**记录数**: 58
**字段数**: 17

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| message_id | varchar(64) | NO | MUL |  |  |
| external_request_id | varchar(64) | NO | UNI |  |  |
| send_code | int(11) | YES |  |  |  |
| errcode | int(11) | YES |  |  |  |
| errmsg | varchar(128) | YES |  |  |  |
| timestamp | bigint(20) | YES | MUL |  |  |
| request_id | varchar(64) | NO |  |  |  |
| org_id | varchar(64) | NO |  |  |  |
| send_status | tinyint(4) | NO |  | 0 |  |
| edit_status | tinyint(4) | NO |  | 0 |  |
| score | int(11) | YES |  |  |  |
| creator | varchar(16) | NO |  | USER |  |
| meta_info | longtext | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | message_id | external_request_id | send_code | errcode | errmsg | timestamp | request_id | org_id | send_status | edit_status | score | creator | meta_info | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1815691290780303400 | 8dfb6c58a890397173bbedc617379e... | 85fb76d8f3fb4ce09114fb7e1bb417... | 0 | NULL | NULL | 1721729513506 | 669f81e88258de79c10cc9bf | 65ea7587ea45ba2d44021639 | 0 | 0 | NULL | ASSISTANT | NULL | Tue Jul 23 2024 18:11:52 GMT+0800 (中国标准时间) | Tue Jul 23 2024 18:11:52 GMT+0800 (中国标准时间) | 0 |
| 1815691332635263000 | bfd3e750f878a43051b5c28f0d8b94... | 132b4dae9c624ba5b20541df821abf... | 0 | NULL | NULL | 1721729523232 | 669f81f223d54f2e3c3d284b | 65ea7587ea45ba2d44021639 | 0 | 0 | NULL | ASSISTANT | NULL | Tue Jul 23 2024 18:12:02 GMT+0800 (中国标准时间) | Tue Jul 23 2024 18:12:02 GMT+0800 (中国标准时间) | 0 |
| 1815945490210685000 | f9b2abf574fe73daf2b5ae0db8eac2... | d12ef34a7c1d4f5aa8fae4545ceec4... | 0 | NULL | NULL | 1721790120219 | 66a06ea623d54f2e3c3d64ff | 65ea7587ea45ba2d44021639 | 0 | 0 | NULL | SYSTEM | NULL | Wed Jul 24 2024 11:01:58 GMT+0800 (中国标准时间) | Wed Jul 24 2024 11:01:58 GMT+0800 (中国标准时间) | 0 |

---

### chat_notice_record

**记录数**: 8
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| room_id | varchar(64) | NO |  |  |  |
| customer_id | varchar(64) | NO | MUL |  |  |
| external_request_id | varchar(64) | NO | MUL |  |  |
| type | int(10) unsigned | NO |  | 0 |  |
| msg | text | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | user_id | room_id | customer_id | external_request_id | type | msg | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|
| 100000 | 18025489654 | R:70857910839733 | 00837187 | 5f757c74665c4854b4f5b46839a069... | 1 | B10AAFC142客户王先生11:02反馈1楼有垃圾，请同... | Wed Jul 24 2024 11:07:04 GMT+0800 (中国标准时间) | Wed Jul 24 2024 11:07:04 GMT+0800 (中国标准时间) | 0 |
| 100001 | 18025489654 | R:70857910839733 | 00837187 | babbe05262244e4184dd1ac5c23e8d... | 1 | 1楼大门口有烟头，请同事帮忙尽快处理。 | Wed Jul 24 2024 11:11:03 GMT+0800 (中国标准时间) | Wed Jul 24 2024 11:11:03 GMT+0800 (中国标准时间) | 0 |
| 100002 | 18025489654 | R:70857910839733 | 00837187 | 4d1216a2a3d243cabe97c19a198a64... | 0 | 2栋客户王先生11:32反馈水龙头坏了，请安排师傅尽快处理。 | Wed Jul 24 2024 11:32:31 GMT+0800 (中国标准时间) | Wed Jul 24 2024 11:32:31 GMT+0800 (中国标准时间) | 0 |

---

### chat_progress

**记录数**: 0
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| chat_id | varchar(64) | NO | MUL |  |  |
| previous_progress | varchar(20) | NO |  |  |  |
| current_progress | varchar(20) | NO |  |  |  |
| notes | text | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

---

### chat_records

**记录数**: 0
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| chat_theme | varchar(500) | YES |  |  |  |
| contact_id | varchar(64) | YES |  |  |  |
| contact_name | varchar(64) | YES |  |  |  |
| steward_id | varchar(64) | YES |  |  |  |
| steward_name | varchar(64) | YES |  |  |  |
| project | varchar(64) | YES |  |  |  |
| project_code | varchar(64) | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

---

### chat_records_details

**记录数**: 0
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| rel_id | int(11) | NO | MUL |  |  |
| content | varchar(500) | NO |  |  |  |
| role | varchar(64) | NO |  |  |  |
| trigger_buz_chance | tinyint(1) | YES |  | 0 |  |
| trigger_event | tinyint(1) | YES |  | 0 |  |
| trigger_emotion | tinyint(1) | YES |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

---

### chat_request

**记录数**: 1,005
**字段数**: 32

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| message_id | varchar(64) | NO | MUL |  |  |
| customer_id | varchar(64) | NO | MUL |  |  |
| customer_name | varchar(64) | NO |  |  |  |
| cust_code | varchar(64) | YES |  |  |  |
| project_code | varchar(64) | NO |  |  |  |
| project_name | varchar(64) | NO |  |  |  |
| content | varchar(256) | YES |  |  |  |
| description | text | YES |  |  |  |
| images | text | YES |  |  |  |
| ai_class_id | varchar(32) | NO |  |  |  |
| ai_class_name | varchar(64) | NO |  |  |  |
| request_class_id | varchar(32) | NO |  |  |  |
| request_class_name | varchar(64) | NO |  |  |  |
| robin_phone | varchar(16) | NO |  |  |  |
| phone | varchar(16) | NO |  |  |  |
| request_id | varchar(64) | NO |  |  |  |
| task_spec_id | varchar(32) | NO |  |  |  |
| status_code | varchar(16) | NO |  |  |  |
| status_name | varchar(32) | NO |  |  |  |
| last_status_code | varchar(16) | NO |  |  |  |
| last_status_name | varchar(32) | NO |  |  |  |
| target_system | tinyint(3) unsigned | NO |  | 0 |  |
| immediate_notification | tinyint(1) | YES |  | 1 |  |
| emergency_level | tinyint(3) unsigned | YES |  | 0 |  |
| address | varchar(255) | YES |  |  |  |
| satisfaction | varchar(12) | NO |  |  |  |
| unsatisfactory_reason | varchar(256) | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(3) unsigned | NO |  | 0 |  |

#### 样本数据
| id | user_id | message_id | customer_id | customer_name | cust_code | project_code | project_name | content | description | images | ai_class_id | ai_class_name | request_class_id | request_class_name | robin_phone | phone | request_id | task_spec_id | status_code | status_name | last_status_code | last_status_name | target_system | immediate_notification | emergency_level | address | satisfaction | unsatisfactory_reason | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 13385263619 |  | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... |  | NULL | 44030011 | 深圳金色家园 | 这是一个测试消息内容 | 这是一个测试消息描述。客户反馈房屋漏水需要修复 | NULL |  |  | 1001249 | AI告警 |  | 1881234123 | 451796125171056641 |  | 0 | 待审核 |  |  | 0 | 1 | 0 | NULL |  | NULL | Mon Aug 12 2024 20:24:04 GMT+0800 (中国标准时间) | Mon Aug 12 2024 20:24:04 GMT+0800 (中国标准时间) | 1 |
| 2 | 13385263619 |  | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... |  | NULL | 44030011 | 深圳金色家园 | 这是一个测试消息内容 | 这是一个测试消息描述。客户反馈房屋漏水需要修复 | NULL |  |  | 1001249 | AI告警 |  | 1881234123 | 451808953459802113 |  | 0 | 待审核 |  |  | 0 | 1 | 0 | NULL |  | NULL | Tue Aug 13 2024 09:59:40 GMT+0800 (中国标准时间) | Tue Aug 13 2024 09:59:40 GMT+0800 (中国标准时间) | 1 |
| 3 | 13385263619 |  | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... |  | NULL | 44030011 | 深圳金色家园 | 这是一个测试消息内容 | 这是一个测试消息描述。客户反馈房屋漏水需要修复 | NULL |  |  | 1001249 | AI告警 |  | 1881234123 | 451809030086066177 |  | 0 | 待审核 |  |  | 0 | 1 | 0 | NULL |  | NULL | Tue Aug 13 2024 10:04:32 GMT+0800 (中国标准时间) | Tue Aug 13 2024 10:04:32 GMT+0800 (中国标准时间) | 1 |

---

### chat_request_event

**记录数**: 1,833
**字段数**: 16

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| cr_id | bigint(20) unsigned | YES | MUL |  |  |
| biz_id | varchar(64) | NO | MUL |  |  |
| biz_sources | varchar(16) | NO |  |  |  |
| request_class_id | varchar(64) | NO |  |  |  |
| request_class | varchar(255) | NO |  |  |  |
| operation_code | varchar(64) | NO |  |  |  |
| operation_name | varchar(64) | NO |  |  |  |
| last_status_code | varchar(64) | NO |  |  |  |
| last_status_name | varchar(255) | NO |  |  |  |
| status_code | varchar(64) | NO |  |  |  |
| status_name | varchar(255) | NO |  |  |  |
| status_desc | varchar(255) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | cr_id | biz_id | biz_sources | request_class_id | request_class | operation_code | operation_name | last_status_code | last_status_name | status_code | status_name | status_desc | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 426 | 426 |  | FM | BUCR030112 |  |  |  |  |  | 0 | 待审核 | 智能客服创建事件 | Tue Nov 26 2024 17:21:48 GMT+0800 (中国标准时间) | Wed Nov 27 2024 20:30:04 GMT+0800 (中国标准时间) | 0 |
| 430 | 430 | B739vyWx5hN7vfsizxVBodvfTv | ioc | CCCL2000000156 |  |  |  |  |  | 0 | 待审核 | 智能客服创建事件 | Wed Nov 27 2024 11:17:38 GMT+0800 (中国标准时间) | Wed Nov 27 2024 20:30:06 GMT+0800 (中国标准时间) | 0 |
| 444 | 444 | 3FNNKnoTj8WaQy7wvhQfe7GiB | ioc | CCCL2000000769 |  |  |  |  |  | 0 | 待审核 | 智能客服创建事件 | Wed Nov 27 2024 20:38:00 GMT+0800 (中国标准时间) | Wed Nov 27 2024 20:38:32 GMT+0800 (中国标准时间) | 0 |

---

### chuan_class

**记录数**: 4,704
**字段数**: 51

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| spec_code | varchar(64) | NO | MUL |  |  |
| spec_id | varchar(64) | NO | MUL |  |  |
| code | varchar(64) | NO | MUL |  |  |
| bo_code | varchar(64) | NO |  |  |  |
| name | varchar(128) | NO |  |  |  |
| parent_id | varchar(64) | NO | MUL |  |  |
| path_code | varchar(256) | NO |  |  |  |
| path_name | varchar(512) | NO |  |  |  |
| due_with_duration | bigint(20) unsigned | YES |  |  |  |
| due_with_duration_unit | varchar(8) | YES |  |  |  |
| follow_up_duration | bigint(20) unsigned | YES |  |  |  |
| version | varchar(64) | NO |  |  |  |
| create_group | varchar(64) | NO |  |  |  |
| create_organization | varchar(64) | NO |  |  |  |
| create_time | datetime | YES |  |  |  |
| create_unit | varchar(64) | YES |  |  |  |
| create_user | varchar(64) | NO |  |  |  |
| old_spec_id | varchar(64) | NO |  |  |  |
| alias | varchar(64) | NO |  |  |  |
| description | varchar(255) | NO |  |  |  |
| remark | varchar(255) | YES |  |  |  |
| follow_up_duration_unit | varchar(8) | NO |  |  |  |
| form_code | varchar(64) | NO |  |  |  |
| form_name | varchar(64) | NO |  |  |  |
| geo_region_id | bigint(20) | YES |  |  |  |
| has_child_num | int(11) | YES |  |  |  |
| required | tinyint(1) | NO |  | 0 |  |
| required_response | tinyint(1) | NO |  | 0 |  |
| selectable | tinyint(1) | NO |  | 0 |  |
| location_id | bigint(20) | NO |  |  |  |
| mdm_code | varchar(64) | NO |  |  |  |
| property_id | bigint(20) | YES |  |  |  |
| priority_class_id | bigint(20) | YES |  |  |  |
| respond_with_duration | bigint(20) unsigned | YES |  |  |  |
| respond_with_duration_unit | varchar(8) | NO |  |  |  |
| seq_number | bigint(20) | YES |  |  |  |
| std_labor_time | bigint(20) unsigned | YES |  |  |  |
| std_labor_time_unit | varchar(8) | NO |  |  |  |
| revision | int(11) | YES |  |  |  |
| service_class_id | varchar(64) | NO |  |  |  |
| publish_datetime | datetime | YES |  |  |  |
| publish_people | varchar(64) | NO |  |  |  |
| tenant_id | bigint(20) | YES |  |  |  |
| data_status | tinyint(4) | NO |  | 0 |  |
| update_user | varchar(64) | NO |  |  |  |
| update_time | datetime | YES |  |  |  |
| cate_desc | varchar(500) | NO |  |  |  |
| delete_user | varchar(64) | NO |  |  |  |
| delete_time | datetime | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | spec_code | spec_id | code | bo_code | name | parent_id | path_code | path_name | due_with_duration | due_with_duration_unit | follow_up_duration | version | create_group | create_organization | create_time | create_unit | create_user | old_spec_id | alias | description | remark | follow_up_duration_unit | form_code | form_name | geo_region_id | has_child_num | required | required_response | selectable | location_id | mdm_code | property_id | priority_class_id | respond_with_duration | respond_with_duration_unit | seq_number | std_labor_time | std_labor_time_unit | revision | service_class_id | publish_datetime | publish_people | tenant_id | data_status | update_user | update_time | cate_desc | delete_user | delete_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 91000000208191 |  | 208191 | CCCL0000208191 | classification | 分类 | 0 | \CCCL0000208191 | \分类 | 0 |  | 0 | V155 |  |  | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) |  | 0 |  |  |  |  |  | triClassification | 分类 | -1 | 109 | 0 | 0 | 0 | -1 |  | -1 | -1 | 0 |  | 1 | 0 |  | -1 | -1 | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) | 0 | 77 | 0 | 0 | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) |  |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |
| 91000001584864 | 100 | 1584864 | CCCL0001584864 | classification | 请求类 | 91000000208191 | \CCCL0000208191\CCCL0001584864 | \分类\请求类 | 0 |  | 0 | V104 |  |  | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) |  | 91000000221931 |  |  |  |  |  | triRequestClass | 请求类 | -1 | 155 | 0 | 1 | 1 | -1 |  | -1 | -1 | 0 |  | -1 | 0 |  | -1 | -1 | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) | 91000000221931 | 77 | 0 | 91000000221931 | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) |  |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |
| 91000001585440 | 2 | 1585440 | CCCL0001585440 | classification | 优先级 | 91000000208191 | \CCCL0000208191\CCCL0001585440 | \分类\优先级 | 0 |  | 0 | V1 |  |  | Tue Feb 08 2005 09:46:33 GMT+0800 (中国标准时间) |  | 91000136602299 |  |  |  |  |  | triPriority | 优先级 | -1 | 10 | 0 | 0 | 0 | -1 |  | -1 | -1 | 0 |  | -1 | 0 |  | -1 | -1 | Tue Feb 08 2005 09:46:33 GMT+0800 (中国标准时间) | 91000136602299 | 77 | 0 | 91000136602299 | Tue Feb 08 2005 09:46:33 GMT+0800 (中国标准时间) |  |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |

---

### chuan_class_prod

**记录数**: 8,309
**字段数**: 50

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| spec_code | varchar(64) | NO | MUL |  |  |
| spec_id | varchar(64) | NO | MUL |  |  |
| code | varchar(64) | NO | MUL |  |  |
| bo_code | varchar(64) | NO |  |  |  |
| name | varchar(64) | NO |  |  |  |
| parent_id | varchar(64) | NO | MUL |  |  |
| path_code | varchar(256) | NO |  |  |  |
| path_name | varchar(512) | NO |  |  |  |
| due_with_duration | bigint(20) unsigned | YES |  |  |  |
| due_with_duration_unit | varchar(8) | YES |  |  |  |
| follow_up_duration | bigint(20) unsigned | YES |  |  |  |
| version | varchar(64) | NO |  |  |  |
| create_group | varchar(64) | NO |  |  |  |
| create_organization | varchar(64) | NO |  |  |  |
| create_time | datetime | YES |  |  |  |
| create_unit | varchar(64) | YES |  |  |  |
| create_user | varchar(64) | NO |  |  |  |
| old_spec_id | varchar(64) | NO |  |  |  |
| alias | varchar(64) | NO |  |  |  |
| description | varchar(255) | NO |  |  |  |
| remark | varchar(255) | YES |  |  |  |
| follow_up_duration_unit | varchar(8) | NO |  |  |  |
| form_code | varchar(64) | NO |  |  |  |
| form_name | varchar(64) | NO |  |  |  |
| geo_region_id | bigint(20) | YES |  |  |  |
| has_child_num | int(11) | YES |  |  |  |
| required | tinyint(1) | NO |  | 0 |  |
| required_response | tinyint(1) | NO |  | 0 |  |
| selectable | tinyint(1) | NO |  | 0 |  |
| location_id | bigint(20) | NO |  |  |  |
| mdm_code | varchar(64) | NO |  |  |  |
| property_id | bigint(20) | YES |  |  |  |
| priority_class_id | bigint(20) | YES |  |  |  |
| respond_with_duration | bigint(20) unsigned | YES |  |  |  |
| respond_with_duration_unit | varchar(8) | NO |  |  |  |
| seq_number | bigint(20) | YES |  |  |  |
| std_labor_time | bigint(20) unsigned | YES |  |  |  |
| std_labor_time_unit | varchar(8) | NO |  |  |  |
| revision | int(11) | YES |  |  |  |
| service_class_id | varchar(64) | NO |  |  |  |
| publish_datetime | datetime | YES |  |  |  |
| publish_people | varchar(64) | NO |  |  |  |
| tenant_id | bigint(20) | YES |  |  |  |
| data_status | tinyint(4) | NO |  | 0 |  |
| update_user | varchar(64) | NO |  |  |  |
| update_time | datetime | YES |  |  |  |
| delete_user | varchar(64) | NO |  |  |  |
| delete_time | datetime | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | spec_code | spec_id | code | bo_code | name | parent_id | path_code | path_name | due_with_duration | due_with_duration_unit | follow_up_duration | version | create_group | create_organization | create_time | create_unit | create_user | old_spec_id | alias | description | remark | follow_up_duration_unit | form_code | form_name | geo_region_id | has_child_num | required | required_response | selectable | location_id | mdm_code | property_id | priority_class_id | respond_with_duration | respond_with_duration_unit | seq_number | std_labor_time | std_labor_time_unit | revision | service_class_id | publish_datetime | publish_people | tenant_id | data_status | update_user | update_time | delete_user | delete_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 91000000208191 |  | 208191 | CCCL0000208191 | classification | 分类 | 0 | \CCCL0000208191 | \分类 | 0 |  | 0 | V77 |  |  | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) |  | 0 |  |  |  |  |  | triClassification | 分类 | -1 | 23 | 0 | 0 | 0 | -1 |  | -1 | -1 | 0 |  | -1 | 0 |  | -1 | -1 | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) | 0 | 7 | 0 | 0 | Sun Oct 27 2002 13:01:25 GMT+0800 (中国标准时间) |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |
| 91000001584395 | 1 | 1584395 | CCCL0001584395 | classification | 状态 | 91000000208191 | \CCCL0000208191\CCCL0001584395 | \分类\状态 | 0 |  | 0 | V1 |  |  | Tue Jan 25 2005 11:05:58 GMT+0800 (中国标准时间) |  | 0 |  |  |  |  |  | triStatus | 状态 | -1 | 1 | 0 | 0 | 0 | -1 |  | -1 | -1 | 0 |  | -1 | 0 |  | -1 | -1 | Tue Jun 07 2022 01:42:28 GMT+0800 (中国标准时间) | 0 | 7 | 0 | 0 | Tue Jan 25 2005 11:05:58 GMT+0800 (中国标准时间) |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |
| 91000001584864 | 100 | 1584864 | CCCL0001584864 | classification | 请求类 | 91000000208191 | \CCCL0000208191\CCCL0001584864 | \分类\请求类 | 0 |  | 0 | V221 |  |  | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) |  | 142015185 |  |  |  |  |  | triRequestClass | 请求类 | -1 | 158 | 0 | 1 | 0 | -1 |  | -1 | -1 | 0 |  | -1 | 0 |  | -1 | -1 | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) | 142015185 | 7 | 0 | 142015185 | Mon Jan 31 2005 15:31:16 GMT+0800 (中国标准时间) |  | Wed Dec 31 1969 16:00:00 GMT+0800 (中国标准时间) | 0 |

---

### daily_message_stats

**记录数**: 1
**字段数**: 3

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| message_date | date | YES |  |  |  |
| message_count | bigint(21) | NO |  | 0 |  |
| active_users | bigint(21) | NO |  | 0 |  |

#### 样本数据
| message_date | message_count | active_users |
|---|---|---|
| Mon Jan 01 2024 00:00:00 GMT+0800 (中国标准时间) | 10 | 5 |

---

### fm_business_types

**记录数**: 96
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| business_code | varchar(128) | NO | PRI |  |  |
| id | int(11) | YES |  |  |  |
| business_name | varchar(128) | YES |  |  |  |
| business_level | int(11) | YES |  |  |  |
| parent_code | varchar(128) | NO | PRI |  |  |
| parent_id | int(11) | YES |  |  |  |
| label_type | varchar(128) | YES |  |  |  |
| label_type_name | varchar(10) | YES |  |  |  |
| cate_desc | varchar(200) | NO |  |  |  |
| is_deleted | tinyint(1) | YES |  | 0 |  |

#### 样本数据
| business_code | id | business_name | business_level | parent_code | parent_id | label_type | label_type_name | cate_desc | is_deleted |
|---|---|---|---|---|---|---|---|---|---|
| BUCR010115 | 3506 | 电动车进楼 | 3 | BUCR0301 | 3156 |  | NULL |  | 0 |
| BUCR02 | 3023 | 住户相关报事 | 1 | 0 | 0 | NULL | NULL |  | 0 |
| BUCR0201 | 3025 | 物业增值服务 | 2 | BUCR02 | 3023 | 4 | 管家 |  | 0 |

---

### kdb_main_question

**记录数**: 900,915
**字段数**: 20

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(10) unsigned | NO | PRI |  | auto_increment |
| knowledge_id | int(10) unsigned | NO | MUL | 0 |  |
| kdb_code | varchar(32) | NO |  |  |  |
| project_code | varchar(32) | NO | MUL |  |  |
| tree_id | int(11) | NO | MUL |  |  |
| question | varchar(200) | NO |  |  |  |
| answer | text | NO |  |  |  |
| answer_type | tinyint(1) | NO |  |  |  |
| valid_type | tinyint(1) | NO |  |  |  |
| valid_start | datetime | YES |  |  |  |
| valid_end | datetime | YES | MUL |  |  |
| knowledge_status | tinyint(3) unsigned | NO |  | 0 |  |
| audit_status | tinyint(3) unsigned | NO |  | 0 |  |
| deleted | tinyint(3) unsigned | NO |  | 0 |  |
| create_time | datetime | NO |  |  |  |
| update_time | datetime | NO | MUL |  | on update CURRENT_TIMESTAMP |
| create_user | varchar(32) | NO |  |  |  |
| update_user | varchar(32) | NO |  |  |  |
| creator_id | varchar(32) | NO |  |  |  |
| update_id | varchar(32) | NO |  |  |  |

#### 样本数据
| id | knowledge_id | kdb_code | project_code | tree_id | question | answer | answer_type | valid_type | valid_start | valid_end | knowledge_status | audit_status | deleted | create_time | update_time | create_user | update_user | creator_id | update_id |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 1 | project | 44030021 | 3 | 高迪的管家电话 | 五月茶13684961201 | 0 | 0 | NULL | NULL | 0 | 0 | 0 | Thu Aug 04 2022 17:37:35 GMT+0800 (中国标准时间) | Mon Sep 18 2023 22:02:06 GMT+0800 (中国标准时间) | 胡琪琦 | 胡琪琦 | 1171759 | 1171759 |
| 2 | 2 | project | 44030021 | 3 | 米罗的管家电话 | 五月茶13684961201 | 0 | 0 | NULL | NULL | 0 | 0 | 0 | Thu Aug 04 2022 17:37:35 GMT+0800 (中国标准时间) | Mon Sep 18 2023 22:02:06 GMT+0800 (中国标准时间) | 胡琪琦 | 胡琪琦 | 1171759 | 1171759 |
| 3 | 3 | project | 44030021 | 3 | 达利的管家电话 | 五月茶13684961201 | 0 | 0 | NULL | NULL | 0 | 0 | 0 | Thu Aug 04 2022 17:37:35 GMT+0800 (中国标准时间) | Mon Sep 18 2023 22:02:06 GMT+0800 (中国标准时间) | 胡琪琦 | 胡琪琦 | 1171759 | 1171759 |

---

### message_extend

**记录数**: 0
**字段数**: 18

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| question_suggestion | json | YES |  |  |  |
| knowledge_ids | json | YES |  |  |  |
| result_type | varchar(32) | YES |  | 0 |  |
| request_params | json | YES |  |  |  |
| token_total | int(11) | YES |  | 0 |  |
| response_time | int(11) | YES |  |  |  |
| voice_url | varchar(200) | YES |  |  |  |
| create_time | datetime | YES |  |  |  |
| mark_status | int(11) | YES |  | 0 |  |
| mislabeling_type | json | YES |  |  |  |
| review_mark_user | varchar(150) | YES |  |  |  |
| review_mark_time | datetime | YES |  |  |  |
| mark_plugin_ids | json | YES |  |  |  |
| context_status | int(11) | YES |  | 0 |  |
| reset_marker | int(11) | YES |  | 0 |  |
| intention_much_wrong | int(11) | YES |  | 0 |  |
| answer_wrong | int(11) | YES |  | 0 |  |
| mark_correct | int(11) | YES |  | 0 |  |

---

### out_user

**记录数**: 16
**字段数**: 17

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| name | varchar(100) | YES |  |  |  |
| user_id | varchar(150) | NO |  |  |  |
| mobile | varchar(15) | YES |  |  |  |
| email | varchar(100) | YES |  |  |  |
| corpid | varchar(100) | YES |  |  |  |
| gender | int(11) | YES |  |  |  |
| avatar | varchar(200) | YES |  |  |  |
| open_id | varchar(100) | NO | MUL |  |  |
| one_id | varchar(100) | NO |  |  |  |
| external_user_id | varchar(100) | NO |  |  |  |
| channel | varchar(32) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| first_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| last_time | datetime | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

#### 样本数据
| id | name | user_id | mobile | email | corpid | gender | avatar | open_id | one_id | external_user_id | channel | deleted | first_time | last_time | create_time | update_time |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 8 | NULL | ovdt3jmFKwLxvs16AL3myfO2Uo1U | 15056288365 | NULL | wxc74aa677d1e5b619 | NULL | NULL | o0QOp7WzCuqksBf5qeUatwW0ZDI0 | 861413978852827198 | wm0fChEAAAyJsiC71Ea7a2O4LUEgJy... | boen_mini_app | 1 | Tue May 20 2025 17:06:26 GMT+0800 (中国标准时间) | Tue May 20 2025 17:06:26 GMT+0800 (中国标准时间) | Tue May 20 2025 17:06:26 GMT+0800 (中国标准时间) | Fri May 23 2025 18:33:07 GMT+0800 (中国标准时间) |
| 11 | NULL | ovdt3jqieIofl1wV9GUj7E71euJM | 18827513332 | NULL | wxc74aa677d1e5b619 | NULL | NULL | o0QOp7f88f3fJY7NiqSpmzvmOwzE | 861414086467670037 | wm0fChEAAAjXoVR5FbvEsvOT1WigIE... | boen_mini_app | 1 | Wed May 21 2025 11:05:55 GMT+0800 (中国标准时间) | Wed May 21 2025 11:06:03 GMT+0800 (中国标准时间) | Wed May 21 2025 11:06:05 GMT+0800 (中国标准时间) | Fri May 23 2025 18:33:07 GMT+0800 (中国标准时间) |
| 13 | NULL | ovdt3jrhcnRN314f9uqC97SWmflo | 15573112223 | NULL | wxc74aa677d1e5b619 | NULL | NULL | o0QOp7WOE9LIO3_P0NcIPzZjuZdI | 861414049639043146 |  | boen_mini_app | 1 | Wed May 21 2025 14:55:14 GMT+0800 (中国标准时间) | Wed May 21 2025 14:55:14 GMT+0800 (中国标准时间) | Wed May 21 2025 14:55:14 GMT+0800 (中国标准时间) | Fri May 23 2025 18:33:07 GMT+0800 (中国标准时间) |

---

### prompt_notice

**记录数**: 23
**字段数**: 12

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| title | varchar(100) | NO |  |  |  |
| content | varchar(1000) | NO |  |  |  |
| effect_begin | datetime | NO |  |  |  |
| effect_end | datetime | NO |  |  |  |
| summary | varchar(500) | YES |  |  |  |
| wecom_user_id | varchar(64) | NO | MUL |  |  |
| project_code | varchar(64) | NO | MUL |  |  |
| type | int(11) | YES |  |  |  |
| create_time | datetime | YES |  |  |  |
| update_time | datetime | YES |  |  | on update CURRENT_TIMESTAMP |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | title | content | effect_begin | effect_end | summary | wecom_user_id | project_code | type | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|
| 9 | 停电通知 |  尊敬的尚华家园业主好！因高压电线故障！ 涉及停电楼层：17... | Fri Oct 11 2024 14:40:00 GMT+0800 (中国标准时间) | Mon Nov 11 2024 17:00:00 GMT+0800 (中国标准时间) | 停电公告
范围：尚华家园17层-28层
原因：高压电线故障
... | 19969266626 | 44030011 | 1 | Fri Nov 08 2024 10:08:24 GMT+0800 (中国标准时间) | Mon Nov 11 2024 17:05:59 GMT+0800 (中国标准时间) | 1 |
| 10 | 小区又有部分楼栋停水了 | 大家请提前准备好，因政府道路施工，小区的1栋至5栋水管需停水... | Mon Nov 11 2024 11:44:00 GMT+0800 (中国标准时间) | Mon Nov 11 2024 11:48:00 GMT+0800 (中国标准时间) | 范围：小区的1栋至5栋。
原因：政府道路施工。 | luoqi | 44030011 | 1 | Mon Nov 11 2024 10:19:02 GMT+0800 (中国标准时间) | Mon Nov 11 2024 17:05:59 GMT+0800 (中国标准时间) | 1 |
| 11 | 预缴物业费 | 提前预缴一年物业费，可以有礼品赠送！！now@@@@ | Thu Nov 14 2024 16:18:00 GMT+0800 (中国标准时间) | Sat Nov 23 2024 14:27:00 GMT+0800 (中国标准时间) | 标题：提前预缴一年物业费有礼品赠送
活动内容：业主提前预缴一... | luoqi | 44030011 | 3 | Mon Nov 11 2024 11:05:47 GMT+0800 (中国标准时间) | Tue Nov 12 2024 10:12:26 GMT+0800 (中国标准时间) | 1 |

---

### rm_role_config

**记录数**: 4
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| rm_role_code | varchar(50) | NO | UNI |  |  |
| rm_role_name | varchar(100) | NO |  |  |  |
| walrus_role_id | bigint(20) | NO | MUL |  |  |
| org_type | tinyint(4) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| create_by | varchar(50) | YES |  |  |  |
| update_by | varchar(50) | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | rm_role_code | rm_role_name | walrus_role_id | org_type | create_time | update_time | create_by | update_by | deleted |
|---|---|---|---|---|---|---|---|---|---|
| 1 | PROFESSIONAL_MANAGER_HOUSEKEEP... | 网格管家 | 37 | 1 | Wed Jun 18 2025 15:04:11 GMT+0800 (中国标准时间) | Wed Jun 18 2025 15:04:11 GMT+0800 (中国标准时间) | NULL | NULL | 0 |
| 2 | FACTORY_MANAGER | 驻场经理 | 36 | 1 | Wed Jun 18 2025 15:04:31 GMT+0800 (中国标准时间) | Wed Jun 18 2025 15:04:56 GMT+0800 (中国标准时间) | NULL | NULL | 0 |
| 3 | DEPARTMENT_DOCKING_PARTNER | 项目总监 | 36 | 1 | Wed Jun 18 2025 15:04:55 GMT+0800 (中国标准时间) | Wed Jun 18 2025 15:04:55 GMT+0800 (中国标准时间) | NULL | NULL | 0 |

---

### sys_active_org

**记录数**: 139
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| parent_id | varchar(64) | NO | MUL |  |  |
| org_level | int(11) | NO | MUL | 0 |  |
| org_name | varchar(64) | NO |  |  |  |
| org_id | varchar(64) | NO | MUL |  |  |
| create_by | varchar(64) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | NO |  |  |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | parent_id | org_level | org_name | org_id | create_by | create_time | update_by | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|
| 1 |  | 0 | 所有组织 | 0 | huangjianhua | Thu Sep 26 2024 22:20:21 GMT+0800 (中国标准时间) |  | Thu Sep 26 2024 22:20:21 GMT+0800 (中国标准时间) | 0 |
| 2 | G57 | 2 | 郑州万科城金兰苑 | 41010046 | 0 | Fri Sep 06 2024 10:41:11 GMT+0800 (中国标准时间) |  | Fri Sep 06 2024 10:41:11 GMT+0800 (中国标准时间) | 0 |
| 3 | 41010046 | 3 | 郑州万科城金兰苑网格A | 41010046A |  | Fri Sep 06 2024 10:41:47 GMT+0800 (中国标准时间) |  | Fri Sep 06 2024 10:41:47 GMT+0800 (中国标准时间) | 0 |

---

### sys_agent

**记录数**: 63
**字段数**: 10

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| agent_id | varchar(64) | YES | MUL |  |  |
| bot_name | varchar(64) | YES |  |  |  |
| app_id | varchar(64) | YES |  |  |  |
| secret_key | text | YES |  |  |  |
| plugins | varchar(256) | YES |  |  |  |
| mode | tinyint(3) unsigned | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | agent_id | bot_name | app_id | secret_key | plugins | mode | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|
| 1 | 65040842e4b0c00be86743ac | 物业管家助理 | 64f98fe5e4b02de8759b2795 | 8T4ULjpc8ernYVDS:EZRJ2uosuQZqf... | NULL | 0 | Fri Mar 15 2024 12:46:29 GMT+0800 (中国标准时间) | Thu Sep 12 2024 16:54:00 GMT+0800 (中国标准时间) | 1 |
| 3 | 660b755be4b0f0259d438183 | 阳光物业Bot | 660b755be4b0f0259d438183 | 3DtbFN/SiTx7wof3:SO7yDOG6IcXU/... | NULL | 0 | Tue Apr 02 2024 12:42:51 GMT+0800 (中国标准时间) | Thu Sep 12 2024 18:44:50 GMT+0800 (中国标准时间) | 1 |
| 5 | 66c42c33e4b0f433b8226655 | 会话总结 | 66c42c33e4b0f433b8226655 | yzXpq4IhEdl/+/q2:qnlmnGQ4n+7NV... | NULL | 0 | Tue Aug 20 2024 14:48:09 GMT+0800 (中国标准时间) | Tue Jun 10 2025 10:20:56 GMT+0800 (中国标准时间) | 0 |

---

### sys_app_config

**记录数**: 4
**字段数**: 16

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| appid | varchar(20) | YES |  |  |  |
| secret | varchar(100) | YES |  |  |  |
| encoding_aes_key | varchar(200) | YES |  |  |  |
| type | varchar(1) | YES |  |  |  |
| ip_limit | json | YES |  |  |  |
| agent_id | varchar(20) | YES |  |  |  |
| corp_id | varchar(50) | YES |  |  |  |
| robot_id | int(11) | YES |  |  |  |
| name | varchar(100) | YES |  |  |  |
| open_kf_id | varchar(100) | YES |  |  |  |
| app_type | varchar(100) | YES |  |  |  |
| permanent_code | varchar(100) | YES |  |  |  |
| update_time | datetime | YES |  |  | on update CURRENT_TIMESTAMP |
| token | varchar(100) | YES |  | helloheyi110 |  |
| encrypt_model | tinyint(1) | YES |  | 0 |  |

#### 样本数据
| id | appid | secret | encoding_aes_key | type | ip_limit | agent_id | corp_id | robot_id | name | open_kf_id | app_type | permanent_code | update_time | token | encrypt_model |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1204 | 1000003 | 7RWmoQtOx7izIC5JwWJsvTRT0_OUjF... | jRDKtTG3wkJqezYw7RCTjQag5De9QW... | 4 | NULL | NULL | ww6ba14fdeabddb037 | 377 | vx客服测试 | wkowLtZQAAeCVFV94xFB_VfbfuV9Pt... | NULL | NULL | Thu Aug 01 2024 17:33:50 GMT+0800 (中国标准时间) | IppNFh6CmmKqDV76RsGMle | 0 |
| 1205 | 706912037567743121 | NULL | NULL | NULL | NULL | 1000165 | ww8e1afaee8085d9e9 | NULL | 智能物业管家 | NULL | oneWo | NULL | Fri Aug 02 2024 15:25:38 GMT+0800 (中国标准时间) | helloheyi110 | 0 |
| 1206 | 748821688420914233 | NULL | D9nxWvCPFe1Gw2uFrJpGYAXcqpWsd8... | NULL | NULL | 1000509 | ww55f24aac66843e9e | NULL | 小可AI | wk0fChEAAAzkrPyIeol9u5twVZyXkL... | oneWo | NULL | Wed Nov 06 2024 11:27:46 GMT+0800 (中国标准时间) | GiJK8hXIw87BomO | 0 |

---

### sys_bot

**记录数**: 72
**字段数**: 34

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| im_bot_id | varchar(64) | YES | MUL |  |  |
| wecom_user_id | varchar(64) | YES | MUL |  |  |
| grid_code | varchar(32) | NO | MUL |  |  |
| grid_name | varchar(64) | NO |  |  |  |
| project_code | varchar(32) | NO |  |  |  |
| project_name | varchar(64) | NO |  |  |  |
| name | varchar(64) | YES |  |  |  |
| bot_id | varchar(64) | YES |  |  |  |
| bot_corp_id | varchar(64) | YES |  |  |  |
| org_corp_id | varchar(64) | YES |  |  |  |
| org_id | varchar(64) | YES |  |  |  |
| partner_org_id | varchar(64) | YES |  |  |  |
| status | int(11) | YES |  |  |  |
| type | int(11) | YES |  |  |  |
| auto_resp_status | tinyint(1) | NO |  | 0 |  |
| auto_resp_time | varchar(128) | NO |  |  |  |
| served_scope | tinyint(4) | NO |  | 0 |  |
| served_user_type | varchar(32) | NO |  | 1 |  |
| served_scenes | varchar(16) | NO |  | 1,2 |  |
| avatar | text | YES |  |  |  |
| group_id | varchar(64) | YES |  |  |  |
| group_name | varchar(64) | YES |  |  |  |
| msg_resp_delay | int(10) unsigned | NO |  | 0 |  |
| wx_kf_id | varchar(32) | NO |  |  |  |
| wx_kf_name | varchar(64) | NO |  |  |  |
| wx_kf_url | text | YES |  |  |  |
| wx_kf_url_short | varchar(64) | YES |  |  |  |
| real_name | varchar(64) | YES |  |  |  |
| phone | varchar(20) | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| disable | tinyint(1) | NO |  | 0 |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | im_bot_id | wecom_user_id | grid_code | grid_name | project_code | project_name | name | bot_id | bot_corp_id | org_corp_id | org_id | partner_org_id | status | type | auto_resp_status | auto_resp_time | served_scope | served_user_type | served_scenes | avatar | group_id | group_name | msg_resp_delay | wx_kf_id | wx_kf_name | wx_kf_url | wx_kf_url_short | real_name | phone | create_time | update_time | disable | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 13 | bf8acbd6ecbe0610d451dde4f51d8e... | Fan | 44030011C | 深圳金色家园网格C | 44030011 | 深圳金色家园 | 芦荟的AI助手 | bf8acbd6ecbe0610d451dde4f51d8e... | NULL | NULL | NULL | NULL | 2 | 2 | 0 |  | 0 | 1 | 1,2 | https://wework.qpic.cn/wwpic/1... | NULL | NULL | 0 | wk0fChEAAAzkrPyIeol9u5twVZyXkL... | 小可AI | https://work.weixin.qq.com/kfi... | https://vk8.co/iwmfLeicbj | 苹果(王能樊) | 13266741564 | Tue Aug 20 2024 10:33:49 GMT+0800 (中国标准时间) | Fri Jun 06 2025 09:16:52 GMT+0800 (中国标准时间) | 0 | 0 |
| 26 | 4567c90a51d7789590340e6f7edab5... | aqhiIii | 44030011A | 武汉数字运营中心项目测试专用网格B | 42010169 | 武汉数字运营中心项目测试专用 | 桑葚的AI助手 | 4567c90a51d7789590340e6f7edab5... | NULL | NULL | NULL | NULL | 2 | 2 | 0 |  | 0 | 1 | 1,2 | https://wework.qpic.cn/bizmail... | NULL | NULL | 0 | wk0fChEAAAzkrPyIeol9u5twVZyXkL... | 小可AI | https://work.weixin.qq.com/kfi... | https://vk8.co/Ybm1Bc9Aaj | 桑葚(王志远) | 18827513332 | Mon Sep 23 2024 10:14:27 GMT+0800 (中国标准时间) | Fri Jun 06 2025 09:16:52 GMT+0800 (中国标准时间) | 0 | 0 |
| 30 | ea7c55479e3310266e4f9f054cc3f7... | huangjianhua | 44030011A | 深圳金色家园网格A | 44030011 | 深圳金色家园 | 黄健华的AI助手 | ea7c55479e3310266e4f9f054cc3f7... | NULL | NULL | NULL | NULL | 2 | 2 | 0 |  | 0 | 1 | 1,2 | https://wework.qpic.cn/wwpic3a... | NULL | NULL | 0 | wk0fChEAAAzkrPyIeol9u5twVZyXkL... | 小可AI | https://work.weixin.qq.com/kfi... | https://vk8.co/c1l69YXdbj | 黄健华 | 15817128123 | Fri Sep 27 2024 18:48:16 GMT+0800 (中国标准时间) | Fri Jun 06 2025 09:16:52 GMT+0800 (中国标准时间) | 0 | 0 |

---

### sys_bot_agent_rel

**记录数**: 92
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| bot_id | varchar(64) | YES | MUL |  |  |
| agent_id | varchar(64) | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | bot_id | agent_id | create_time | update_time | deleted |
|---|---|---|---|---|---|
| 1 | 65eff157a2e175c60c892ff8 | 669f80ffe4b07d2a32b30b03 | Mon Mar 18 2024 19:36:55 GMT+0800 (中国标准时间) | Tue Aug 13 2024 16:06:17 GMT+0800 (中国标准时间) | 0 |
| 2 | 660b7024d1368fae1f9a7df5 | 669f80ffe4b07d2a32b30b03 | Tue Apr 02 2024 13:38:29 GMT+0800 (中国标准时间) | Tue Aug 13 2024 16:06:17 GMT+0800 (中国标准时间) | 0 |
| 3 | 668fe074496ee0c5d905f027 | 669f80ffe4b07d2a32b30b03 | Tue Jul 23 2024 14:49:30 GMT+0800 (中国标准时间) | Tue Jul 23 2024 18:10:57 GMT+0800 (中国标准时间) | 0 |

---

### sys_bot_config

**记录数**: 265
**字段数**: 12

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| bot_id | varchar(64) | YES | MUL |  |  |
| defaulted | tinyint(1) | NO |  | 1 |  |
| msg_type | varchar(10) | YES |  |  |  |
| daily_msg | text | YES |  |  |  |
| daily_group_msg | text | YES |  |  |  |
| notice_msg_template | text | YES |  |  |  |
| wx_bot_id | varchar(64) | NO |  |  |  |
| service_hotline | text | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

#### 样本数据
| id | bot_id | defaulted | msg_type | daily_msg | daily_group_msg | notice_msg_template | wx_bot_id | service_hotline | deleted | create_time | update_time |
|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 1688857689478191 | 0 | text | [{"messageType":7,"payload":{"... | [{"messageType":7,"payload":{"... | [{"imBotId":"{imBotId}",    "i... | a04cf73d-cece-43dd-adec-cbcdf2... | NULL | 0 | Fri Mar 15 2024 15:23:41 GMT+0800 (中国标准时间) | Fri Nov 15 2024 15:44:30 GMT+0800 (中国标准时间) |
| 2 | 1688857178640228 | 0 | text | [{"messageType":7,"payload":{"... | [{"messageType":7,"payload":{"... | [{"imBotId":"{imBotId}",    "i... | a04cf73d-cece-43dd-adec-cbcdf2... | NULL | 0 | Fri Mar 15 2024 15:23:41 GMT+0800 (中国标准时间) | Fri Nov 15 2024 15:44:30 GMT+0800 (中国标准时间) |
| 3 | bf8acbd6ecbe0610d451dde4f51d8e... | 0 | text | [{"messageType":7,"payload":{"... | [{"messageType":7,"payload":{"... | [{"imBotId":"{imBotId}",    "i... | a04cf73d-cece-43dd-adec-cbcdf2... | 010-123456212121212121 | 0 | Mon Sep 02 2024 10:58:05 GMT+0800 (中国标准时间) | Thu Apr 17 2025 16:09:51 GMT+0800 (中国标准时间) |

---

### sys_bot_scenario

**记录数**: 2
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| scenario_code | varchar(16) | NO |  |  |  |
| scenario_name | varchar(32) | NO |  |  |  |
| scenario_desc | varchar(200) | NO |  |  |  |
| msg_template | text | YES |  |  |  |
| timing | tinyint(3) unsigned | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | scenario_code | scenario_name | scenario_desc | msg_template | timing | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|
| 1 | scenario_1 | 暂离 | 用于暂时离开，短时间内无法及时回复业主信息的场景。 | [{"messageType":7,"payload":{"... | 0 | Tue Jun 18 2024 15:44:19 GMT+0800 (中国标准时间) | Tue Jun 18 2024 15:44:19 GMT+0800 (中国标准时间) | 0 |
| 3 | scenario_2 | 休假 | 用于休假期间，较长时间内无法回复业主信息的场景。 | [{"messageType":7,"payload":{"... | 1 | Tue Jun 18 2024 15:47:55 GMT+0800 (中国标准时间) | Tue Jun 18 2024 15:47:55 GMT+0800 (中国标准时间) | 0 |

---

### sys_bot_status

**记录数**: 82
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(32) | NO | MUL |  |  |
| user_status | varchar(32) | NO | MUL |  |  |
| start_time | datetime | NO |  |  |  |
| end_time | datetime | YES |  |  |  |
| status_type | tinyint(3) unsigned | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | user_id | user_status | start_time | end_time | status_type | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|
| 1 | 13385263619 | scenario_1 | Thu Jun 20 2024 10:34:32 GMT+0800 (中国标准时间) | Fri Jun 21 2024 10:40:35 GMT+0800 (中国标准时间) | 0 | Thu Jun 20 2024 10:34:39 GMT+0800 (中国标准时间) | Thu Jun 20 2024 10:34:43 GMT+0800 (中国标准时间) | 0 |
| 2 | 13385263619 | scenario_2 | Thu Jun 20 2024 09:35:13 GMT+0800 (中国标准时间) | Thu Jun 20 2024 09:38:20 GMT+0800 (中国标准时间) | 0 | Thu Jun 20 2024 10:35:34 GMT+0800 (中国标准时间) | Thu Jun 20 2024 10:35:34 GMT+0800 (中国标准时间) | 0 |
| 3 | 00837187 | scenario_1 | Sun Jun 16 2024 10:35:53 GMT+0800 (中国标准时间) | Wed Jun 19 2024 10:35:58 GMT+0800 (中国标准时间) | 0 | Thu Jun 20 2024 10:36:03 GMT+0800 (中国标准时间) | Thu Jun 20 2024 10:36:03 GMT+0800 (中国标准时间) | 0 |

---

### sys_dict

**记录数**: 57
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| type | varchar(255) | YES |  |  |  |
| code | varchar(255) | YES | MUL |  |  |
| value | varchar(1500) | YES |  |  |  |
| description | varchar(1024) | YES |  |  |  |
| create_time | timestamp | YES |  |  | on update CURRENT_TIMESTAMP |
| update_time | timestamp | YES |  |  | on update CURRENT_TIMESTAMP |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | type | code | value | description | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|
| 1 | project_biz | water | 送水 | NULL | Tue Jul 30 2024 15:20:21 GMT+0800 (中国标准时间) | Tue Jul 30 2024 15:20:21 GMT+0800 (中国标准时间) | 0 |
| 2 | chat_ai_label | long | 回复太长 | NULL | Thu Aug 15 2024 15:15:08 GMT+0800 (中国标准时间) | Thu Aug 15 2024 15:15:08 GMT+0800 (中国标准时间) | 0 |
| 3 | chat_ai_label | error | 回复错误 | NULL | Thu Aug 15 2024 15:15:43 GMT+0800 (中国标准时间) | Thu Aug 15 2024 15:15:43 GMT+0800 (中国标准时间) | 0 |

---

### sys_download_task

**记录数**: 252
**字段数**: 13

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| task_id | varchar(64) | NO | MUL |  |  |
| file_name | varchar(128) | NO |  |  |  |
| file_path | varchar(255) | NO |  |  |  |
| file_size | bigint(20) unsigned | NO |  | 0 |  |
| status | varchar(16) | YES |  |  |  |
| params | longtext | YES |  |  |  |
| function_name | varchar(64) | NO |  |  |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_user_id | varchar(64) | NO |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | task_id | file_name | file_path | file_size | status | params | function_name | create_user_id | update_user_id | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | ad657316db12f183832da4a2942ea7... | 对话管理_对话记录_20240910114603.xlsx | walrus/test/download/20240910/... | 9445 | COMPLETED | {} | exportChatRequest | aqhiIii | system | Tue Sep 10 2024 11:46:03 GMT+0800 (中国标准时间) | Tue Sep 10 2024 12:38:22 GMT+0800 (中国标准时间) | 0 |
| 5 | d63e25a900d832507ab831fe73774b... | 事件管理_事件记录_20240910145355.xlsx | walrus/test/download/20240910/... | 6740 | COMPLETED | {} | exportChatRequest | aqhiIii | system | Tue Sep 10 2024 14:53:55 GMT+0800 (中国标准时间) | Tue Sep 10 2024 14:54:04 GMT+0800 (中国标准时间) | 0 |
| 6 | ac4811cfcaf64981b0b3fbf19e8fd5... | 事件管理_事件记录_20240910150534.xlsx | walrus/test/download/20240910/... | 6738 | COMPLETED | {} | exportChatRequest | aqhiIii | system | Tue Sep 10 2024 15:05:35 GMT+0800 (中国标准时间) | Tue Sep 10 2024 15:05:41 GMT+0800 (中国标准时间) | 0 |

---

### sys_knowledge

**记录数**: 913
**字段数**: 16

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| knowledge_id | varchar(64) | NO | MUL |  |  |
| template_id | bigint(20) unsigned | YES | MUL |  |  |
| project_code | varchar(32) | NO | MUL |  |  |
| project_name | varchar(64) | NO |  |  |  |
| question | varchar(255) | NO |  |  |  |
| answer | text | YES |  |  |  |
| answer_reference | text | YES |  |  |  |
| audit_id | bigint(20) unsigned | YES | MUL |  |  |
| knowledge_status | int(11) | NO |  | 0 |  |
| gc_status | tinyint(4) | NO |  | 0 |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | knowledge_id | template_id | project_code | project_name | question | answer | answer_reference | audit_id | knowledge_status | gc_status | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 847 | 560d71510fc5486abe64430326fac4... | 72 | 44030011 | 深圳金色家园 | 安装充电桩、安装空调需要备案吗，要提交哪些资料？ | 3212 | NULL | 268 | 1 | 1 | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) |  | Wed Apr 09 2025 11:56:15 GMT+0800 (中国标准时间) | ZengXiang | 1 |
| 848 | e1e5ee06a36e4f4c83ec63d8a1d2cd... | 71 | 44030011 | 深圳金色家园 | 装修备案时需要缴纳哪些费用？ | 需缴纳装修押金及装修垃圾清运费 | NULL | 252 | 1 | 1 | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) |  | Fri Nov 01 2024 16:04:38 GMT+0800 (中国标准时间) | luoqi | 1 |
| 849 | f34374285f274ec896527d702d9d91... | 70 | 44030011 | 深圳金色家园 | 局部改造是否需要装修备案？ | 不需要。具体咨询管理处 | NULL | 253 | 1 | 1 | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) |  | Fri Nov 01 2024 16:04:43 GMT+0800 (中国标准时间) | luoqi | 1 |

---

### sys_knowledge_audit

**记录数**: 20
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| project_code | varchar(32) | NO | MUL |  |  |
| project_name | varchar(64) | NO |  |  |  |
| audit_status | tinyint(1) | NO |  | 1 |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | project_code | project_name | audit_status | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|
| 156 | 44030011 | 深圳金色家园 | 1 | Fri Nov 01 2024 10:26:52 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:26:52 GMT+0800 (中国标准时间) | luoqi | 0 |
| 157 | 44030347 | 深圳万科都会四季花园 | 1 | Fri Nov 01 2024 13:52:49 GMT+0800 (中国标准时间) | ZengXiang | Fri Nov 01 2024 13:52:49 GMT+0800 (中国标准时间) | ZengXiang | 0 |
| 158 | 44030011 | 深圳金色家园 | 1 | Fri Nov 01 2024 13:54:08 GMT+0800 (中国标准时间) | ZengXiang | Fri Nov 01 2024 13:54:08 GMT+0800 (中国标准时间) | ZengXiang | 0 |

---

### sys_knowledge_audit_detail

**记录数**: 35
**字段数**: 14

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| audit_id | bigint(20) unsigned | YES | MUL |  |  |
| knowledge_id | varchar(32) | NO | MUL |  |  |
| project_code | varchar(32) | NO | MUL |  |  |
| project_name | varchar(32) | NO |  |  |  |
| question | varchar(255) | NO |  |  |  |
| answer | text | YES |  |  |  |
| reason | text | YES |  |  |  |
| audit_status | tinyint(4) | NO |  | 0 |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | audit_id | knowledge_id | project_code | project_name | question | answer | reason | audit_status | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 240 | 156 | f34374285f274ec896527d702d9d91... | 44030011 | 深圳金色家园 | 局部改造是否需要装修备案？ | 不需要 |  | 1 | Fri Nov 01 2024 10:24:16 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:27:04 GMT+0800 (中国标准时间) | luoqi | 0 |
| 241 | 156 | cc25675851ef4e60bd0f54b72ca9f3... | 44030011 | 深圳金色家园 | 可以封阳台吗？ | 可以封阳台，涉及到噪音施工不能在周末进行 |  | 1 | Fri Nov 01 2024 10:24:27 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:27:04 GMT+0800 (中国标准时间) | luoqi | 0 |
| 242 | 156 | e1e5ee06a36e4f4c83ec63d8a1d2cd... | 44030011 | 深圳金色家园 | 装修备案时需要缴纳哪些费用？ | 装修押金及装修垃圾清运费 |  | 1 | Fri Nov 01 2024 10:24:31 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:27:04 GMT+0800 (中国标准时间) | luoqi | 0 |

---

### sys_knowledge_category

**记录数**: 11
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| category_name | varchar(32) | NO |  |  |  |
| data_status | tinyint(1) | NO |  | 1 |  |
| rank | int(10) unsigned | NO | MUL | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | category_name | data_status | rank | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|
| 1 | 装修-装修备案 | 1 | 1 | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) |  | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) | YeXueFen | 0 |
| 2 | 装修-装修管理规定 | 1 | 2 | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) |  | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) | YeXueFen | 0 |
| 3 | 车辆-车位管理 | 1 | 3 | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) |  | Tue Oct 15 2024 12:01:58 GMT+0800 (中国标准时间) | YeXueFen | 0 |

---

### sys_knowledge_template

**记录数**: 117
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| question | varchar(255) | YES |  |  |  |
| category_id | bigint(20) unsigned | YES |  |  |  |
| data_type | varchar(16) | NO |  |  |  |
| data_status | tinyint(1) | NO |  | 1 |  |
| answer_reference | text | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | question | category_id | data_type | data_status | answer_reference | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|---|---|
| 68 | 装修备案需要本人办理吗？去哪里办理？ | 1 | knowledge | 1 | 装修备案需要业主或委托人办理；前往幸福驿站自助机、住这儿AP... | Fri Nov 01 2024 09:57:40 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 09:57:40 GMT+0800 (中国标准时间) | luoqi | 0 |
| 69 | 装修备案可以线上办理吗？如何办理？装修备案需要带什么材料？ | 1 | knowledge | 1 | 可以线上办理，在住这儿APP装修登记入口进入按照操作完成填写... | Fri Nov 01 2024 10:01:50 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:01:50 GMT+0800 (中国标准时间) | luoqi | 0 |
| 70 | 局部改造是否需要装修备案？ | 1 | knowledge | 1 | 需要/不需要 | Fri Nov 01 2024 10:02:11 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:02:11 GMT+0800 (中国标准时间) | luoqi | 0 |

---

### sys_project_config

**记录数**: 60
**字段数**: 13

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| project_code | varchar(32) | NO | MUL |  |  |
| project_name | varchar(64) | NO |  |  |  |
| audit_required | tinyint(4) | NO |  | 0 |  |
| service_hotline | text | YES |  |  |  |
| target_system | tinyint(4) | YES |  | -1 |  |
| wx_bot_id | varchar(64) | NO |  |  |  |
| collaborator_agent_id | varchar(64) | NO |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_user_id | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user_id | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | project_code | project_name | audit_required | service_hotline | target_system | wx_bot_id | collaborator_agent_id | create_time | create_user_id | update_time | update_user_id | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 56 | 44030011 | 深圳金色家园 | 1 | 010-123456212121212121 | 3 | a04cf73d-cece-43dd-adec-cbcdf2... | 680f40bbe4b0c67a9830f7e0 | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) | luoqi | 0 |
| 57 | 44030347 | 深圳万科都会四季花园 | 1 | 080-12345678 | 1 |  |  | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:15:20 GMT+0800 (中国标准时间) | ZengXiang | 0 |
| 58 | 42010050 | 武汉万科汉口传奇唐樾 | 0 | NULL | 1 |  |  | Fri Nov 01 2024 10:19:27 GMT+0800 (中国标准时间) | luoqi | Fri Nov 01 2024 10:19:27 GMT+0800 (中国标准时间) | luoqi | 0 |

---

### sys_role

**记录数**: 15
**字段数**: 11

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| role_name | varchar(32) | NO |  |  |  |
| role_desc | varchar(500) | NO |  |  |  |
| parent_id | int(11) | NO |  | 0 |  |
| sort | int(11) | YES |  |  |  |
| status | tinyint(2) | NO |  | 1 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| create_by | varchar(64) | YES |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | YES |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | role_name | role_desc | parent_id | sort | status | create_time | create_by | update_time | update_by | deleted |
|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 超级管理员 | 超级管理员 | 0 | NULL | 1 | Mon May 27 2024 14:03:12 GMT+0800 (中国标准时间) |  | Mon May 27 2024 14:03:12 GMT+0800 (中国标准时间) |  | 0 |
| 32 | 普通管理员 | 二级 | 1 | 12 | 1 | Wed Sep 25 2024 15:41:55 GMT+0800 (中国标准时间) | luoqi | Wed Sep 25 2024 17:21:17 GMT+0800 (中国标准时间) | luoqi | 0 |
| 33 | 总部运营 | 总部 | 32 | 1 | 1 | Wed Sep 25 2024 15:50:28 GMT+0800 (中国标准时间) | luoqi | Tue Oct 15 2024 16:39:08 GMT+0800 (中国标准时间) | 18588983145 | 0 |

---

### sys_role_route

**记录数**: 905
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| role_id | bigint(20) | NO | MUL | 0 |  |
| route_id | bigint(20) | NO |  | 0 |  |
| create_by | varchar(64) | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | YES |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | role_id | route_id | create_by | create_time | update_by | update_time | deleted |
|---|---|---|---|---|---|---|---|
| 1 | 2 | 1 |  | Wed Sep 04 2024 11:18:40 GMT+0800 (中国标准时间) |  | Wed Sep 04 2024 11:19:11 GMT+0800 (中国标准时间) | 1 |
| 2 | 2 | 2 |  | Wed Sep 04 2024 11:18:40 GMT+0800 (中国标准时间) |  | Wed Sep 04 2024 11:19:11 GMT+0800 (中国标准时间) | 1 |
| 3 | 2 | 1 |  | Wed Sep 04 2024 11:24:07 GMT+0800 (中国标准时间) |  | Wed Sep 04 2024 11:24:37 GMT+0800 (中国标准时间) | 1 |

---

### sys_route

**记录数**: 56
**字段数**: 14

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| order_no | int(11) | NO |  | 0 |  |
| parent_id | bigint(20) | NO | MUL | 0 |  |
| icon | varchar(64) | NO |  |  |  |
| route_name | varchar(64) | NO |  |  |  |
| route_url | varchar(100) | NO |  |  |  |
| route_type | tinyint(4) | NO |  | 0 |  |
| route_status | tinyint(4) | NO |  | 1 |  |
| route_desc | varchar(500) | NO |  |  |  |
| create_by | varchar(64) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | NO |  |  |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | order_no | parent_id | icon | route_name | route_url | route_type | route_status | route_desc | create_by | create_time | update_by | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 1 | 0 | AliyunOutlined | 对话管理 | /chatRecords | 1 | 1 | 对话管理列表 | LiZhiPeng | Wed Sep 04 2024 10:17:13 GMT+0800 (中国标准时间) | Fan | Tue Dec 10 2024 19:26:03 GMT+0800 (中国标准时间) | 0 |
| 3 | 1005 | 0 | SettingOutlined | 权限管理 | /accessManage | 1 | 1 | 权限管理 | Fan | Thu Sep 05 2024 18:01:32 GMT+0800 (中国标准时间) | Fan | Mon Dec 30 2024 17:04:54 GMT+0800 (中国标准时间) | 0 |
| 4 | 1 | 3 | ArrowsAltOutlined | 功能管理 | /accessManage/menuList | 1 | 1 | 功能管理 | Fan | Fri Sep 06 2024 11:03:29 GMT+0800 (中国标准时间) | LiZhiPeng | Sat Sep 14 2024 15:18:16 GMT+0800 (中国标准时间) | 0 |

---

### sys_user

**记录数**: 66
**字段数**: 20

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| full_name | varchar(64) | NO |  |  |  |
| user_type | tinyint(1) | NO |  | 1 |  |
| gender | tinyint(4) | NO |  | 0 |  |
| mobile | varchar(32) | NO |  |  |  |
| avatar | text | YES |  |  |  |
| qr_code | text | YES |  |  |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| create_by | varchar(64) | NO |  |  |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| company_code | varchar(32) | YES |  |  |  |
| company_name | varchar(32) | YES |  |  |  |
| project | varchar(32) | NO |  |  |  |
| project_name | varchar(32) | NO |  |  |  |
| grid | varchar(32) | NO |  |  |  |
| grid_name | varchar(32) | NO |  |  |  |
| edit_tag | int(1) | YES |  | 1 |  |

#### 样本数据
| id | user_id | full_name | user_type | gender | mobile | avatar | qr_code | create_time | create_by | update_time | update_by | deleted | company_code | company_name | project | project_name | grid | grid_name | edit_tag |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 7 | luoqi | 罗祺 | 0 | 0 | 13728696572 | NULL | NULL | Thu Jun 12 2025 16:44:23 GMT+0800 (中国标准时间) | 0 | Thu Jun 12 2025 16:44:23 GMT+0800 (中国标准时间) | mazheng | 0 |  |  |  |  |  |  | 1 |
| 82 | huangjianhua | 黄健华 | 2 | 0 | 15817128123 | https://wework.qpic.cn/wwpic3a... | NULL | Wed Jun 11 2025 19:45:18 GMT+0800 (中国标准时间) | luoqi | Wed Jun 11 2025 19:45:18 GMT+0800 (中国标准时间) | luoqi | 0 | A05 | A53深圳第三分公司 | 44030011 | 深圳金色家园 | 44030011A | 深圳金色家园网格A | 1 |
| 83 | Fan | 苹果(王能樊) | 2 | 0 | 13266741564 | https://wework.qpic.cn/wwpic/1... | NULL | Mon May 19 2025 16:19:06 GMT+0800 (中国标准时间) | luoqi | Mon May 19 2025 16:21:37 GMT+0800 (中国标准时间) | luoqi | 0 | A05 | A53深圳第三分公司 | 44030011 | 深圳金色家园 | 44030011A | 深圳金色家园网格A | 1 |

---

### sys_user_org

**记录数**: 67
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| org_id | varchar(64) | NO | MUL |  |  |
| org_name | varchar(64) | NO |  |  |  |
| create_by | varchar(64) | NO |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_by | varchar(64) | NO |  |  |  |
| update_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | user_id | org_id | org_name | create_by | create_time | update_by | update_time | deleted |
|---|---|---|---|---|---|---|---|---|
| 1688 | 00788049 | 0 | 所有组织 | luoqi | Fri Sep 27 2024 17:50:34 GMT+0800 (中国标准时间) |  | Fri Sep 27 2024 17:50:34 GMT+0800 (中国标准时间) | 0 |
| 1689 | luoy108 | 44030011 | 深圳金色家园 | luoqi | Fri Sep 27 2024 17:54:54 GMT+0800 (中国标准时间) |  | Fri Sep 27 2024 17:54:54 GMT+0800 (中国标准时间) | 0 |
| 1713 | 00841897 | 44030011 | 深圳金色家园 | aqhiIii | Fri Sep 27 2024 18:28:28 GMT+0800 (中国标准时间) |  | Fri Sep 27 2024 18:28:28 GMT+0800 (中国标准时间) | 0 |

---

### sys_user_role

**记录数**: 126
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| role_id | bigint(20) unsigned | NO | MUL |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | user_id | role_id | create_time | update_time | deleted |
|---|---|---|---|---|---|
| 14 | luoqi | 1 | Thu Sep 19 2024 10:09:50 GMT+0800 (中国标准时间) | Thu Sep 19 2024 10:09:50 GMT+0800 (中国标准时间) | 0 |
| 82 | luoqi | 3 | Tue Sep 24 2024 17:36:26 GMT+0800 (中国标准时间) | Tue Sep 24 2024 17:36:26 GMT+0800 (中国标准时间) | 1 |
| 231 | aqhiIii | 32 | Fri Sep 27 2024 17:37:15 GMT+0800 (中国标准时间) | Fri Sep 27 2024 17:37:15 GMT+0800 (中国标准时间) | 1 |

---

### sys_visit

**记录数**: 0
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(64) | NO | MUL |  |  |
| page_id | varchar(64) | NO | MUL |  |  |
| visit_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| date | date | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |

---

### sys_visit_statistics

**记录数**: 3,985
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| page_id | varchar(64) | NO | MUL |  |  |
| date | date | YES |  |  |  |
| pv | int(10) unsigned | NO |  | 0 |  |
| uv | int(10) unsigned | NO |  | 0 |  |
| create_time | datetime | NO | MUL | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | page_id | date | pv | uv | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|
| 13 | 19867730103 | Tue Aug 27 2024 00:00:00 GMT+0800 (中国标准时间) | 90 | 90 | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | 0 |
| 14 | luoqi | Tue Aug 27 2024 00:00:00 GMT+0800 (中国标准时间) | 0 | 0 | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | 0 |
| 15 | Fan | Tue Aug 27 2024 00:00:00 GMT+0800 (中国标准时间) | 446 | 90 | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | Tue Aug 27 2024 16:34:37 GMT+0800 (中国标准时间) | 0 |

---

### user_activity_stats

**记录数**: 5
**字段数**: 5

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | varchar(36) | NO |  |  |  |
| username | varchar(100) | NO |  |  |  |
| message_count | bigint(21) | NO |  | 0 |  |
| last_message_time | timestamp | YES |  |  |  |
| first_message_time | timestamp | YES |  |  |  |

#### 样本数据
| id | username | message_count | last_message_time | first_message_time |
|---|---|---|---|---|
| user-001 | alice | 2 | Mon Jan 01 2024 10:25:00 GMT+0800 (中国标准时间) | Mon Jan 01 2024 10:00:00 GMT+0800 (中国标准时间) |
| user-002 | bob | 2 | Mon Jan 01 2024 10:30:00 GMT+0800 (中国标准时间) | Mon Jan 01 2024 10:05:00 GMT+0800 (中国标准时间) |
| user-003 | charlie | 2 | Mon Jan 01 2024 10:35:00 GMT+0800 (中国标准时间) | Mon Jan 01 2024 10:10:00 GMT+0800 (中国标准时间) |

---

### user_opinion

**记录数**: 17
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| user_id | varchar(100) | NO | MUL |  |  |
| opinion | text | NO |  |  |  |
| images | text | YES |  |  |  |
| channel | varchar(32) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

#### 样本数据
| id | user_id | opinion | images | channel | deleted | create_time | update_time |
|---|---|---|---|---|---|---|---|
| 1 | ovdt3jp0X9Bp-HBwzX87d8D31gFo | 666666 | 111,222 | boen_mini_app | 0 | Thu May 01 2025 14:43:38 GMT+0800 (中国标准时间) | Fri May 23 2025 18:20:55 GMT+0800 (中国标准时间) |
| 2 | ovdt3jp0X9Bp-HBwzX87d8D31gFo | 7777 | 333,444 | boen_mini_app | 0 | Mon May 19 2025 15:06:05 GMT+0800 (中国标准时间) | Fri May 23 2025 18:20:55 GMT+0800 (中国标准时间) |
| 3 | ovdt3jp0X9Bp-HBwzX87d8D31gFo | 7777 | https://grandline-prod.oss-cn-... | boen_mini_app | 0 | Mon May 19 2025 16:04:06 GMT+0800 (中国标准时间) | Fri May 23 2025 18:20:55 GMT+0800 (中国标准时间) |

---

### users

**记录数**: 5
**字段数**: 4

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | varchar(36) | NO | PRI |  |  |
| username | varchar(100) | NO | UNI |  |  |
| email | varchar(255) | YES |  |  |  |
| created_at | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |

#### 样本数据
| id | username | email | created_at |
|---|---|---|---|
| user-001 | alice | <EMAIL> | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |
| user-002 | bob | <EMAIL> | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |
| user-003 | charlie | <EMAIL> | Thu Jun 12 2025 10:06:15 GMT+0800 (中国标准时间) |

---

### wechat_app_config

**记录数**: 1
**字段数**: 7

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| app_id | varchar(64) | NO |  |  |  |
| app_secret | varchar(64) | NO |  |  |  |
| channel | varchar(32) | NO | UNI |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

#### 样本数据
| id | app_id | app_secret | channel | deleted | create_time | update_time |
|---|---|---|---|---|---|---|
| 1 | wxc74aa677d1e5b619 | 6cfafd8806d435cb1aa645fe0bf3b6... | boen_mini_app | 0 | Tue May 13 2025 17:36:02 GMT+0800 (中国标准时间) | Tue May 13 2025 17:36:02 GMT+0800 (中国标准时间) |

---

### work_class

**记录数**: 385
**字段数**: 9

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| code | varchar(64) | NO | MUL |  |  |
| project_code | varchar(32) | NO | MUL |  |  |
| source | tinyint(1) | NO |  | 1 |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| create_user | varchar(64) | NO |  |  |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_user | varchar(64) | NO |  |  |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | code | project_code | source | create_time | create_user | update_time | update_user | deleted |
|---|---|---|---|---|---|---|---|---|
| 814 | BUCR010115 | 44030036 | 2 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | 0 |
| 815 | BUCR02 | 44030036 | 2 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | 0 |
| 816 | BUCR0201 | 44030036 | 2 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | Tue Nov 26 2024 17:43:28 GMT+0800 (中国标准时间) | 149886691 | 0 |

---

### work_order

**记录数**: 5
**字段数**: 20

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| related_work_order_id | bigint(20) | YES |  |  |  |
| processing_result | varchar(255) | YES |  |  |  |
| source | varchar(255) | YES |  |  |  |
| visit_time | varchar(255) | YES |  |  |  |
| project_name | varchar(255) | YES |  |  |  |
| building_number | varchar(255) | YES |  |  |  |
| house_number | varchar(255) | YES |  |  |  |
| project_alias | varchar(255) | YES |  |  |  |
| notes | text | YES |  |  |  |
| task_type | enum('praise','complaint') | YES |  |  |  |
| priority | enum('normal','urgent') | YES |  |  |  |
| description | varchar(255) | YES |  |  |  |
| title | varchar(255) | YES |  |  |  |
| link | varchar(255) | YES |  |  |  |
| created_at | datetime | NO |  | CURRENT_TIMESTAMP |  |
| created_by | varchar(64) | YES |  |  |  |
| updated_at | datetime | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| updated_by | varchar(64) | YES |  |  |  |
| is_deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | related_work_order_id | processing_result | source | visit_time | project_name | building_number | house_number | project_alias | notes | task_type | priority | description | title | link | created_at | created_by | updated_at | updated_by | is_deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1000001 | NULL | 已处理 | NULL | NULL | 深圳金色花园一期 | 2 | 2301 | NULL | 2024/1/1 12:30已处理完毕，当日下午四点前小区内... | complaint | normal | 一楼垃圾桶堆得满满当当，臭的要死，好几天没有清理，元旦节就没... | 垃圾清理不及时 | NULL | Tue Oct 29 2024 13:46:08 GMT+0800 (中国标准时间) | NULL | Tue Oct 29 2024 13:48:56 GMT+0800 (中国标准时间) | NULL | 0 |
| 1000002 | NULL | 进行中 | NULL | NULL | 深圳金色花园一期 | 2 | 2301 | NULL | 其他业主买车没买车位，物业没有权利不让进车库，难以管理，联系... | complaint | normal | 小区车库里的车辆停放毫无秩序。好多车不停车位上，随便停在过道... | 车库车辆乱停，出入不方便 | NULL | Tue Oct 29 2024 13:49:26 GMT+0800 (中国标准时间) | NULL | Tue Oct 29 2024 13:50:06 GMT+0800 (中国标准时间) | NULL | 0 |
| 1000003 | NULL | 已处理 | NULL | NULL | 深圳金色花园一期 | 2 | 2301 | NULL | 已表扬对应管家 | praise | normal | 回来的时候车钥匙丢了，不知道丢在哪里，去4s店补个钥匙又要几... | 表扬管家张三找到车钥匙 | NULL | Tue Oct 29 2024 13:50:28 GMT+0800 (中国标准时间) | NULL | Tue Oct 29 2024 13:51:02 GMT+0800 (中国标准时间) | NULL | 0 |

---

### wx_mini_prgm_config

**记录数**: 1
**字段数**: 12

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) unsigned | NO | PRI |  | auto_increment |
| page_id | varchar(32) | NO | MUL |  |  |
| app_id | varchar(32) | YES | MUL |  |  |
| app_name | varchar(32) | NO |  |  |  |
| title | varchar(64) | NO |  |  |  |
| page_path | text | YES |  |  |  |
| thumb_url | text | YES |  |  |  |
| icon_url | text | YES |  |  |  |
| description | text | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| deleted | tinyint(1) | NO |  | 0 |  |

#### 样本数据
| id | page_id | app_id | app_name | title | page_path | thumb_url | icon_url | description | create_time | update_time | deleted |
|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 14c794de521143559d0de2234ab3f4... | wxe0f6c61fa1c120d5 | 万物云物业服务 | 物业费账单 | /pages/ConfirmBill/index.html | https://coordinator.onewo.com/... | https://img2.4009515151.com/co... | NULL | Fri Nov 08 2024 14:03:08 GMT+0800 (中国标准时间) | Fri Nov 08 2024 14:03:08 GMT+0800 (中国标准时间) | 0 |

---

### wxkf_cursor

**记录数**: 3
**字段数**: 8

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| kf_cursor | varchar(100) | YES |  |  |  |
| open_kfid | varchar(100) | YES |  |  |  |
| name | varchar(100) | YES |  |  |  |
| avatar | varchar(300) | YES |  |  |  |
| config_id | int(11) | YES |  |  |  |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP |  |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP |  |

#### 样本数据
| id | kf_cursor | open_kfid | name | avatar | config_id | create_time | update_time |
|---|---|---|---|---|---|---|---|
| 1 | HHFq49uYHoiCuZRv9NLs | wkowLtZQAAeCVFV94xFB_VfbfuV9Pt... | NULL | NULL | NULL | Mon Aug 12 2024 11:16:14 GMT+0800 (中国标准时间) | Mon Aug 12 2024 11:16:14 GMT+0800 (中国标准时间) |
| 2 | 2ErkT6vJu7BAsYz8yaU1pj | wk0fChEAAAzkrPyIeol9u5twVZyXkL... | 小可AI | https://wework.qpic.cn/wwpic3a... | NULL | Mon Aug 12 2024 11:16:14 GMT+0800 (中国标准时间) | Mon Aug 12 2024 11:16:14 GMT+0800 (中国标准时间) |
| 4 | NULL | wk0fChEAAAEMPikBn5fRzbqnG6XSwn... | 智能客服管家 | https://wework.qpic.cn/wwpic3a... | NULL | Tue Sep 10 2024 15:15:35 GMT+0800 (中国标准时间) | Tue Sep 10 2024 15:15:35 GMT+0800 (中国标准时间) |

---

### wxkf_msg

**记录数**: 12,615
**字段数**: 5

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| msg_id | varchar(100) | YES | MUL |  |  |
| msg_record_id | bigint(20) | YES |  |  |  |
| state | int(11) | YES |  | 1 |  |
| message_id | varchar(100) | YES |  |  |  |

#### 样本数据
| id | msg_id | msg_record_id | state | message_id |
|---|---|---|---|---|
| 1 | 7cfe6ef11ed0b29694ca7b83f9d21d... | NULL | 1 | NULL |
| 2 | cfd640155ae248c9ad994031c6dbf0... | NULL | 1 | NULL |
| 3 | 6e118bfd4f9951aa064fa20b0c49f0... | NULL | 1 | e541a09dab994594a011d84e81ed62... |

---

### xxl_job_group

**记录数**: 2
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| app_name | varchar(64) | NO |  |  |  |
| title | varchar(12) | NO |  |  |  |
| address_type | tinyint(4) | NO |  | 0 |  |
| address_list | text | YES |  |  |  |
| update_time | datetime | YES |  |  |  |

#### 样本数据
| id | app_name | title | address_type | address_list | update_time |
|---|---|---|---|---|---|
| 1 | xxl-job-executor-sample | 示例执行器 | 0 | NULL | Thu Jun 19 2025 21:44:39 GMT+0800 (中国标准时间) |
| 2 | walrus-test | walrus-test | 0 | http://**************:9999/ | Thu Jun 19 2025 21:44:39 GMT+0800 (中国标准时间) |

---

### xxl_job_info

**记录数**: 30
**字段数**: 24

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| job_group | int(11) | NO |  |  |  |
| job_desc | varchar(255) | NO |  |  |  |
| add_time | datetime | YES |  |  |  |
| update_time | datetime | YES |  |  |  |
| author | varchar(64) | YES |  |  |  |
| alarm_email | varchar(255) | YES |  |  |  |
| schedule_type | varchar(50) | NO |  | NONE |  |
| schedule_conf | varchar(128) | YES |  |  |  |
| misfire_strategy | varchar(50) | NO |  | DO_NOTHING |  |
| executor_route_strategy | varchar(50) | YES |  |  |  |
| executor_handler | varchar(255) | YES |  |  |  |
| executor_param | varchar(512) | YES |  |  |  |
| executor_block_strategy | varchar(50) | YES |  |  |  |
| executor_timeout | int(11) | NO |  | 0 |  |
| executor_fail_retry_count | int(11) | NO |  | 0 |  |
| glue_type | varchar(50) | NO |  |  |  |
| glue_source | mediumtext | YES |  |  |  |
| glue_remark | varchar(128) | YES |  |  |  |
| glue_updatetime | datetime | YES |  |  |  |
| child_jobid | varchar(255) | YES |  |  |  |
| trigger_status | tinyint(4) | NO |  | 0 |  |
| trigger_last_time | bigint(13) | NO |  | 0 |  |
| trigger_next_time | bigint(13) | NO |  | 0 |  |

#### 样本数据
| id | job_group | job_desc | add_time | update_time | author | alarm_email | schedule_type | schedule_conf | misfire_strategy | executor_route_strategy | executor_handler | executor_param | executor_block_strategy | executor_timeout | executor_fail_retry_count | glue_type | glue_source | glue_remark | glue_updatetime | child_jobid | trigger_status | trigger_last_time | trigger_next_time |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | 1 | 测试任务1 | Sat Nov 03 2018 22:21:31 GMT+0800 (中国标准时间) | Sat Nov 03 2018 22:21:31 GMT+0800 (中国标准时间) | XXL |  | CRON | 0 0 0 * * ? * | DO_NOTHING | FIRST | demoJobHandler |  | SERIAL_EXECUTION | 0 | 0 | BEAN |  | GLUE代码初始化 | Sat Nov 03 2018 22:21:31 GMT+0800 (中国标准时间) |  | 0 | 0 | 0 |
| 2 | 2 | 同步百川工单类型 | Thu Aug 22 2024 15:16:47 GMT+0800 (中国标准时间) | Tue Aug 27 2024 15:42:46 GMT+0800 (中国标准时间) | 王志远 |  | CRON | 0 0 3 * * ? | DO_NOTHING | FIRST | syncChuanClassJob |  | SERIAL_EXECUTION | 0 | 0 | BEAN |  | GLUE代码初始化 | Thu Aug 22 2024 15:16:47 GMT+0800 (中国标准时间) |  | 1 | 1750273200000 | 1750359600000 |
| 3 | 2 | 客服聊天日报 | Thu Aug 22 2024 15:17:29 GMT+0800 (中国标准时间) | Tue Aug 27 2024 15:42:43 GMT+0800 (中国标准时间) | 王志远 |  | CRON | 0 0 9 * * ? | DO_NOTHING | FIRST | dailyReportNoticeJob |  | SERIAL_EXECUTION | 0 | 0 | BEAN |  | GLUE代码初始化 | Thu Aug 22 2024 15:17:29 GMT+0800 (中国标准时间) |  | 1 | 1750294800000 | 1750381200000 |

---

### xxl_job_lock

**记录数**: 1
**字段数**: 1

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| lock_name | varchar(50) | NO | PRI |  |  |

#### 样本数据
| lock_name |
|---|
| schedule_lock |

---

### xxl_job_log

**记录数**: 3,959,350
**字段数**: 15

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | bigint(20) | NO | PRI |  | auto_increment |
| job_group | int(11) | NO |  |  |  |
| job_id | int(11) | NO |  |  |  |
| executor_address | varchar(255) | YES |  |  |  |
| executor_handler | varchar(255) | YES |  |  |  |
| executor_param | varchar(512) | YES |  |  |  |
| executor_sharding_param | varchar(20) | YES |  |  |  |
| executor_fail_retry_count | int(11) | NO |  | 0 |  |
| trigger_time | datetime | YES | MUL |  |  |
| trigger_code | int(11) | NO |  |  |  |
| trigger_msg | text | YES |  |  |  |
| handle_time | datetime | YES |  |  |  |
| handle_code | int(11) | NO | MUL |  |  |
| handle_msg | text | YES |  |  |  |
| alarm_status | tinyint(4) | NO |  | 0 |  |

#### 样本数据
| id | job_group | job_id | executor_address | executor_handler | executor_param | executor_sharding_param | executor_fail_retry_count | trigger_time | trigger_code | trigger_msg | handle_time | handle_code | handle_msg | alarm_status |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 27784794 | 2 | 7 | http://**************:9999/ | wxBotMsgServiceJob |  | NULL | 0 | Tue May 20 2025 00:00:01 GMT+0800 (中国标准时间) | 200 | 任务触发类型：Cron触发<br>调度机器：192.168.... | Tue May 20 2025 00:00:01 GMT+0800 (中国标准时间) | 200 |  | 0 |
| 27784795 | 2 | 7 | http://**************:9999/ | wxBotMsgServiceJob |  | NULL | 0 | Tue May 20 2025 00:00:02 GMT+0800 (中国标准时间) | 200 | 任务触发类型：Cron触发<br>调度机器：192.168.... | Tue May 20 2025 00:00:02 GMT+0800 (中国标准时间) | 200 |  | 0 |
| 27784796 | 2 | 7 | http://**************:9999/ | wxBotMsgServiceJob |  | NULL | 0 | Tue May 20 2025 00:00:03 GMT+0800 (中国标准时间) | 200 | 任务触发类型：Cron触发<br>调度机器：192.168.... | Tue May 20 2025 00:00:03 GMT+0800 (中国标准时间) | 200 |  | 0 |

---

### xxl_job_log_report

**记录数**: 304
**字段数**: 6

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| trigger_day | datetime | YES | UNI |  |  |
| running_count | int(11) | NO |  | 0 |  |
| suc_count | int(11) | NO |  | 0 |  |
| fail_count | int(11) | NO |  | 0 |  |
| update_time | datetime | YES |  |  |  |

#### 样本数据
| id | trigger_day | running_count | suc_count | fail_count | update_time |
|---|---|---|---|---|---|
| 1 | Thu Aug 22 2024 00:00:00 GMT+0800 (中国标准时间) | 0 | 1 | 0 | NULL |
| 2 | Wed Aug 21 2024 00:00:00 GMT+0800 (中国标准时间) | 0 | 0 | 0 | NULL |
| 3 | Tue Aug 20 2024 00:00:00 GMT+0800 (中国标准时间) | 0 | 0 | 0 | NULL |

---

### xxl_job_logglue

**记录数**: 0
**字段数**: 7

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| job_id | int(11) | NO |  |  |  |
| glue_type | varchar(50) | YES |  |  |  |
| glue_source | mediumtext | YES |  |  |  |
| glue_remark | varchar(128) | NO |  |  |  |
| add_time | datetime | YES |  |  |  |
| update_time | datetime | YES |  |  |  |

---

### xxl_job_registry

**记录数**: 1
**字段数**: 5

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| registry_group | varchar(50) | NO | MUL |  |  |
| registry_key | varchar(255) | NO |  |  |  |
| registry_value | varchar(255) | NO |  |  |  |
| update_time | datetime | YES |  |  |  |

#### 样本数据
| id | registry_group | registry_key | registry_value | update_time |
|---|---|---|---|---|
| 1193 | EXECUTOR | walrus-test | http://**************:9999/ | Thu Jun 19 2025 21:44:52 GMT+0800 (中国标准时间) |

---

### xxl_job_user

**记录数**: 5
**字段数**: 5

#### 字段结构
| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |
|--------|------|----------|-----|--------|----------|
| id | int(11) | NO | PRI |  | auto_increment |
| username | varchar(50) | NO | UNI |  |  |
| password | varchar(50) | NO |  |  |  |
| role | tinyint(4) | NO |  |  |  |
| permission | varchar(255) | YES |  |  |  |

#### 样本数据
| id | username | password | role | permission |
|---|---|---|---|---|
| 1 | admin | 54f4d659737b0ea5264e2adefa3bf8... | 1 | NULL |
| 2 | lizp44 | 60162fa786ce8943bfdd8e87ebdf82... | 1 |  |
| 4 | luoq38 | c865bc13bdb95ab20b532051368dc2... | 0 | 2 |

---

