const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// MySQL数据库连接配置
const DB_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'root',
  password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
  database: 'walrus',
  timezone: '+08:00',
  charset: 'utf8mb4',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  multipleStatements: true
};

// 创建数据库连接
async function createMySQLConnection() {
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ MySQL数据库连接成功');
    return connection;
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error);
    throw error;
  }
}

// 获取所有表名
async function getAllTables(connection) {
  try {
    const [rows] = await connection.execute('SHOW TABLES');
    return rows;
  } catch (error) {
    console.error('获取表列表失败:', error);
    throw error;
  }
}

// 获取表结构
async function getTableStructure(connection, tableName) {
  try {
    const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
    const [indexes] = await connection.execute(`SHOW INDEX FROM ${tableName}`);
    const [createTable] = await connection.execute(`SHOW CREATE TABLE ${tableName}`);
    
    return {
      tableName,
      columns,
      indexes,
      createTable
    };
  } catch (error) {
    console.error(`获取表 ${tableName} 结构失败:`, error);
    throw error;
  }
}

// 获取表的样本数据
async function getTableSample(connection, tableName, limit = 5) {
  try {
    const [rows] = await connection.execute(`SELECT * FROM ${tableName} LIMIT ${limit}`);
    return rows;
  } catch (error) {
    console.error(`获取表 ${tableName} 样本数据失败:`, error);
    throw error;
  }
}

// 获取表的记录数
async function getTableCount(connection, tableName) {
  try {
    const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
    return rows[0].count;
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error);
    return 0;
  }
}

// 生成Markdown格式的数据库分析报告
function generateMarkdownReport(analysisResult) {
  let markdown = `# MySQL数据库 'walrus' 结构分析报告\n\n`;
  markdown += `**分析时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n`;
  markdown += `**数据库**: walrus\n`;
  markdown += `**总表数**: ${analysisResult.tableCount}\n\n`;
  
  markdown += `## 📊 数据库概览\n\n`;
  markdown += `| 表名 | 记录数 | 字段数 | 主要用途推测 |\n`;
  markdown += `|------|--------|--------|----------------|\n`;
  
  analysisResult.tables.forEach(table => {
    const purpose = guessPurpose(table.name, table.columns);
    markdown += `| ${table.name} | ${table.recordCount.toLocaleString()} | ${table.columnCount} | ${purpose} |\n`;
  });
  
  markdown += `\n## 🗂️ 详细表结构\n\n`;
  
  analysisResult.tables.forEach(table => {
    markdown += `### ${table.name}\n\n`;
    markdown += `**记录数**: ${table.recordCount.toLocaleString()}\n`;
    markdown += `**字段数**: ${table.columnCount}\n\n`;
    
    markdown += `#### 字段结构\n`;
    markdown += `| 字段名 | 类型 | 允许NULL | 键 | 默认值 | 额外信息 |\n`;
    markdown += `|--------|------|----------|-----|--------|----------|\n`;
    
    table.columns.forEach(col => {
      markdown += `| ${col.Field} | ${col.Type} | ${col.Null} | ${col.Key || ''} | ${col.Default || ''} | ${col.Extra || ''} |\n`;
    });
    
    if (table.sampleData && table.sampleData.length > 0) {
      markdown += `\n#### 样本数据\n`;
      const firstRow = table.sampleData[0];
      const headers = Object.keys(firstRow);
      
      markdown += `| ${headers.join(' | ')} |\n`;
      markdown += `|${headers.map(() => '---').join('|')}|\n`;
      
      table.sampleData.slice(0, 3).forEach(row => {
        const values = headers.map(header => {
          const value = row[header];
          if (value === null) return 'NULL';
          if (typeof value === 'string' && value.length > 30) {
            return value.substring(0, 30) + '...';
          }
          return String(value);
        });
        markdown += `| ${values.join(' | ')} |\n`;
      });
    }
    
    markdown += `\n---\n\n`;
  });
  
  return markdown;
}

// 推测表的用途
function guessPurpose(tableName, columns) {
  const name = tableName.toLowerCase();
  const columnNames = columns.map(col => col.Field.toLowerCase());
  
  if (name.includes('conversation') || columnNames.some(col => col.includes('message') || col.includes('chat'))) {
    return '对话/聊天记录';
  }
  if (name.includes('user') || name.includes('member')) {
    return '用户/成员信息';
  }
  if (name.includes('project')) {
    return '项目信息';
  }
  if (name.includes('company') || name.includes('corp')) {
    return '公司/企业信息';
  }
  if (name.includes('work') && name.includes('order')) {
    return '工单信息';
  }
  if (name.includes('grid')) {
    return '网格/区域信息';
  }
  if (name.includes('survey') || name.includes('feedback')) {
    return '调查/反馈信息';
  }
  if (name.includes('log') || name.includes('audit')) {
    return '日志/审计信息';
  }
  if (name.includes('config') || name.includes('setting')) {
    return '配置/设置信息';
  }
  
  return '待分析';
}

// 主分析函数
async function analyzeMySQLDatabase() {
  let connection = null;
  
  try {
    connection = await createMySQLConnection();
    
    // 获取所有表
    const tables = await getAllTables(connection);
    console.log(`\n📊 数据库 'walrus' 包含 ${tables.length} 个表:`);
    
    const analysisResult = {
      database: 'walrus',
      tableCount: tables.length,
      tables: []
    };
    
    let totalRecords = 0;
    
    // 分析每个表
    for (const tableRow of tables) {
      const tableName = Object.values(tableRow)[0];
      console.log(`\n🔍 分析表: ${tableName}`);
      
      try {
        const structure = await getTableStructure(connection, tableName);
        const sampleData = await getTableSample(connection, tableName, 3);
        const recordCount = await getTableCount(connection, tableName);
        
        totalRecords += recordCount;
        
        const tableInfo = {
          name: tableName,
          recordCount,
          columnCount: structure.columns.length,
          columns: structure.columns,
          indexes: structure.indexes,
          sampleData,
          createTableSQL: structure.createTable
        };
        
        analysisResult.tables.push(tableInfo);
        
        console.log(`  📝 字段数: ${tableInfo.columnCount}`);
        console.log(`  📊 记录数: ${recordCount.toLocaleString()}`);
        
        // 显示主要字段
        console.log(`  🗂️ 主要字段:`, structure.columns.slice(0, 5).map(col => `${col.Field}(${col.Type})`).join(', '));
        
      } catch (error) {
        console.error(`  ❌ 分析表 ${tableName} 失败:`, error);
      }
    }
    
    console.log(`\n📈 总记录数: ${totalRecords.toLocaleString()}`);
    
    // 生成分析报告
    const report = generateMarkdownReport(analysisResult);
    
    // 保存报告到文件
    await fs.writeFile('mysql-database-analysis.md', report, 'utf8');
    console.log('\n📄 分析报告已保存到: mysql-database-analysis.md');
    
    return analysisResult;
    
  } catch (error) {
    console.error('❌ 数据库分析失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

// 执行分析
if (require.main === module) {
  analyzeMySQLDatabase()
    .then(() => {
      console.log('\n✅ 数据库分析完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 分析失败:', error);
      process.exit(1);
    });
}

module.exports = {
  analyzeMySQLDatabase,
  createMySQLConnection,
  getAllTables,
  getTableStructure,
  getTableSample,
  getTableCount
}; 