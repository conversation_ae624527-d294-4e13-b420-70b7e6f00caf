// 直接使用mysql2/promise 
const mysql = require('mysql2/promise');

// 从环境变量获取数据库配置
require('dotenv').config({ path: '.env.local' });

async function analyzeDataStructure() {
  let connection;
  
  try {
    // 创建数据库连接 (使用项目配置)
    connection = await mysql.createConnection({
      host: '**************',
      port: 3306,
      user: 'root',
      password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
      database: 'walrus',
      timezone: '+08:00',
      charset: 'utf8mb4'
    });

    console.log('📊 开始分析MySQL数据库结构...\n');

    // 1. 查看chat_msg表结构
    console.log('🔍 1. chat_msg表结构分析:');
    console.log('=' .repeat(60));
    
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'walrus' AND TABLE_NAME = 'chat_msg'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.table(columns);

    // 2. 查看chat_msg表数据样本
    console.log('\n🔍 2. chat_msg表数据样本 (最新10条):');
    console.log('=' .repeat(60));
    
    const [samples] = await connection.execute(`
      SELECT id, chat_id, timestamp, payload, external_user_id, bot_id
      FROM chat_msg 
      ORDER BY timestamp DESC 
      LIMIT 10
    `);
    
    samples.forEach((row, index) => {
      console.log(`\n--- 记录 ${index + 1} ---`);
      console.log(`ID: ${row.id}`);
      console.log(`Chat ID: ${row.chat_id}`);
      console.log(`时间戳: ${row.timestamp} (${new Date(row.timestamp).toLocaleString()})`);
      console.log(`用户ID: ${row.external_user_id}`);
      console.log(`Bot ID: ${row.bot_id}`);
      console.log(`Payload: ${row.payload ? row.payload.substring(0, 200) + '...' : 'NULL'}`);
    });

    // 3. 分析payload字段内容结构
    console.log('\n🔍 3. payload字段内容结构分析:');
    console.log('=' .repeat(60));
    
    const [payloadSamples] = await connection.execute(`
      SELECT payload 
      FROM chat_msg 
      WHERE payload IS NOT NULL 
      ORDER BY RAND() 
      LIMIT 5
    `);
    
    payloadSamples.forEach((row, index) => {
      console.log(`\n--- Payload样本 ${index + 1} ---`);
      try {
        const payload = JSON.parse(row.payload);
        console.log('JSON结构:');
        console.log(JSON.stringify(payload, null, 2));
      } catch (e) {
        console.log('非JSON格式:', row.payload.substring(0, 100));
      }
    });

    // 4. 检查满意度调查数据
    console.log('\n🔍 4. 满意度调查数据分析:');
    console.log('=' .repeat(60));
    
    const [surveyData] = await connection.execute(`
      SELECT 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.survey')) as survey_value,
        COUNT(*) as count
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload) 
        AND JSON_EXTRACT(payload, '$.survey') IS NOT NULL
      GROUP BY JSON_UNQUOTE(JSON_EXTRACT(payload, '$.survey'))
      ORDER BY count DESC
    `);
    
    console.table(surveyData);

    // 5. 检查工单和线索数据
    console.log('\n🔍 5. 工单和线索数据分析:');
    console.log('=' .repeat(60));
    
    const [workOrderData] = await connection.execute(`
      SELECT 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.work_order')) as work_order,
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.lead')) as lead,
        COUNT(*) as count
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload) 
        AND (JSON_EXTRACT(payload, '$.work_order') IS NOT NULL 
             OR JSON_EXTRACT(payload, '$.lead') IS NOT NULL)
      GROUP BY 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.work_order')),
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.lead'))
      ORDER BY count DESC
      LIMIT 10
    `);
    
    console.table(workOrderData);

    // 6. 检查24小时分布数据
    console.log('\n🔍 6. 24小时对话分布分析:');
    console.log('=' .repeat(60));
    
    const [hourlyData] = await connection.execute(`
      SELECT 
        HOUR(FROM_UNIXTIME(timestamp/1000)) as hour,
        COUNT(*) as conversation_count
      FROM chat_msg 
      WHERE payload IS NOT NULL
      GROUP BY HOUR(FROM_UNIXTIME(timestamp/1000))
      ORDER BY hour
    `);
    
    console.table(hourlyData);

    // 7. 检查chat_list表关联
    console.log('\n🔍 7. chat_list表关联分析:');
    console.log('=' .repeat(60));
    
    const [chatListSample] = await connection.execute(`
      SELECT 
        cl.chat_id,
        cl.project_code,
        cl.project_name,
        COUNT(cm.id) as message_count
      FROM chat_list cl
      LEFT JOIN chat_msg cm ON cl.chat_id = cm.chat_id
      GROUP BY cl.chat_id, cl.project_code, cl.project_name
      ORDER BY message_count DESC
      LIMIT 10
    `);
    
    console.table(chatListSample);

    // 8. 检查时间范围
    console.log('\n🔍 8. 数据时间范围分析:');
    console.log('=' .repeat(60));
    
    const [timeRange] = await connection.execute(`
      SELECT 
        MIN(FROM_UNIXTIME(timestamp/1000)) as earliest_time,
        MAX(FROM_UNIXTIME(timestamp/1000)) as latest_time,
        COUNT(*) as total_messages
      FROM chat_msg
    `);
    
    console.table(timeRange);

    // 9. 检查JSON字段的各个属性
    console.log('\n🔍 9. JSON payload字段属性统计:');
    console.log('=' .repeat(60));
    
    const jsonAttributes = [
      'survey', 'work_order', 'lead', 'messages', 'emotion', 'satisfaction'
    ];
    
    for (const attr of jsonAttributes) {
      const [attrStats] = await connection.execute(`
        SELECT 
          '${attr}' as attribute_name,
          COUNT(CASE WHEN JSON_EXTRACT(payload, '$.${attr}') IS NOT NULL THEN 1 END) as has_value,
          COUNT(*) as total_records,
          ROUND(COUNT(CASE WHEN JSON_EXTRACT(payload, '$.${attr}') IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as percentage
        FROM chat_msg 
        WHERE payload IS NOT NULL AND JSON_VALID(payload)
      `);
      
      console.table(attrStats);
    }

  } catch (error) {
    console.error('❌ 数据库分析失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行分析
analyzeDataStructure().then(() => {
  console.log('\n✅ 数据库结构分析完成!');
}).catch(console.error); 