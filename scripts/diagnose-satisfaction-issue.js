const mysql = require('mysql2/promise');

async function diagnoseSatisfactionIssue() {
  console.log('🔍 VAN模式: 诊断满意度趋势为空问题...');
  
  const connection = await mysql.createConnection({
    host: 'rm-cn-09k2q5zn3004h2ho.mysql.rds.aliyuncs.com',
    port: 3306,
    user: 'mofei2024',
    password: 'MF2024@3306',
    database: 'walrus',
    charset: 'utf8mb4'
  });

  try {
    console.log('\n📊 1. 检查满意度数据分布...');
    
    // 检查满意度数据分布
    const [satisfactionRows] = await connection.execute(`
      SELECT 
        cl.satisfaction,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM chat_list cl 
      WHERE cl.satisfaction IS NOT NULL AND cl.satisfaction != ''
      GROUP BY cl.satisfaction 
      ORDER BY count DESC
    `);
    
    console.log('满意度代码分布:');
    satisfactionRows.forEach(row => {
      console.log(`  ${row.satisfaction}: ${row.count}条 (${row.percentage}%)`);
    });
    
    console.log('\n📅 2. 检查时间范围内的满意度数据...');
    
    // 检查时间范围内的满意度数据
    const [timeRangeRows] = await connection.execute(`
      SELECT 
        DATE(FROM_UNIXTIME(cm.timestamp/1000)) as date,
        COUNT(DISTINCT cm.chat_id) as total_chats,
        COUNT(CASE WHEN cl.satisfaction IS NOT NULL AND cl.satisfaction != '' THEN 1 END) as satisfaction_count,
        COUNT(CASE WHEN cl.satisfaction IN ('S05', 'S08', 'S10') THEN 1 END) as satisfied_count,
        COUNT(CASE WHEN cl.satisfaction = 'S0' THEN 1 END) as very_unsatisfied_count
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      WHERE FROM_UNIXTIME(cm.timestamp/1000) >= '2025-05-21' 
        AND FROM_UNIXTIME(cm.timestamp/1000) <= '2025-05-27 23:59:59'
      GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
      ORDER BY date
    `);
    
    console.log('2025-05-21到2025-05-27期间满意度数据:');
    timeRangeRows.forEach(row => {
      const satisfactionRate = row.satisfaction_count > 0 ? 
        (row.satisfied_count * 100 / row.satisfaction_count).toFixed(2) : 0;
      console.log(`  ${row.date}: 总会话${row.total_chats}个, 有满意度${row.satisfaction_count}个, 满意${row.satisfied_count}个 (满意率${satisfactionRate}%)`);
    });
    
    console.log('\n🔗 3. 检查会话整合情况...');
    
    // 检查会话整合问题 - chat_msg vs chat_list的关系
    const [sessionRows] = await connection.execute(`
      SELECT 
        COUNT(DISTINCT cm.message_id) as total_messages,
        COUNT(DISTINCT cm.chat_id) as unique_chat_ids,
        COUNT(DISTINCT cl.chat_id) as chat_list_sessions,
        ROUND(COUNT(DISTINCT cl.chat_id) * 100.0 / COUNT(DISTINCT cm.chat_id), 2) as session_coverage
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      WHERE FROM_UNIXTIME(cm.timestamp/1000) >= '2025-05-21' 
        AND FROM_UNIXTIME(cm.timestamp/1000) <= '2025-05-27 23:59:59'
    `);
    
    console.log('会话整合情况:');
    sessionRows.forEach(row => {
      console.log(`  消息总数: ${row.total_messages}`);
      console.log(`  chat_msg中唯一会话ID: ${row.unique_chat_ids}`);
      console.log(`  chat_list中会话记录: ${row.chat_list_sessions}`);
      console.log(`  会话覆盖率: ${row.session_coverage}%`);
    });
    
    console.log('\n📈 4. 分析对话量过大问题...');
    
    // 分析对话量过大问题
    const [volumeRows] = await connection.execute(`
      SELECT 
        DATE(FROM_UNIXTIME(cm.timestamp/1000)) as date,
        COUNT(*) as total_messages,
        COUNT(DISTINCT cm.chat_id) as unique_sessions,
        ROUND(COUNT(*) / COUNT(DISTINCT cm.chat_id), 2) as avg_messages_per_session
      FROM chat_msg cm
      WHERE FROM_UNIXTIME(cm.timestamp/1000) >= '2025-05-21' 
        AND FROM_UNIXTIME(cm.timestamp/1000) <= '2025-05-27 23:59:59'
      GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
      ORDER BY date
    `);
    
    console.log('每日对话量分析:');
    volumeRows.forEach(row => {
      console.log(`  ${row.date}: ${row.total_messages}条消息, ${row.unique_sessions}个会话, 平均每会话${row.avg_messages_per_session}条消息`);
    });
    
    console.log('\n🔍 5. 检查chat_msg_event关联率...');
    
    // 检查事件关联率
    const [eventRows] = await connection.execute(`
      SELECT 
        COUNT(DISTINCT cm.message_id) as total_messages,
        COUNT(DISTINCT cme.message_id) as messages_with_events,
        ROUND(COUNT(DISTINCT cme.message_id) * 100.0 / COUNT(DISTINCT cm.message_id), 2) as event_coverage,
        COUNT(CASE WHEN cme.biz_type = 'IOC_TASK' THEN 1 END) as work_order_events,
        COUNT(CASE WHEN cme.biz_type = 'CHANCE' THEN 1 END) as lead_events
      FROM chat_msg cm
      LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
      WHERE FROM_UNIXTIME(cm.timestamp/1000) >= '2025-05-21' 
        AND FROM_UNIXTIME(cm.timestamp/1000) <= '2025-05-27 23:59:59'
    `);
    
    console.log('事件关联情况:');
    eventRows.forEach(row => {
      console.log(`  总消息数: ${row.total_messages}`);
      console.log(`  有事件的消息: ${row.messages_with_events}`);
      console.log(`  事件覆盖率: ${row.event_coverage}%`);
      console.log(`  工单事件: ${row.work_order_events}个`);
      console.log(`  线索事件: ${row.lead_events}个`);
    });
    
    console.log('\n🎯 问题总结:');
    
    // 问题总结
    const totalSatisfaction = satisfactionRows.reduce((sum, row) => sum + row.count, 0);
    const timeRangeTotal = timeRangeRows.reduce((sum, row) => sum + row.satisfaction_count, 0);
    const avgCoverage = sessionRows[0]?.session_coverage || 0;
    const avgEventCoverage = eventRows[0]?.event_coverage || 0;
    
    console.log(`1. 满意度数据总量: ${totalSatisfaction}条`);
    console.log(`2. 查询时间范围内满意度数据: ${timeRangeTotal}条`);
    console.log(`3. 会话覆盖率: ${avgCoverage}%`);
    console.log(`4. 事件覆盖率: ${avgEventCoverage}%`);
    
    if (timeRangeTotal === 0) {
      console.log('\n❌ 问题识别: 查询时间范围内没有满意度数据!');
      console.log('建议: 调整查询时间范围到有数据的日期');
    }
    
    if (avgCoverage < 90) {
      console.log('\n⚠️ 问题识别: 会话覆盖率较低，可能存在数据整合问题');
    }
    
    if (avgEventCoverage < 10) {
      console.log('\n⚠️ 问题识别: 事件覆盖率极低，影响业务统计准确性');
    }
    
  } finally {
    await connection.end();
  }
}

diagnoseSatisfactionIssue().catch(console.error); 