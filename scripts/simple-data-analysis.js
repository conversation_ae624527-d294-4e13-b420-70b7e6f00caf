const mysql = require('mysql2/promise');

async function simpleDataAnalysis() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: '**************',
      port: 3306,
      user: 'root',
      password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
      database: 'walrus',
      timezone: '+08:00',
      charset: 'utf8mb4'
    });

    console.log('🔍 payload字段内容结构分析:\n');
    
    // 1. 检查payload字段的JSON结构
    const [payloadSamples] = await connection.execute(`
      SELECT payload 
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload)
      ORDER BY RAND() 
      LIMIT 3
    `);
    
    console.log('=== Payload样本结构 ===');
    payloadSamples.forEach((row, index) => {
      console.log(`\n--- 样本 ${index + 1} ---`);
      try {
        const payload = JSON.parse(row.payload);
        console.log('JSON键:', Object.keys(payload));
        console.log('结构预览:', JSON.stringify(payload, null, 2).substring(0, 300) + '...');
      } catch (e) {
        console.log('解析错误:', e.message);
      }
    });

    // 2. 检查满意度数据
    console.log('\n=== 满意度调查数据统计 ===');
    const [surveyData] = await connection.execute(`
      SELECT 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.survey')) as survey_value,
        COUNT(*) as count
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload) 
        AND JSON_EXTRACT(payload, '$.survey') IS NOT NULL
      GROUP BY JSON_UNQUOTE(JSON_EXTRACT(payload, '$.survey'))
      ORDER BY count DESC
    `);
    
    if (surveyData.length > 0) {
      console.table(surveyData);
    } else {
      console.log('❌ 没有找到满意度调查数据');
    }

    // 3. 检查工单转化数据
    console.log('\n=== 工单转化数据统计 ===');
    const [workOrderData] = await connection.execute(`
      SELECT 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.work_order')) as work_order_value,
        COUNT(*) as count
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload) 
        AND JSON_EXTRACT(payload, '$.work_order') IS NOT NULL
      GROUP BY JSON_UNQUOTE(JSON_EXTRACT(payload, '$.work_order'))
      ORDER BY count DESC
      LIMIT 5
    `);
    
    if (workOrderData.length > 0) {
      console.table(workOrderData);
    } else {
      console.log('❌ 没有找到工单转化数据');
    }

    // 4. 检查商机转化数据
    console.log('\n=== 商机转化数据统计 ===');
    const [leadData] = await connection.execute(`
      SELECT 
        JSON_UNQUOTE(JSON_EXTRACT(payload, '$.lead')) as lead_value,
        COUNT(*) as count
      FROM chat_msg 
      WHERE payload IS NOT NULL 
        AND JSON_VALID(payload) 
        AND JSON_EXTRACT(payload, '$.lead') IS NOT NULL
      GROUP BY JSON_UNQUOTE(JSON_EXTRACT(payload, '$.lead'))
      ORDER BY count DESC
      LIMIT 5
    `);
    
    if (leadData.length > 0) {
      console.table(leadData);
    } else {
      console.log('❌ 没有找到商机转化数据');
    }

    // 5. 检查payload中所有可能的键
    console.log('\n=== payload中的所有字段类型 ===');
    const [keyAnalysis] = await connection.execute(`
      SELECT 
        COUNT(*) as total_payloads,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.text') IS NOT NULL THEN 1 END) as has_text,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.survey') IS NOT NULL THEN 1 END) as has_survey,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.work_order') IS NOT NULL THEN 1 END) as has_work_order,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.lead') IS NOT NULL THEN 1 END) as has_lead,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.messages') IS NOT NULL THEN 1 END) as has_messages,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.emotion') IS NOT NULL THEN 1 END) as has_emotion,
        COUNT(CASE WHEN JSON_EXTRACT(payload, '$.satisfaction') IS NOT NULL THEN 1 END) as has_satisfaction
      FROM chat_msg 
      WHERE payload IS NOT NULL AND JSON_VALID(payload)
    `);
    
    console.table(keyAnalysis);

    // 6. 检查时间分布
    console.log('\n=== 24小时对话分布 ===');
    const [hourlyData] = await connection.execute(`
      SELECT 
        HOUR(FROM_UNIXTIME(timestamp/1000)) as hour,
        COUNT(*) as count
      FROM chat_msg 
      WHERE timestamp IS NOT NULL
      GROUP BY HOUR(FROM_UNIXTIME(timestamp/1000))
      ORDER BY hour
      LIMIT 10
    `);
    
    console.table(hourlyData);

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

simpleDataAnalysis().then(() => {
  console.log('\n✅ 数据分析完成!');
}).catch(console.error); 