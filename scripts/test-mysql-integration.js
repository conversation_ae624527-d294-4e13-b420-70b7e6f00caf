// 由于这是一个JS文件测试TS文件，我们需要直接使用mysql2测试
const mysql = require('mysql2/promise');

// MySQL配置
const MYSQL_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'root',
  password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
  database: 'walrus',
  timezone: '+08:00',
  charset: 'utf8mb4'
};

// 创建连接池
const pool = mysql.createPool(MYSQL_CONFIG);

// 测试函数
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.execute('SELECT 1');
    connection.release();
    console.log('✅ MySQL数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ MySQL数据库连接测试失败:', error);
    return false;
  }
}

async function getProjects() {
  const connection = await pool.getConnection();
  try {
    const query = `
      SELECT DISTINCT
        spc.project_code,
        spc.project_name,
        GROUP_CONCAT(DISTINCT sb.grid_name) AS grid_names
      FROM sys_project_config spc
      LEFT JOIN sys_bot sb ON spc.project_code = sb.project_code
      WHERE spc.project_name IS NOT NULL
      GROUP BY spc.project_code, spc.project_name
      ORDER BY spc.project_name
    `;
    const [rows] = await connection.execute(query);
    return rows.map(row => ({
      project_code: row.project_code,
      project_name: row.project_name,
      grid_names: row.grid_names ? row.grid_names.split(',') : []
    }));
  } finally {
    connection.release();
  }
}

async function getGrids() {
  const connection = await pool.getConnection();
  try {
    const query = `
      SELECT DISTINCT grid_name 
      FROM sys_bot 
      WHERE grid_name IS NOT NULL AND grid_name != ''
      ORDER BY grid_name
    `;
    const [rows] = await connection.execute(query);
    return rows.map(row => row.grid_name);
  } finally {
    connection.release();
  }
}

async function getConversations(params = {}) {
  const connection = await pool.getConnection();
  try {
    const { limit = 1000, offset = 0, project_id } = params;
    
    let whereClause = 'WHERE cm.deleted = 0';
    const queryParams = [];
    
    if (project_id) {
      whereClause += ' AND cl.project_code = ?';
      queryParams.push(project_id);
    }
    
    const query = `
      SELECT 
        cm.id,
        cm.chat_id,
        FROM_UNIXTIME(cm.timestamp/1000) AS created_at,
        cm.external_user_id AS user_id,
        cm.contact_name AS user_name,
        JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.text')) AS content,
        cl.project_code AS project_id,
        cl.project_name AS project_name,
        sb.grid_name AS grid_name,
        COALESCE(cm.emotion_type, cl.emotion_type) AS emotion_type,
        CASE 
          WHEN cl.satisfaction IN ('很满意', '满意') THEN '满意'
          WHEN cl.satisfaction = '一般' THEN '一般'
          WHEN cl.satisfaction IN ('不满意', '很不满意') THEN '不满意'
          ELSE NULL
        END AS survey,
        CASE 
          WHEN EXISTS(
            SELECT 1 FROM chat_msg_event cme 
            WHERE cme.message_id = cm.message_id 
            AND cme.biz_type LIKE '%WORK%'
          ) THEN 1 
          ELSE 0 
        END AS work_order
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      AND cm.timestamp IS NOT NULL
      AND cm.payload IS NOT NULL
      ORDER BY cm.timestamp DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(limit, offset);
    const [rows] = await connection.execute(query, queryParams);
    return rows;
  } finally {
    connection.release();
  }
}

async function getStats(params = {}) {
  const connection = await pool.getConnection();
  try {
    const query = `
      SELECT 
        COUNT(*) AS total_conversations,
        COUNT(DISTINCT cm.chat_id) AS unique_chats,
        COUNT(DISTINCT cm.external_user_id) AS unique_users,
        COUNT(DISTINCT cl.project_code) AS unique_projects,
        COUNT(DISTINCT sb.grid_name) AS unique_grids,
        SUM(CASE WHEN cl.satisfaction IN ('很满意', '满意') THEN 1 ELSE 0 END) AS satisfied_count,
        SUM(CASE WHEN cl.satisfaction = '一般' THEN 1 ELSE 0 END) AS neutral_count,
        SUM(CASE WHEN cl.satisfaction IN ('不满意', '很不满意') THEN 1 ELSE 0 END) AS unsatisfied_count,
        COUNT(CASE WHEN EXISTS(
          SELECT 1 FROM chat_msg_event cme 
          WHERE cme.message_id = cm.message_id 
          AND cme.biz_type LIKE '%WORK%'
        ) THEN 1 END) AS work_orders
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      WHERE cm.deleted = 0 AND cm.timestamp IS NOT NULL
    `;
    const [rows] = await connection.execute(query);
    return rows[0];
  } finally {
    connection.release();
  }
}

async function closePool() {
  await pool.end();
}

async function runIntegrationTests() {
  console.log('🚀 开始MySQL数据库集成测试...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    const connectionOk = await testConnection();
    if (!connectionOk) {
      throw new Error('数据库连接失败');
    }
    console.log('✅ 数据库连接成功\n');

    // 2. 测试获取项目列表
    console.log('2️⃣ 测试获取项目列表...');
    const projects = await getProjects();
    console.log(`📋 找到 ${projects.length} 个项目:`);
    projects.slice(0, 5).forEach(project => {
      console.log(`  - ${project.project_name} (${project.project_code})`);
    });
    console.log('✅ 项目列表获取成功\n');

    // 3. 测试获取网格列表
    console.log('3️⃣ 测试获取网格列表...');
    const grids = await getGrids();
    console.log(`🌐 找到 ${grids.length} 个网格:`);
    grids.slice(0, 10).forEach(grid => {
      console.log(`  - ${grid}`);
    });
    console.log('✅ 网格列表获取成功\n');

    // 4. 测试获取对话记录
    console.log('4️⃣ 测试获取对话记录...');
    const conversations = await getConversations({ limit: 5 });
    console.log(`💬 获取到 ${conversations.length} 条对话记录:`);
    conversations.forEach((conv, index) => {
      console.log(`  ${index + 1}. [${conv.created_at}] ${conv.user_name}: ${conv.content?.substring(0, 50)}...`);
      console.log(`     项目: ${conv.project_name || '未知'}, 网格: ${conv.grid_name || '未知'}`);
    });
    console.log('✅ 对话记录获取成功\n');

    // 5. 测试统计数据
    console.log('5️⃣ 测试获取统计数据...');
    const stats = await getStats({});
    console.log('📊 统计数据:');
    console.log(`  - 总对话数: ${stats.total_conversations}`);
    console.log(`  - 独立会话: ${stats.unique_chats}`);
    console.log(`  - 独立用户: ${stats.unique_users}`);
    console.log(`  - 项目数量: ${stats.unique_projects}`);
    console.log(`  - 网格数量: ${stats.unique_grids}`);
    console.log(`  - 满意度统计:`);
    console.log(`    * 满意: ${stats.satisfied_count}`);
    console.log(`    * 一般: ${stats.neutral_count}`);
    console.log(`    * 不满意: ${stats.unsatisfied_count}`);
    console.log(`  - 工单数量: ${stats.work_orders}`);
    console.log('✅ 统计数据获取成功\n');

    // 6. 测试过滤查询
    console.log('6️⃣ 测试过滤查询...');
    if (projects.length > 0) {
      const firstProject = projects[0];
      const filteredConversations = await getConversations({
        project_id: firstProject.project_code,
        limit: 3
      });
      console.log(`🔍 项目 "${firstProject.project_name}" 的对话记录 (${filteredConversations.length} 条):`);
      filteredConversations.forEach((conv, index) => {
        console.log(`  ${index + 1}. ${conv.user_name}: ${conv.content?.substring(0, 40)}...`);
      });
    }
    console.log('✅ 过滤查询测试成功\n');

    // 7. 数据质量检查
    console.log('7️⃣ 数据质量检查...');
    const qualityIssues = [];
    
    // 检查数据完整性
    const sampleConversations = await getConversations({ limit: 100 });
    const missingContent = sampleConversations.filter(c => !c.content || c.content.trim() === '');
    const missingUser = sampleConversations.filter(c => !c.user_name || c.user_name.trim() === '');
    const missingProject = sampleConversations.filter(c => !c.project_name);
    
    if (missingContent.length > 0) {
      qualityIssues.push(`${missingContent.length} 条记录缺少内容`);
    }
    if (missingUser.length > 0) {
      qualityIssues.push(`${missingUser.length} 条记录缺少用户名`);
    }
    if (missingProject.length > 0) {
      qualityIssues.push(`${missingProject.length} 条记录缺少项目信息`);
    }

    if (qualityIssues.length > 0) {
      console.log('⚠️ 发现数据质量问题:');
      qualityIssues.forEach(issue => console.log(`  - ${issue}`));
    } else {
      console.log('✅ 数据质量检查通过');
    }
    console.log();

    // 8. 性能测试
    console.log('8️⃣ 性能测试...');
    const startTime = Date.now();
    await getConversations({ limit: 1000 });
    const duration = Date.now() - startTime;
    console.log(`⚡ 查询1000条记录耗时: ${duration}ms`);
    
    if (duration > 5000) {
      console.log('⚠️ 查询性能可能需要优化');
    } else {
      console.log('✅ 查询性能良好');
    }
    console.log();

    console.log('🎉 所有集成测试完成！');
    console.log('\n📋 测试总结:');
    console.log(`✅ 数据库连接: 正常`);
    console.log(`✅ 项目数据: ${projects.length} 个项目`);
    console.log(`✅ 网格数据: ${grids.length} 个网格`);
    console.log(`✅ 对话数据: ${stats.total_conversations} 条记录`);
    console.log(`✅ 查询性能: ${duration}ms (1000条记录)`);
    
    if (qualityIssues.length > 0) {
      console.log(`⚠️ 数据质量: 发现 ${qualityIssues.length} 个问题`);
    } else {
      console.log(`✅ 数据质量: 良好`);
    }

  } catch (error) {
    console.error('❌ 集成测试失败:', error);
    process.exit(1);
  } finally {
    // 关闭连接池
    await closePool();
    console.log('\n🔐 数据库连接池已关闭');
  }
}

// 运行测试
if (require.main === module) {
  runIntegrationTests()
    .then(() => {
      console.log('\n✅ MySQL集成测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { runIntegrationTests }; 