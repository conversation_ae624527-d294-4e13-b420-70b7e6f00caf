const mysql = require('mysql2/promise');

async function analyzeChatTables() {
  const connection = await mysql.createConnection({
    host: '**************',
    port: 3306,
    user: 'root',
    password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
    database: 'walrus',
    timezone: '+08:00'
  });

  try {
    // 分析chat_msg表
    console.log('=== chat_msg 表结构 ===');
    const [chatMsgColumns] = await connection.execute('DESCRIBE chat_msg');
    chatMsgColumns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    console.log('\n=== chat_msg 样本数据 ===');
    const [chatMsgSample] = await connection.execute('SELECT * FROM chat_msg LIMIT 3');
    console.log(JSON.stringify(chatMsgSample, null, 2));

    // 分析chat_msg_event表
    console.log('\n=== chat_msg_event 表结构 ===');
    const [chatEventColumns] = await connection.execute('DESCRIBE chat_msg_event');
    chatEventColumns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    console.log('\n=== chat_msg_event 样本数据 ===');
    const [chatEventSample] = await connection.execute('SELECT * FROM chat_msg_event LIMIT 3');
    console.log(JSON.stringify(chatEventSample, null, 2));

    // 分析chat_list表
    console.log('\n=== chat_list 表结构 ===');
    const [chatListColumns] = await connection.execute('DESCRIBE chat_list');
    chatListColumns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    console.log('\n=== chat_list 样本数据 ===');
    const [chatListSample] = await connection.execute('SELECT * FROM chat_list LIMIT 2');
    console.log(JSON.stringify(chatListSample, null, 2));

    // 分析关联关系
    console.log('\n=== 表关联分析 ===');
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM chat_msg');
    console.log(`chat_msg 总记录数: ${countResult[0].total}`);
    
    const [eventCountResult] = await connection.execute('SELECT COUNT(*) as total FROM chat_msg_event');
    console.log(`chat_msg_event 总记录数: ${eventCountResult[0].total}`);

    const [listCountResult] = await connection.execute('SELECT COUNT(*) as total FROM chat_list');
    console.log(`chat_list 总记录数: ${listCountResult[0].total}`);

    // 分析数据时间范围
    console.log('\n=== 数据时间范围分析 ===');
    try {
      const [timeRange] = await connection.execute(`
        SELECT 
          MIN(send_time) as earliest, 
          MAX(send_time) as latest,
          DATE(MIN(send_time)) as earliest_date,
          DATE(MAX(send_time)) as latest_date
        FROM chat_msg 
        WHERE send_time IS NOT NULL
      `);
      console.log('chat_msg 时间范围:', timeRange[0]);
    } catch (error) {
      console.log('chat_msg 时间字段分析失败:', error.message);
    }

    // 分析项目分布
    console.log('\n=== 项目分布分析 ===');
    try {
      const [projectStats] = await connection.execute(`
        SELECT project_code, COUNT(*) as msg_count 
        FROM chat_msg 
        WHERE project_code IS NOT NULL 
        GROUP BY project_code 
        ORDER BY msg_count DESC 
        LIMIT 10
      `);
      console.log('Top 10 项目消息数:', projectStats);
    } catch (error) {
      console.log('项目分布分析失败:', error.message);
    }

  } catch (error) {
    console.error('分析失败:', error);
  } finally {
    await connection.end();
  }
}

analyzeChatTables(); 