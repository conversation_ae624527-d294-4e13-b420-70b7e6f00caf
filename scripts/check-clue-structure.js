const mysql = require('mysql2/promise');

async function checkClueStructure() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: '**************',
      port: 3306,
      user: 'root',
      password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
      database: 'walrus',
      timezone: '+08:00',
      charset: 'utf8mb4'
    });

    console.log('🔍 检查chat_clue表结构\n');
    
    // 1. 查看表结构
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'walrus' AND TABLE_NAME = 'chat_clue'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('=== chat_clue表字段结构 ===');
    console.table(columns);

    // 2. 查看样本数据
    const [samples] = await connection.execute(`
      SELECT * FROM chat_clue LIMIT 3
    `);
    
    console.log('\n=== chat_clue表样本数据 ===');
    console.table(samples);

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkClueStructure().then(() => {
  console.log('\n✅ 检查完成!');
}).catch(console.error); 