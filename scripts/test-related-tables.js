const mysql = require('mysql2/promise');

async function testRelatedTables() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: '**************',
      port: 3306,
      user: 'root',
      password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
      database: 'walrus',
      timezone: '+08:00',
      charset: 'utf8mb4'
    });

    console.log('🔍 测试关联表数据可用性\n');

    // 1. 测试chat_list表的满意度数据
    console.log('=== 1. chat_list表满意度数据 ===');
    const [satisfactionData] = await connection.execute(`
      SELECT 
        satisfaction,
        COUNT(*) as count
      FROM chat_list 
      WHERE satisfaction IS NOT NULL 
        AND satisfaction != ''
      GROUP BY satisfaction
      ORDER BY count DESC
    `);
    
    if (satisfactionData.length > 0) {
      console.table(satisfactionData);
      console.log(`✅ 找到满意度数据: ${satisfactionData.length} 种类型`);
    } else {
      console.log('❌ chat_list表无满意度数据');
    }

    // 2. 测试chat_msg_event表的事件数据
    console.log('\n=== 2. chat_msg_event表事件类型 ===');
    const [eventData] = await connection.execute(`
      SELECT 
        biz_type,
        COUNT(*) as count
      FROM chat_msg_event 
      WHERE biz_type IS NOT NULL
      GROUP BY biz_type
      ORDER BY count DESC
      LIMIT 10
    `);
    
    if (eventData.length > 0) {
      console.table(eventData);
      console.log(`✅ 找到事件数据: ${eventData.length} 种事件类型`);
    } else {
      console.log('❌ chat_msg_event表无事件数据');
    }

    // 3. 测试chat_clue表的商机数据
    console.log('\n=== 3. chat_clue表商机数据 ===');
    const [clueData] = await connection.execute(`
      SELECT 
        COUNT(*) as total_clues,
        COUNT(DISTINCT chat_id) as unique_chats,
        COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_clues
      FROM chat_clue
    `);
    
    console.table(clueData);

    // 4. 测试关联查询 - 满意度趋势
    console.log('\n=== 4. 满意度趋势测试查询 ===');
    const [satisfactionTrend] = await connection.execute(`
      SELECT 
        DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
        cl.satisfaction as survey_value,
        COUNT(*) as count
      FROM chat_msg cm
      JOIN chat_list cl ON cm.chat_id = cl.chat_id
      WHERE cl.satisfaction IS NOT NULL
        AND cm.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY)) * 1000
      GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000)), cl.satisfaction
      ORDER BY day DESC
      LIMIT 10
    `);
    
    if (satisfactionTrend.length > 0) {
      console.table(satisfactionTrend);
      console.log('✅ 满意度趋势查询成功');
    } else {
      console.log('❌ 满意度趋势查询无数据');
    }

    // 5. 测试工单转化率
    console.log('\n=== 5. 工单转化率测试查询 ===');
    const [workOrderRate] = await connection.execute(`
      SELECT 
        DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
        COUNT(DISTINCT cm.chat_id) as total_conversations,
        COUNT(DISTINCT CASE WHEN cme.biz_type LIKE '%WORK%' OR cme.biz_type LIKE '%工单%' THEN cm.chat_id END) as work_order_conversations
      FROM chat_msg cm
      LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
      WHERE cm.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY)) * 1000
      GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
      ORDER BY day DESC
      LIMIT 5
    `);
    
    if (workOrderRate.length > 0) {
      console.table(workOrderRate);
      console.log('✅ 工单转化率查询成功');
    } else {
      console.log('❌ 工单转化率查询无数据');
    }

    // 6. 测试修复后的24小时分析
    console.log('\n=== 6. 修复后的24小时分析测试 ===');
    const [hourlyFixed] = await connection.execute(`
      SELECT 
        HOUR(FROM_UNIXTIME(cm.timestamp/1000)) AS hour_num,
        COUNT(*) AS conversation_count,
        COUNT(DISTINCT cm.chat_id) as unique_chats
      FROM chat_msg cm
      WHERE cm.timestamp IS NOT NULL
        AND cm.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) * 1000
      GROUP BY HOUR(FROM_UNIXTIME(cm.timestamp/1000))
      ORDER BY hour_num
    `);
    
    if (hourlyFixed.length > 0) {
      console.table(hourlyFixed);
      console.log('✅ 24小时分析查询成功');
    } else {
      console.log('❌ 24小时分析查询无数据');
    }

    // 7. 测试情感分析数据
    console.log('\n=== 7. 情感分析数据测试 ===');
    const [emotionData] = await connection.execute(`
      SELECT 
        cm.emotion_type as emotion,
        COUNT(*) as count
      FROM chat_msg cm
      WHERE cm.emotion_type IS NOT NULL 
        AND cm.emotion_type != ''
      GROUP BY cm.emotion_type
      ORDER BY count DESC
    `);
    
    if (emotionData.length > 0) {
      console.table(emotionData);
      console.log('✅ 情感分析数据可用');
    } else {
      console.log('❌ 无情感分析数据');
    }

    // 8. 数据可用性总结
    console.log('\n=== 数据可用性总结 ===');
    const summary = {
      满意度数据: satisfactionData.length > 0 ? '✅ 可用' : '❌ 不可用',
      事件数据: eventData.length > 0 ? '✅ 可用' : '❌ 不可用', 
      商机数据: clueData[0].total_clues > 0 ? '✅ 可用' : '❌ 不可用',
      满意度趋势: satisfactionTrend.length > 0 ? '✅ 可用' : '❌ 不可用',
      工单转化: workOrderRate.length > 0 ? '✅ 可用' : '❌ 不可用',
      小时分析: hourlyFixed.length > 0 ? '✅ 可用' : '❌ 不可用',
      情感分析: emotionData.length > 0 ? '✅ 可用' : '❌ 不可用'
    };
    
    console.table([summary]);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testRelatedTables().then(() => {
  console.log('\n✅ 关联表数据测试完成!');
}).catch(console.error); 