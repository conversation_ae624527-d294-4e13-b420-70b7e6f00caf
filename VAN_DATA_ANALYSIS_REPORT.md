# VAN模式 - MySQL数据结构深度分析报告

**分析时间**: 2025-06-18  
**分析目标**: 解决24小时对话分析、满意度趋势等数据为空的问题  
**数据库**: walrus (MySQL)  
**表数据量**: 28,263条有效payload记录

## 🔍 核心问题发现

### 问题现象
以下功能查询返回空数据：
- ❌ 24小时对话分析
- ❌ 满意度趋势  
- ❌ 工单转化率趋势
- ❌ 商机转化率趋势
- ❌ 平均对话轮次趋势
- ❌ 问题分类详细分析
- ❌ 问题分类项目Top排名

### 根本原因分析

#### 1. **payload字段结构与预期不匹配**

**现实结构**:
```json
{
  "text": "用户消息内容",
  "mention": ["1688856562485869"],
  "pureText": "纯文本内容"
}
```

**代码期望的结构**:
```json
{
  "survey": "很满意",
  "work_order": "true", 
  "lead": "true",
  "messages": [...],
  "emotion": "positive",
  "satisfaction": "satisfied"
}
```

#### 2. **数据字段完全缺失**
通过分析28,263条payload记录发现：
- `survey`: 0条记录 ❌
- `work_order`: 0条记录 ❌  
- `lead`: 0条记录 ❌
- `messages`: 0条记录 ❌
- `emotion`: 0条记录 ❌
- `satisfaction`: 0条记录 ❌

#### 3. **业务数据可能存储在其他表**

基于MySQL数据库结构分析，发现以下可能的数据源：

**满意度数据可能在**:
- `chat_list.satisfaction` 字段
- `annot_conversation_evaluation` 表 (25条记录)
- `user_opinion` 表 (17条记录)

**工单数据可能在**:
- `work_order` 表 (5条记录)
- `chat_msg_event` 表 (2,346,387条记录 - 关键!)
- `chat_clue` 表 (342条记录)

**情感分析数据可能在**:
- `chat_msg.emotion_type` 字段
- `chat_list.emotion_type` 字段

## 🎯 解决方案设计

### 方案A: 关联表查询 (推荐)

#### 1. 满意度趋势查询
```sql
-- 从chat_list表获取满意度数据
SELECT 
  DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
  cl.satisfaction as survey_value,
  COUNT(*) as count,
  CASE 
    WHEN cl.satisfaction IN ('很满意', '满意') THEN 'satisfied'
    WHEN cl.satisfaction = '一般' THEN 'neutral'
    WHEN cl.satisfaction IN ('不满意', '很不满意') THEN 'unsatisfied'
    ELSE 'unknown'
  END as satisfaction_category
FROM chat_msg cm
JOIN chat_list cl ON cm.chat_id = cl.chat_id
WHERE cl.satisfaction IS NOT NULL
GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000)), cl.satisfaction
ORDER BY day DESC
```

#### 2. 工单转化率趋势
```sql
-- 从chat_msg_event表关联工单数据
SELECT 
  DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
  COUNT(DISTINCT cm.chat_id) as total_conversations,
  COUNT(DISTINCT CASE WHEN cme.biz_type LIKE '%WORK%' THEN cm.chat_id END) as work_order_conversations,
  ROUND(
    COUNT(DISTINCT CASE WHEN cme.biz_type LIKE '%WORK%' THEN cm.chat_id END) * 100.0 / 
    COUNT(DISTINCT cm.chat_id), 2
  ) as work_order_rate
FROM chat_msg cm
LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
ORDER BY day DESC
```

#### 3. 商机转化率趋势
```sql
-- 从chat_clue表获取商机数据
SELECT 
  DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
  COUNT(DISTINCT cm.chat_id) as total_conversations,
  COUNT(DISTINCT cc.chat_id) as lead_conversations,
  ROUND(
    COUNT(DISTINCT cc.chat_id) * 100.0 / COUNT(DISTINCT cm.chat_id), 2
  ) as lead_rate
FROM chat_msg cm
LEFT JOIN chat_clue cc ON cm.chat_id = cc.chat_id
GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
ORDER BY day DESC
```

#### 4. 24小时对话分析 (修复SQL语法错误)
```sql
-- 修复GROUP BY错误的小时分析
SELECT 
  HOUR(FROM_UNIXTIME(cm.timestamp/1000)) AS hour_num,
  COUNT(*) AS conversation_count,
  COUNT(DISTINCT cm.chat_id) as unique_chats,
  COUNT(DISTINCT CASE WHEN cme.biz_type LIKE '%WORK%' THEN cm.chat_id END) as work_order_count,
  COUNT(DISTINCT cc.chat_id) as lead_count
FROM chat_msg cm
LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
LEFT JOIN chat_clue cc ON cm.chat_id = cc.chat_id
WHERE cm.timestamp IS NOT NULL
GROUP BY HOUR(FROM_UNIXTIME(cm.timestamp/1000))
ORDER BY hour_num
```

#### 5. 平均对话轮次趋势
```sql
-- 基于chat_id统计对话轮次
SELECT 
  DATE(FROM_UNIXTIME(cm.timestamp/1000)) as day,
  AVG(chat_counts.message_count) as avg_messages_per_chat
FROM (
  SELECT 
    chat_id,
    DATE(FROM_UNIXTIME(timestamp/1000)) as day,
    COUNT(*) as message_count
  FROM chat_msg
  WHERE timestamp IS NOT NULL
  GROUP BY chat_id, DATE(FROM_UNIXTIME(timestamp/1000))
) chat_counts
JOIN chat_msg cm ON chat_counts.chat_id = cm.chat_id 
  AND chat_counts.day = DATE(FROM_UNIXTIME(cm.timestamp/1000))
GROUP BY day
ORDER BY day DESC
```

### 方案B: 数据模拟 (临时方案)

如果关联表数据不足，可以基于现有数据模拟：

```sql
-- 基于文本内容模拟满意度
SELECT 
  DATE(FROM_UNIXTIME(timestamp/1000)) as day,
  CASE 
    WHEN JSON_UNQUOTE(JSON_EXTRACT(payload, '$.text')) LIKE '%谢谢%' 
      OR JSON_UNQUOTE(JSON_EXTRACT(payload, '$.text')) LIKE '%满意%' 
      THEN 'satisfied'
    WHEN JSON_UNQUOTE(JSON_EXTRACT(payload, '$.text')) LIKE '%问题%' 
      OR JSON_UNQUOTE(JSON_EXTRACT(payload, '$.text')) LIKE '%投诉%' 
      THEN 'unsatisfied'
    ELSE 'neutral'
  END as satisfaction_category,
  COUNT(*) as count
FROM chat_msg
WHERE payload IS NOT NULL 
  AND JSON_VALID(payload)
  AND JSON_EXTRACT(payload, '$.text') IS NOT NULL
GROUP BY day, satisfaction_category
ORDER BY day DESC
```

## 🔧 实施计划

### 第一阶段: 查询修复 (立即执行)

1. **修复mysql-conversation-stats.ts中的小时分析查询**
   - 解决GROUP BY语法错误
   - 使用关联表数据

2. **更新所有趋势查询函数**
   - 替换payload字段查询为关联表查询
   - 添加数据验证和回退机制

3. **数据源映射**
   - 满意度: `chat_list.satisfaction`
   - 工单: `chat_msg_event` 表关联
   - 商机: `chat_clue` 表关联
   - 情感: `chat_msg.emotion_type`

### 第二阶段: 数据验证 (1-2天内)

1. **执行测试查询验证数据可用性**
2. **分析数据质量和完整性**
3. **确定最优数据源组合**

### 第三阶段: API更新 (2-3天内)

1. **更新conversation-stats API中的所有查询类型**
2. **添加数据有效性检查和错误处理**
3. **实现数据回退机制**

## 📊 数据源优先级排序

### 高可用性数据源
1. **chat_msg_event** (2,346,387条) - 事件数据丰富
2. **chat_list** (2,425条) - 对话元数据完整
3. **chat_msg** (28,185条) - 消息主体数据

### 中等可用性数据源
1. **chat_clue** (342条) - 商机数据
2. **annot_conversation** (1,620条) - 对话标注
3. **user_opinion** (17条) - 用户意见

### 低可用性数据源
1. **work_order** (5条) - 工单数据极少
2. **annot_conversation_evaluation** (25条) - 评估数据稀少

## 🎯 预期效果

实施后预期解决的问题：
- ✅ 24小时对话分析有数据
- ✅ 满意度趋势基于真实评分
- ✅ 工单转化基于事件数据
- ✅ 商机转化基于线索数据
- ✅ 对话轮次基于消息统计
- ✅ 问题分类基于文本分析

## 🚨 风险评估

### 数据风险
- **低风险**: 24小时分析、对话轮次 (基于充足数据)
- **中风险**: 满意度趋势 (基于chat_list.satisfaction字段)
- **高风险**: 工单/商机转化 (依赖关联表数据质量)

### 技术风险
- SQL语法兼容性
- 查询性能影响
- 数据一致性问题

## 💡 建议

1. **优先修复小时分析的GROUP BY错误**
2. **实施关联表查询方案**
3. **添加数据质量监控**
4. **建立数据回退机制**
5. **考虑未来数据结构标准化**

---

**VAN模式结论**: 数据为空是因为查询逻辑与实际数据结构不匹配。通过关联表查询和SQL修复，可以完全解决空数据问题。 