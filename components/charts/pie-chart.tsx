import { useMemo, useRef } from 'react'
import { Box, Typography, useTheme, IconButton, Paper } from '@mui/material'
import { GetApp as DownloadIcon } from '@mui/icons-material'
import { useExportChart } from '@/hooks/use-export-chart'
import {
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts'

interface DataPoint {
  name: string
  value: number
  [key: string]: any
}

export interface PieChartProps {
  data: DataPoint[]
  nameKey?: string
  dataKey: string
  title?: string
  height?: number
  colors?: string[]
  showLegend?: boolean
  donut?: boolean
  fileName?: string
}

// 自定义工具提示组件
const CustomTooltip = ({ active, payload }: any) => {
  if (!active || !payload || !payload.length) return null

  try {
    const data = payload[0]
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
        }}
      >
        <p style={{ margin: 0, fontWeight: 'bold' }}>{`${data.name}`}</p>
        <p style={{ margin: '5px 0 0', color: data.color }}>{`${data.value}`}</p>
      </div>
    )
  } catch (error) {
    console.error('Error in tooltip:', error)
    return null
  }
}

export function PieChart({
  data,
  nameKey = 'name',
  dataKey,
  title,
  height = 300,
  colors,
  showLegend = true,
  donut = false,
  fileName = 'pie-chart-data',
}: PieChartProps) {
  const theme = useTheme()
  const { exportToExcel } = useExportChart()

  // Default chart colors
  const defaultColors = useMemo(() => {
    return [
      '#00A389', // green
      '#6B5AED', // purple
      '#ED954B', // orange
      '#E55246', // red
      '#3498db', // blue
      '#f1c40f', // yellow
      '#9b59b6', // violet
      '#1abc9c', // turquoise
      '#e67e22', // carrot
      '#34495e', // dark blue
    ]
  }, [])

  // Use provided colors or default colors
  const chartColors = colors || defaultColors

  // 预处理数据，处理空值，并使用正确的键名
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return []

    return data
      .map(item => {
        // 创建新的数据项，确保所有必要的字段都有值
        const newItem = { ...item }
        // 确保名称和值字段存在
        newItem.name = item[nameKey] || '未命名'
        newItem.value = item[dataKey] !== null && item[dataKey] !== undefined ? item[dataKey] : 0
        return newItem
      })
      .filter(item => item.value > 0) // 筛选出有值的数据
  }, [data, nameKey, dataKey])

  if (!processedData.length) {
    return (
      <Box>
        {title && (
          <Typography variant="h6" sx={{ mb: 2 }}>
            {title}
          </Typography>
        )}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            textAlign: 'center',
            backgroundColor: 'rgba(0,0,0,0.02)',
            height: height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="text.secondary">暂无数据</Typography>
        </Paper>
      </Box>
    )
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, alignItems: 'center' }}>
        {title && (
          <Typography variant="h6" fontWeight="medium">
            {title}
          </Typography>
        )}
        <IconButton
          size="small"
          onClick={() => exportToExcel(data, fileName)}
          sx={{
            transition: 'all 0.2s',
            '&:hover': {
              transform: 'scale(1.1)',
              color: theme.palette.primary.main,
            },
          }}
        >
          <DownloadIcon fontSize="small" />
        </IconButton>
      </Box>

      <Paper
        sx={{
          width: '100%',
          borderRadius: 2,
          boxShadow: theme.shadows[1],
          p: 2,
          '&:hover': {
            boxShadow: theme.shadows[3],
          },
        }}
      >
        <ResponsiveContainer width="100%" height={height}>
          <RechartsPieChart>
            <Pie
              data={processedData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={100}
              innerRadius={donut ? 60 : 0}
              fill="#8884d8"
              paddingAngle={2}
              isAnimationActive={false} // 禁用动画以解决潜在问题
            >
              {processedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            {showLegend && <Legend layout="horizontal" verticalAlign="bottom" align="center" />}
          </RechartsPieChart>
        </ResponsiveContainer>
      </Paper>
    </Box>
  )
}
