import React, { useMemo } from 'react'
import {
  <PERSON><PERSON>hart as Re<PERSON>rtsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import { Box, Typography, IconButton, useTheme, Paper } from '@mui/material'
import { GetApp as DownloadIcon } from '@mui/icons-material'
import { useExportChart } from '@/hooks/use-export-chart'
import { format as formatDate, isValid } from 'date-fns'

export interface BarChartProps {
  data: any[]
  xDataKey: string
  yDataKeys: Array<{
    key: string
    name: string
    color?: string
  }>
  title?: string
  fileName?: string
}

// 自定义工具提示组件，避免 null 值错误
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) {
    return null
  }

  try {
    // 尝试格式化日期，如果失败则直接显示原始标签
    let displayLabel = label
    if (label) {
      // 检查是否是日期格式 (YYYY-MM-DD)
      if (typeof label === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(label)) {
        const date = new Date(label)
        if (isValid(date)) {
          displayLabel = formatDate(date, 'yyyy-MM-dd')
        }
      }
    }

    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
        }}
      >
        <p
          className="label"
          style={{ margin: 0, fontWeight: 'bold' }}
        >{`日期: ${displayLabel || '未知'}`}</p>
        {payload.map((entry: any, index: number) => (
          <p
            key={`item-${index}`}
            style={{
              margin: '5px 0 0',
              color: entry.color,
            }}
          >
            {`${entry.name}: ${entry.value !== null && entry.value !== undefined ? entry.value : '无数据'}`}
          </p>
        ))}
      </div>
    )
  } catch (error) {
    console.error('Error in tooltip:', error)
    return null
  }
}

// 自定义X轴格式化方法
const formatXAxis = (tickItem: any) => {
  // 安全检查
  if (!tickItem) return ''

  try {
    // 如果是日期字符串，格式化它
    if (typeof tickItem === 'string') {
      // 处理YYYY-MM-DD格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(tickItem)) {
        const date = new Date(tickItem)
        if (isValid(date)) {
          return formatDate(date, 'MM-dd')
        }
      }
      // 处理YYYY-MM格式
      else if (/^\d{4}-\d{2}$/.test(tickItem)) {
        return tickItem
      }
    }
    // 如果是时间戳
    else if (typeof tickItem === 'number') {
      const date = new Date(tickItem)
      if (isValid(date)) {
        return formatDate(date, 'MM-dd')
      }
    }
    return String(tickItem)
  } catch (error) {
    console.error('Error formatting X axis:', error)
    return String(tickItem || '')
  }
}

export function BarChart({
  data,
  xDataKey,
  yDataKeys,
  title,
  fileName = 'chart-data',
}: BarChartProps) {
  const theme = useTheme()
  const { exportToExcel } = useExportChart()

  // 预处理数据，确保没有null值
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    const newData = data.map(item => {
      const newItem = { ...item }
      // 确保所有需要的字段都有值
      yDataKeys.forEach(({ key }) => {
        if (newItem[key] === null || newItem[key] === undefined) {
          newItem[key] = 0
        }
      })
      return newItem
    })

    // 如果xDataKey看起来是日期字段，按日期排序
    if (
      newData.length > 0 &&
      newData[0][xDataKey] &&
      typeof newData[0][xDataKey] === 'string' &&
      (/^\d{4}-\d{2}-\d{2}$/.test(newData[0][xDataKey]) ||
        /^\d{4}-\d{2}$/.test(newData[0][xDataKey]))
    ) {
      return [...newData].sort((a, b) => {
        // 确保日期值存在
        const aValue = a[xDataKey]
        const bValue = b[xDataKey]

        if (!aValue || !bValue) return 0

        const dateA = new Date(aValue)
        const dateB = new Date(bValue)

        if (!isValid(dateA) || !isValid(dateB)) return 0

        return dateA.getTime() - dateB.getTime()
      })
    }

    return newData
  }, [data, yDataKeys, xDataKey])

  // 压缩数据，针对长时间范围的数据
  const compressedData = useMemo(() => {
    // 如果数据量小于60，不需要压缩
    if (!processedData || processedData.length <= 60) return processedData

    // 对于大数据量，确保显示首尾数据，中间数据进行压缩抽样
    const firstItems = processedData.slice(0, 10) // 保留前10条
    const lastItems = processedData.slice(-10) // 保留最后10条

    // 确定中间需要抽样的数据
    const middleData = processedData.slice(10, -10)

    // 如果中间数据很少，不需要抽样
    if (middleData.length < 40) return processedData

    // 计算需要保留多少中间数据点（总共保留约40个点）
    const targetMiddlePoints = 40
    const stride = Math.ceil(middleData.length / targetMiddlePoints)

    // 抽样中间数据
    const sampledMiddleData = []
    for (let i = 0; i < middleData.length; i += stride) {
      sampledMiddleData.push(middleData[i])
    }

    // 合并首、中、尾数据
    return [...firstItems, ...sampledMiddleData, ...lastItems]
  }, [processedData])

  if (!data || data.length === 0) {
    return (
      <Box>
        <Typography variant="h6" sx={{ mb: 2 }}>
          {title}
        </Typography>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            textAlign: 'center',
            backgroundColor: 'rgba(0,0,0,0.02)',
            height: '300px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="text.secondary">暂无数据</Typography>
        </Paper>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, alignItems: 'center' }}>
        <Typography variant="h6">{title}</Typography>
        <IconButton
          size="small"
          onClick={() => exportToExcel(data, fileName)}
          sx={{
            transition: 'all 0.2s',
            '&:hover': {
              transform: 'scale(1.1)',
              color: theme.palette.primary.main,
            },
          }}
        >
          <DownloadIcon fontSize="small" />
        </IconButton>
      </Box>

      <ResponsiveContainer width="100%" height={300}>
        <RechartsBarChart
          data={compressedData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 25, // 增加底部空间以显示完整X轴标签
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey={xDataKey}
            stroke={theme.palette.text.secondary}
            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
            tickFormatter={formatXAxis}
            angle={-45} // 倾斜标签，防止重叠
            textAnchor="end" // 对齐方式
            height={60} // 增加高度，为倾斜的标签提供更多空间
          />
          <YAxis
            stroke={theme.palette.text.secondary}
            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {yDataKeys.map((dataKey, index) => (
            <Bar
              key={dataKey.key}
              dataKey={dataKey.key}
              name={dataKey.name}
              fill={dataKey.color || theme.palette.primary.main}
              isAnimationActive={false} // 禁用动画，提高性能并减少渲染问题
            />
          ))}
        </RechartsBarChart>
      </ResponsiveContainer>
    </Box>
  )
}
