'use client'

import React, { ReactNode, useRef } from 'react'
import { <PERSON>, Typo<PERSON>, Button } from '@mui/material'
import { FileDownload as DownloadIcon } from '@mui/icons-material'
import * as XLSX from 'xlsx'

export interface ChartContainerProps {
  title: string
  fileName: string
  children: ReactNode
  data: any[] // The data that will be exported when clicking the export button
}

export function ChartContainer({ title, fileName, children, data }: ChartContainerProps) {
  const anchorRef = useRef<HTMLButtonElement>(null)

  // Helper function to export data to Excel with UTF-8 encoding
  const exportToExcel = () => {
    try {
      const worksheet = XLSX.utils.json_to_sheet(data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Data')

      // Use UTF-8 encoding for proper character display
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      })

      // Create a download link and trigger click
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${fileName || 'data-export'}.xlsx`
      document.body.appendChild(link)
      link.click()

      // Cleanup
      setTimeout(() => {
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }, 100)
    } catch (error) {
      console.error('Error exporting data:', error)
    }
  }

  if (!data || data.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          width: '100%',
        }}
      >
        <Typography variant="h6" fontWeight="medium" mb={1}>
          {title}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          暂无数据
        </Typography>
      </Box>
    )
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
        }}
      >
        <Typography variant="h6" fontWeight="medium">
          {title}
        </Typography>
        <Button
          ref={anchorRef}
          variant="outlined"
          size="small"
          startIcon={<DownloadIcon />}
          onClick={exportToExcel}
        >
          导出Excel
        </Button>
      </Box>

      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          width: '100%',
          height: 'calc(100% - 48px)',
          overflow: 'hidden',
        }}
      >
        {children}
      </Box>
    </Box>
  )
}
