'use client'

import React, { useMemo } from 'react'
import { NPSResult } from '@/hooks/use-analytics-data'
import { Box, Typography, useTheme, Paper, IconButton } from '@mui/material'
import { GetApp as DownloadIcon } from '@mui/icons-material'
import { useExportChart } from '@/hooks/use-export-chart'
import { format as formatDate, isValid } from 'date-fns'
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'

export interface NPSChartProps {
  data: NPSResult[]
  title: string
  xAxisKey?: 'month' | 'day'
  fileName: string
}

// 自定义工具提示组件，避免 null 值错误
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) {
    return null
  }

  try {
    // 尝试格式化日期，如果失败则直接显示原始标签
    let displayLabel = label
    if (label) {
      // 检查是否是日期格式 (YYYY-MM-DD)
      if (typeof label === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(label)) {
        const date = new Date(label)
        if (isValid(date)) {
          displayLabel = formatDate(date, 'yyyy-MM-dd')
        }
      }
    }

    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
        }}
      >
        <p
          className="label"
          style={{ margin: 0, fontWeight: 'bold' }}
        >{`日期: ${displayLabel || '未知'}`}</p>
        {payload.map((entry: any, index: number) => (
          <p
            key={`item-${index}`}
            style={{
              margin: '5px 0 0',
              color: entry.color,
            }}
          >
            {`${entry.name}: ${entry.value !== null && entry.value !== undefined ? entry.value : '无数据'}`}
          </p>
        ))}
      </div>
    )
  } catch (error) {
    console.error('Error in tooltip:', error)
    return null
  }
}

// 自定义X轴格式化方法
const formatXAxis = (tickItem: any) => {
  // 安全检查
  if (!tickItem) return ''

  try {
    // 如果是日期字符串，格式化它
    if (typeof tickItem === 'string') {
      // 处理YYYY-MM-DD格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(tickItem)) {
        const date = new Date(tickItem)
        if (isValid(date)) {
          return formatDate(date, 'MM-dd')
        }
      }
      // 处理YYYY-MM格式
      else if (/^\d{4}-\d{2}$/.test(tickItem)) {
        return tickItem
      }
    }
    // 如果是时间戳
    else if (typeof tickItem === 'number') {
      const date = new Date(tickItem)
      if (isValid(date)) {
        return formatDate(date, 'MM-dd')
      }
    }
    return String(tickItem)
  } catch (error) {
    console.error('Error formatting X axis:', error)
    return String(tickItem || '')
  }
}

export function NPSChart({ data, title, xAxisKey = 'month', fileName }: NPSChartProps) {
  const theme = useTheme()
  const { exportToExcel } = useExportChart()

  // 预处理数据，确保没有null值
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    const newData = data.map(item => {
      const newItem = { ...item }
      // 确保NPS相关值都有数值
      if (newItem.nps_score === null || newItem.nps_score === undefined) {
        newItem.nps_score = 0
      }
      if (newItem.promoters_percentage === null || newItem.promoters_percentage === undefined) {
        newItem.promoters_percentage = 0
      }
      if (newItem.passives_percentage === null || newItem.passives_percentage === undefined) {
        newItem.passives_percentage = 0
      }
      if (newItem.detractors_percentage === null || newItem.detractors_percentage === undefined) {
        newItem.detractors_percentage = 0
      }
      return newItem
    })

    // 如果xAxisKey是day，确保按日期排序
    if (xAxisKey === 'day' && newData.length > 0 && newData[0][xAxisKey]) {
      return [...newData].sort((a, b) => {
        // 确保日期值存在
        const aValue = a[xAxisKey]
        const bValue = b[xAxisKey]

        if (!aValue || !bValue) return 0

        const dateA = new Date(aValue)
        const dateB = new Date(bValue)

        if (!isValid(dateA) || !isValid(dateB)) return 0

        return dateA.getTime() - dateB.getTime()
      })
    }

    return newData
  }, [data, xAxisKey])

  // 压缩数据，针对长时间范围的数据
  const compressedData = useMemo(() => {
    // 如果数据量小于60，不需要压缩
    if (!processedData || processedData.length <= 60) return processedData

    // 对于大数据量，确保显示首尾数据，中间数据进行压缩抽样
    const firstItems = processedData.slice(0, 10) // 保留前10条
    const lastItems = processedData.slice(-10) // 保留最后10条

    // 确定中间需要抽样的数据
    const middleData = processedData.slice(10, -10)

    // 如果中间数据很少，不需要抽样
    if (middleData.length < 40) return processedData

    // 计算需要保留多少中间数据点（总共保留约40个点）
    const targetMiddlePoints = 40
    const stride = Math.ceil(middleData.length / targetMiddlePoints)

    // 抽样中间数据
    const sampledMiddleData = []
    for (let i = 0; i < middleData.length; i += stride) {
      sampledMiddleData.push(middleData[i])
    }

    // 合并首、中、尾数据
    return [...firstItems, ...sampledMiddleData, ...lastItems]
  }, [processedData])

  if (!data || data.length === 0) {
    return (
      <Box>
        <Typography variant="h6" sx={{ mb: 2 }}>
          {title}
        </Typography>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            textAlign: 'center',
            backgroundColor: 'rgba(0,0,0,0.02)',
            height: '300px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="text.secondary">暂无数据</Typography>
        </Paper>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, alignItems: 'center' }}>
        <Typography variant="h6">{title}</Typography>
        <IconButton
          size="small"
          onClick={() => exportToExcel(data, fileName)}
          sx={{
            transition: 'all 0.2s',
            '&:hover': {
              transform: 'scale(1.1)',
              color: theme.palette.primary.main,
            },
          }}
        >
          <DownloadIcon fontSize="small" />
        </IconButton>
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          gap: 3,
        }}
      >
        {/* NPS分数趋势图 */}
        <Paper
          sx={{
            width: '100%',
            borderRadius: 2,
            p: 2,
            flexGrow: 1,
            boxShadow: theme.shadows[1],
            '&:hover': {
              boxShadow: theme.shadows[3],
            },
          }}
        >
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 1 }}>
            NPS分数趋势
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={compressedData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 25,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
              <XAxis
                dataKey={xAxisKey}
                stroke={theme.palette.text.secondary}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatXAxis}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis
                stroke={theme.palette.text.secondary}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                domain={[-100, 100]}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="nps_score"
                name="NPS分数"
                stroke="#00A389"
                activeDot={{ r: 8 }}
                isAnimationActive={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </Paper>

        {/* NPS构成堆叠柱状图 */}
        <Paper
          sx={{
            width: '100%',
            borderRadius: 2,
            p: 2,
            flexGrow: 1,
            boxShadow: theme.shadows[1],
            '&:hover': {
              boxShadow: theme.shadows[3],
            },
          }}
        >
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 1 }}>
            NPS构成 - 推荐者/中立者/批评者占比
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={compressedData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 25,
              }}
              stackOffset="sign"
            >
              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
              <XAxis
                dataKey={xAxisKey}
                stroke={theme.palette.text.secondary}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatXAxis}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis
                stroke={theme.palette.text.secondary}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar
                dataKey="promoters_percentage"
                name="推荐者 (%)"
                stackId="stack"
                fill="#00A389"
                isAnimationActive={false}
              />
              <Bar
                dataKey="passives_percentage"
                name="中立者 (%)"
                stackId="stack"
                fill="#f1c40f"
                isAnimationActive={false}
              />
              <Bar
                dataKey="detractors_percentage"
                name="批评者 (%)"
                stackId="stack"
                fill="#E55246"
                isAnimationActive={false}
              />
            </BarChart>
          </ResponsiveContainer>
        </Paper>
      </Box>
    </Box>
  )
}
