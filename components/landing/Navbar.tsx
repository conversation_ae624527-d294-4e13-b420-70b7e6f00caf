'use client'

import { Box, Typography, Button, alpha, useTheme } from '@mui/material'
import { SignInButton, SignedIn, SignedOut } from '@clerk/nextjs'
import Link from 'next/link'

interface NavbarProps {
  onWaitlistClick: () => void
}

export default function Navbar({ onWaitlistClick }: NavbarProps) {
  const theme = useTheme()

  return (
    <Box
      component="nav"
      sx={{
        p: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backdropFilter: 'blur(8px)',
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        position: 'sticky',
        top: 0,
        zIndex: 100,
      }}
    >
      <Typography variant="h5" fontWeight="bold" color="primary">
        Chat Analytics
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <Button
          variant="outlined"
          color="primary"
          size="small"
          onClick={onWaitlistClick}
          sx={{
            borderRadius: 4,
            textTransform: 'none',
            px: 2,
          }}
        >
          加入等待名单
        </Button>
        <SignedOut>
          <Button 
            component={Link}
            href="/sign-in"
            variant="contained" 
            color="primary"
          >
            登录系统
          </Button>
        </SignedOut>
        <SignedIn>
          <Button 
            component={Link}
            href="/dashboard"
            variant="contained" 
            color="primary"
          >
            进入仪表盘
          </Button>
        </SignedIn>
      </Box>
    </Box>
  )
}
