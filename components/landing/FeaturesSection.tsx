'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  alpha,
  useTheme,
} from '@mui/material'
import { Psychology, BarChart, SupportAgent, AutoGraph, CheckCircle } from '@mui/icons-material'

export default function FeaturesSection() {
  const [animationsLoaded, setAnimationsLoaded] = useState(false)
  const theme = useTheme()

  useEffect(() => {
    setAnimationsLoaded(true)
  }, [])

  const features = [
    {
      title: '语义理解分析',
      description: '利用先进的自然语言处理技术，深入理解对话内容，捕捉客户真实意图和情感倾向。',
      icon: <Psychology sx={{ fontSize: 50 }} />,
      color: theme.palette.primary.main,
    },
    {
      title: '多维度数据可视化',
      description:
        '通过直观的图表和仪表盘，展示对话数据的多个维度，包括情绪分布、主题聚类和问题分类。',
      icon: <BarChart sx={{ fontSize: 50 }} />,
      color: theme.palette.secondary.main,
    },
    {
      title: '客户画像洞察',
      description:
        '自动生成详细的客户画像，包括兴趣偏好、行为模式和需求痛点，帮助您更好地理解目标客户。',
      icon: <SupportAgent sx={{ fontSize: 50 }} />,
      color: '#FF5722',
    },
    {
      title: '趋势预测和警报',
      description: '识别对话中的趋势变化，预测可能的问题和机会，并在关键指标异常时发出实时警报。',
      icon: <AutoGraph sx={{ fontSize: 50 }} />,
      color: '#00BCD4',
    },
  ]

  const benefits = ['基于大语言模型的高级分析', '提供详细的数据报告和导出', '持续优化的算法和模型']

  return (
    <Box
      sx={{
        py: 10,
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      }}
    >
      <Container maxWidth="xl">
        <Box
          sx={{
            textAlign: 'center',
            mb: 6,
            opacity: animationsLoaded ? 1 : 0,
            transform: animationsLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'opacity 0.8s ease-out, transform 0.8s ease-out',
          }}
        >
          <Typography variant="h3" fontWeight="bold" gutterBottom>
            强大的 AI 驱动分析能力
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
            我们的平台提供全方位的对话分析功能，帮助您深入理解客户互动
          </Typography>
        </Box>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
            gap: 4,
          }}
        >
          {features.map((feature, index) => (
            <Paper
              key={index}
              elevation={2}
              sx={{
                p: 4,
                height: '100%',
                borderRadius: 3,
                transition: 'transform 0.3s, box-shadow 0.3s',
                opacity: animationsLoaded ? 1 : 0,
                transform: animationsLoaded ? 'translateY(0)' : 'translateY(20px)',
                transitionDelay: `${0.2 + index * 0.1}s`,
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: theme.shadows[8],
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 2,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: alpha(feature.color, 0.1),
                  color: feature.color,
                }}
              >
                {feature.icon}
              </Box>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {feature.title}
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                {feature.description}
              </Typography>

              <List sx={{ mt: 2 }}>
                {benefits.map((point, i) => (
                  <ListItem key={i} sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <CheckCircle color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={point} />
                  </ListItem>
                ))}
              </List>
            </Paper>
          ))}
        </Box>
      </Container>
    </Box>
  )
}
