import { render, screen, fireEvent } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import '@testing-library/jest-dom'
import HeroSection from '../HeroSection'

// 创建测试主题
const theme = createTheme()

// 包装组件的助手函数
const renderWithTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>)
}

describe('HeroSection', () => {
  const mockOnWaitlistClick = jest.fn()

  beforeEach(() => {
    mockOnWaitlistClick.mockClear()
  })

  it('renders the main title correctly', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    expect(screen.getByText('AI 对话分析系统')).toBeInTheDocument()
  })

  it('renders the subtitle correctly', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    expect(screen.getByText('全方位解析客户对话，驱动业务增长')).toBeInTheDocument()
  })

  it('renders all feature cards', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    expect(screen.getByText('实时分析')).toBeInTheDocument()
    expect(screen.getByText('全面报告')).toBeInTheDocument()
    expect(screen.getByText('智能预测')).toBeInTheDocument()
    expect(screen.getByText('AI 驱动')).toBeInTheDocument()
  })

  it('calls onWaitlistClick when waitlist button is clicked', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    const waitlistButton = screen.getByText('申请使用')
    fireEvent.click(waitlistButton)

    expect(mockOnWaitlistClick).toHaveBeenCalledTimes(1)
  })

  it('renders the right side card with correct content', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    expect(screen.getByText('智能对话分析')).toBeInTheDocument()
    expect(
      screen.getByText('使用先进的AI技术，快速分析海量对话数据，获取深度洞察，提升客户体验。')
    ).toBeInTheDocument()
  })

  it('has proper responsive design classes', () => {
    renderWithTheme(<HeroSection onWaitlistClick={mockOnWaitlistClick} />)

    // 检查是否有响应式容器
    const container = screen.getByText('AI 对话分析系统').closest('[class*="MuiContainer"]')
    expect(container).toBeInTheDocument()
  })
})
