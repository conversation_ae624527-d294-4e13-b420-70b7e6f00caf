'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  alpha,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import { SignedIn, SignedOut } from '@clerk/nextjs'
import { Speed, BarChart, Psychology, Insights } from '@mui/icons-material'
import Link from 'next/link'

interface HeroSectionProps {
  onWaitlistClick: () => void
}

export default function HeroSection({ onWaitlistClick }: HeroSectionProps) {
  const [animationsLoaded, setAnimationsLoaded] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  useEffect(() => {
    setAnimationsLoaded(true)
  }, [])

  const features = [
    {
      title: '实时分析',
      desc: '对对话内容进行实时分析，获取即时洞察',
      icon: <Speed color="primary" />,
    },
    {
      title: '全面报告',
      desc: '生成详细的分析报告，支持多维度筛选',
      icon: <BarChart color="primary" />,
    },
    {
      title: '智能预测',
      desc: '预测客户需求和行为，提前做好准备',
      icon: <Insights color="primary" />,
    },
    {
      title: 'AI 驱动',
      desc: '采用先进的 AI 技术，不断优化分析模型',
      icon: <Psychology color="primary" />,
    },
  ]

  return (
    <Container maxWidth="xl" sx={{ mt: 8, mb: 10, px: { xs: 2, sm: 4, md: 6 } }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          gap: { xs: 4, md: 6 },
        }}
      >
        {/* 左侧文字内容 */}
        <Box
          sx={{
            flex: 1,
            pr: { md: 6 },
            opacity: animationsLoaded ? 1 : 0,
            transform: animationsLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'opacity 0.8s ease-out, transform 0.8s ease-out',
          }}
        >
          <Typography
            variant="h2"
            fontWeight="bold"
            gutterBottom
            sx={{
              background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 2,
              fontSize: { xs: '2rem', md: '3rem' },
            }}
          >
            AI 对话分析系统
          </Typography>

          <Typography
            variant="h5"
            color="text.secondary"
            gutterBottom
            sx={{ fontSize: { xs: '1.2rem', md: '1.5rem' } }}
          >
            全方位解析客户对话，驱动业务增长
          </Typography>

          <Typography variant="body1" sx={{ mb: 4 }}>
            我们的 AI
            对话分析平台可以深入挖掘客户对话数据，提供可行的洞察，帮助企业更好地理解客户需求，提升客户满意度和业务表现。
          </Typography>

          {/* CTA按钮 */}
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 6 }}>
            <SignedOut>
              <Button
                component={Link}
                href="/sign-in"
                variant="contained"
                color="primary"
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 'bold',
                  boxShadow: theme.shadows[4],
                }}
              >
                立即登录
              </Button>
            </SignedOut>
            <SignedIn>
              <Button
                component={Link}
                href="/dashboard"
                variant="contained"
                color="primary"
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 'bold',
                  boxShadow: theme.shadows[4],
                }}
              >
                进入仪表盘
              </Button>
            </SignedIn>
            <Button
              variant="outlined"
              color="primary"
              size="large"
              onClick={onWaitlistClick}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 'bold',
              }}
            >
              申请使用
            </Button>
          </Box>

          {/* 特点展示 */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'repeat(4, 1fr)' },
              gap: 2,
            }}
          >
            {features.map((item, index) => (
              <Paper
                key={index}
                elevation={0}
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  borderColor: alpha(theme.palette.primary.main, 0.2),
                  transition: 'all 0.3s',
                  opacity: animationsLoaded ? 1 : 0,
                  transform: animationsLoaded ? 'translateY(0)' : 'translateY(20px)',
                  transitionDelay: `${index * 0.1}s`,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[2],
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {item.icon}
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    ml={1}
                    sx={{ fontSize: { xs: '0.9rem', sm: '1.1rem' } }}
                  >
                    {item.title}
                  </Typography>
                </Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  {item.desc}
                </Typography>
              </Paper>
            ))}
          </Box>
        </Box>

        {/* 右侧可视化展示 */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            opacity: animationsLoaded ? 1 : 0,
            transform: animationsLoaded ? 'translateX(0)' : 'translateX(20px)',
            transition: 'opacity 0.8s ease-out, transform 0.8s ease-out',
            transitionDelay: '0.3s',
          }}
        >
          <Paper
            elevation={8}
            sx={{
              p: 4,
              borderRadius: 4,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              width: '100%',
              maxWidth: 500,
            }}
          >
            <Typography variant="h6" fontWeight="bold" mb={3} textAlign="center">
              实时数据洞察
            </Typography>

            {/* 模拟数据展示 */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {[
                { label: '对话总数', value: '12,345', color: theme.palette.primary.main },
                { label: '满意度', value: '94.2%', color: theme.palette.success.main },
                { label: '转化率', value: '23.8%', color: theme.palette.warning.main },
                { label: '响应时间', value: '1.2s', color: theme.palette.info.main },
              ].map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(item.color, 0.1),
                    border: `1px solid ${alpha(item.color, 0.2)}`,
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    {item.label}
                  </Typography>
                  <Typography variant="h6" fontWeight="bold" color={item.color}>
                    {item.value}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Box>
      </Box>
    </Container>
  )
}
