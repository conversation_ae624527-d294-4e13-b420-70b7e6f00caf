'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tab,
  Tabs,
  Box,
  Button,
  Alert,
  Skeleton,
  Chip,
  CircularProgress,

} from '@mui/material'
import DownloadIcon from '@mui/icons-material/Download'
import * as XLSX from 'xlsx'
import { fetchWithRetry } from '@/lib/fetchWithRetry'
import { ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon } from '@mui/icons-material'

interface CategoryProjectRanking {
  项目名称: string
  区域: string
  公司: string
  对话数量: number
  '该类问题占比(%)': number
  平均消息数: number
  '工单转化率(%)': number
  '满意度(%)': number
}

interface CategoryProjectRankingProps {
  startDate?: Date | null
  endDate?: Date | null
  selectedGroup?: string | null
  selectedArea?: string | null
  selectedCompany?: string | null
}

const categories = [
  { key: '基础设施类', name: '基础设施类', icon: '🏗️' },
  { key: '物业服务类', name: '物业服务类', icon: '🏢' },
  { key: '维修类服务', name: '维修类服务', icon: '🔧' },
  { key: '车辆与出入类', name: '车辆与出入类', icon: '🚗' },
  { key: '社区服务类', name: '社区服务类', icon: '🏠' },
  { key: '一般交互类', name: '一般交互类', icon: '💬' },
  { key: '异常情况', name: '异常情况', icon: '⚠️' },
]

export function CategoryProjectRanking({
  startDate,
  endDate,
  selectedGroup,
  selectedArea,
  selectedCompany,
}: CategoryProjectRankingProps) {
  const [currentCategory, setCurrentCategory] = useState(0)
  const [data, setData] = useState<{ [key: string]: any[] }>({})
  const [loading, setLoading] = useState(false)
  const [showMore, setShowMore] = useState<{ [key: string]: boolean }>({})

  const fetchData = useCallback(async (categoryName: string) => {
    if (data[categoryName] && data[categoryName].length > 0) return

    setLoading(true)
    try {
      const params = new URLSearchParams()
      params.append('type', 'category_project_ranking')
      params.append('category', categoryName)
      
      if (startDate) {
        params.append('startDate', startDate instanceof Date ? startDate.toISOString().split('T')[0] : String(startDate))
      }
      if (endDate) {
        params.append('endDate', endDate instanceof Date ? endDate.toISOString().split('T')[0] : String(endDate))
      }

      if (selectedGroup) params.append('group', selectedGroup)
      if (selectedArea) params.append('area', selectedArea)
      if (selectedCompany) params.append('company', selectedCompany)

      const response = await fetch(`/api/conversation-stats?${params}`)
      const result = await response.json()

      if (response.ok) {
        setData(prev => ({
          ...prev,
          [categoryName]: result.data || [],
        }))
      }
    } catch (error) {
      console.error('Error fetching category project ranking:', error)
    } finally {
      setLoading(false)
    }
  }, [startDate, endDate, selectedGroup, selectedArea, selectedCompany, data])

  useEffect(() => {
    const currentCategoryName = categories[currentCategory].name
    fetchData(currentCategoryName)
  }, [currentCategory, fetchData])

  const currentCategoryName = categories[currentCategory].name
  const currentData = data[currentCategoryName] || []
  
  // 分组显示：前10个作为默认显示，其余作为"查看更多"
  const defaultShowCount = 10
  const topData = currentData.slice(0, defaultShowCount)
  const remainingData = currentData.slice(defaultShowCount)
  const hasMoreData = remainingData.length > 0

  const handleToggleShowMore = () => {
    setShowMore(prev => ({
      ...prev,
      [currentCategoryName]: !prev[currentCategoryName]
    }))
  }

  const isShowingMore = showMore[currentCategoryName] || false

  const exportToExcel = () => {
    const currentCategoryName = categories[currentCategory].name
    const categoryData = data[currentCategoryName] || []
    
    if (categoryData.length === 0) {
      alert('没有数据可导出')
      return
    }

    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.json_to_sheet(categoryData)
    XLSX.utils.book_append_sheet(workbook, worksheet, `${currentCategoryName}项目排名`)
    XLSX.writeFile(workbook, `${currentCategoryName}_项目Top5排名.xlsx`)
  }

  return (
    <Card variant="outlined" sx={{ height: 'auto', boxShadow: 1 }}>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" fontWeight="medium" gutterBottom>
          问题分类项目Top排名
        </Typography>

        {/* 分类选择器 */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {categories.map((category, index) => (
              <Chip
                key={index}
                label={category.name}
                clickable
                color={index === currentCategory ? 'primary' : 'default'}
                onClick={() => setCurrentCategory(index)}
                sx={{
                  fontWeight: index === currentCategory ? 'bold' : 'normal',
                }}
              />
            ))}
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={32} />
          </Box>
        ) : currentData.length > 0 ? (
          <Box>
            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 'none' }}>
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ bgcolor: 'action.hover' }}>
                    <TableCell sx={{ fontWeight: 'bold', width: '60px' }}>排名</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>项目名称</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>区域</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>公司</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>对话数量</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>该类问题占比</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>平均消息数</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>工单转化率</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>满意度</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* 显示前10个 */}
                  {topData.map((row, index) => (
                    <TableRow key={index} hover>
                      <TableCell>
                        <Typography variant="h6" color="primary">
                          #{index + 1}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {row.项目名称}
                        </Typography>
                      </TableCell>
                      <TableCell>{row.区域 || '-'}</TableCell>
                      <TableCell>{row.公司 || '-'}</TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="medium">
                          {row.对话数量}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {row['该类问题占比(%)'] || 0}%
                      </TableCell>
                      <TableCell align="right">
                        {row.平均消息数 || 0}
                      </TableCell>
                      <TableCell align="right">
                        {row['工单转化率(%)'] || 0}%
                      </TableCell>
                      <TableCell align="right">
                        {row['满意度(%)'] || 0}%
                      </TableCell>
                    </TableRow>
                  ))}
                  
                  {/* 可展开的更多数据 */}
                  {isShowingMore && remainingData.map((row, index) => (
                      <TableRow key={index + defaultShowCount} hover>
                        <TableCell>
                          <Typography variant="h6" color="primary">
                            #{index + defaultShowCount + 1}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {row.项目名称}
                          </Typography>
                        </TableCell>
                        <TableCell>{row.区域 || '-'}</TableCell>
                        <TableCell>{row.公司 || '-'}</TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            {row.对话数量}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {row['该类问题占比(%)'] || 0}%
                        </TableCell>
                        <TableCell align="right">
                          {row.平均消息数 || 0}
                        </TableCell>
                        <TableCell align="right">
                          {row['工单转化率(%)'] || 0}%
                        </TableCell>
                        <TableCell align="right">
                          {row['满意度(%)'] || 0}%
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            {/* 查看更多按钮 */}
            {hasMoreData && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Button
                  onClick={handleToggleShowMore}
                  startIcon={isShowingMore ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  variant="outlined"
                  size="small"
                >
                  {isShowingMore ? `收起 (隐藏${remainingData.length}项)` : `查看更多 (还有${remainingData.length}项)`}
                </Button>
              </Box>
            )}
            
            {/* 统计信息 */}
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                共找到 {currentData.length} 个项目，当前显示 {isShowingMore ? currentData.length : Math.min(defaultShowCount, currentData.length)} 个
              </Typography>
            </Box>
          </Box>
        ) : (
          <Alert severity="info">
            当前筛选条件下，{categories[currentCategory].name}暂无足够数据（需要至少3条对话）
          </Alert>
        )}
      </CardContent>
    </Card>
  )
} 