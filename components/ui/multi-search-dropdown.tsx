'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Paper,
  List,
  ListItemButton,
  ListItemText,
  IconButton,
  Checkbox,
  Popper,
  Grow,
  MenuList,
  ClickAwayListener,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  useTheme,
} from '@mui/material'
import { Search as SearchIcon, Clear as ClearIcon } from '@mui/icons-material'

interface MultiSearchDropdownProps {
  options: string[]
  selectedValues: string[]
  onChange: (values: string[]) => void
  label: string
  placeholder?: string
}

export function MultiSearchDropdown({
  options,
  selectedValues,
  onChange,
  label,
  placeholder = '搜索...',
}: MultiSearchDropdownProps) {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const anchorRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Focus on search input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // Toggle option selection
  const handleToggle = (option: string) => {
    const newSelectedValues = selectedValues.includes(option)
      ? selectedValues.filter(value => value !== option)
      : [...selectedValues, option]

    onChange(newSelectedValues)
  }

  // Clear all selections
  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange([])
    setIsOpen(false)
  }

  return (
    <Box sx={{ mb: 2 }}>
      <FormControl fullWidth size="small">
        <InputLabel id={`${label}-label`}>
          {label} {selectedValues.length > 0 ? `(${selectedValues.length})` : '(全部)'}
        </InputLabel>
        <Select
          labelId={`${label}-label`}
          multiple
          value={selectedValues}
          onChange={e => onChange(e.target.value as string[])}
          input={
            <OutlinedInput
              label={`${label} ${selectedValues.length > 0 ? `(${selectedValues.length})` : '(全部)'}`}
            />
          }
          renderValue={selected => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {selected.length > 0 ? (
                selected.slice(0, 3).map(value => (
                  <Chip
                    key={value}
                    label={value}
                    size="small"
                    onDelete={e => {
                      e.stopPropagation()
                      onChange(selectedValues.filter(v => v !== value))
                    }}
                    onMouseDown={e => e.stopPropagation()}
                  />
                ))
              ) : (
                <Typography color="text.secondary">全部</Typography>
              )}
              {selected.length > 3 && (
                <Chip label={`+${selected.length - 3}个`} size="small" color="primary" />
              )}
            </Box>
          )}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
            },
          }}
        >
          <Box
            sx={{
              p: 1,
              position: 'sticky',
              top: 0,
              zIndex: 1,
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderColor: 'divider',
            }}
          >
            <TextField
              inputRef={inputRef}
              size="small"
              fullWidth
              placeholder={placeholder}
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              onClick={e => e.stopPropagation()}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchTerm('')} edge="end">
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null,
              }}
            />
          </Box>

          <MenuItem onClick={handleClearAll} sx={{ fontWeight: 'medium', color: 'text.secondary' }}>
            清除所有选择
          </MenuItem>

          <Divider />

          {filteredOptions.length > 0 ? (
            filteredOptions.map(option => (
              <MenuItem key={option} value={option} sx={{ display: 'flex', alignItems: 'center' }}>
                <Checkbox checked={selectedValues.includes(option)} size="small" sx={{ mr: 1 }} />
                <ListItemText primary={option} />
              </MenuItem>
            ))
          ) : (
            <Box sx={{ py: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                没有匹配结果
              </Typography>
            </Box>
          )}
        </Select>
      </FormControl>
    </Box>
  )
}
