import React from 'react'
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  Tooltip,
} from '@mui/material'
import { FileDownload as DownloadIcon } from '@mui/icons-material'
import * as XLSX from 'xlsx'

export interface Column {
  key: string
  header: string
}

export interface DataTableProps {
  data: any[]
  columns: Column[]
  title?: string
  fileName?: string
  maxHeight?: string
}

export function DataTable({ 
  data, 
  columns, 
  title, 
  fileName = 'data-export', 
  maxHeight = '350px' 
}: DataTableProps) {
  const handleExport = () => {
    try {
      const worksheet = XLSX.utils.json_to_sheet(data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Data')

      // Use UTF-8 encoding for proper character display
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      })

      // Create a download link and trigger click
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${fileName || 'data-export'}.xlsx`
      document.body.appendChild(link)
      link.click()

      // Cleanup
      setTimeout(() => {
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }, 100)
    } catch (error) {
      console.error('Error exporting data:', error)
    }
  }

  if (!data || data.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          width: '100%',
          minHeight: '200px',
        }}
      >
        <Typography variant="body1" color="text.secondary">
          暂无数据
        </Typography>
      </Box>
    )
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
          flexShrink: 0,
        }}
      >
        {title && (
          <Typography variant="h6" fontWeight="medium">
            {title}
          </Typography>
        )}
        <Tooltip title="导出到Excel">
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={handleExport}
          >
            导出
          </Button>
        </Tooltip>
      </Box>

      <TableContainer
        component={Paper}
        variant="outlined"
        sx={{
          flexGrow: 1,
          maxHeight: maxHeight,
          width: '100%',
          overflow: 'auto',
          backgroundColor: 'background.paper',
          borderRadius: 2,
          // 改善滚动条样式
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(0,0,0,0.1)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.3)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: 'rgba(0,0,0,0.5)',
            },
          },
        }}
      >
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              {columns.map(column => (
                <TableCell
                  key={column.key}
                  sx={{
                    fontWeight: 'bold',
                    backgroundColor: 'background.paper',
                    borderBottom: '2px solid',
                    borderBottomColor: 'divider',
                    position: 'sticky',
                    top: 0,
                    zIndex: 10,
                  }}
                >
                  {column.header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                hover
                sx={{
                  '&:nth-of-type(odd)': {
                    backgroundColor: 'action.hover',
                  },
                  transition: 'background-color 0.2s',
                }}
              >
                {columns.map(column => (
                  <TableCell key={`${rowIndex}-${column.key}`}>
                    {row[column.key] !== null && row[column.key] !== undefined 
                      ? row[column.key].toString()
                      : '-'
                    }
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* 显示数据条数 */}
      <Box sx={{ mt: 1, textAlign: 'center', flexShrink: 0 }}>
        <Typography variant="caption" color="text.secondary">
          共 {data.length} 条记录
        </Typography>
      </Box>
    </Box>
  )
}
