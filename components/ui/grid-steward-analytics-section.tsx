'use client'

import React, { useState, useCallback, useEffect } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Skeleton,
  <PERSON>ert,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
  Paper,
  CircularProgress,
} from '@mui/material'
import { format } from 'date-fns'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import PeopleIcon from '@mui/icons-material/People'
import GridOnIcon from '@mui/icons-material/GridOn'
import SchoolIcon from '@mui/icons-material/School'
import VerifiedIcon from '@mui/icons-material/Verified'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import WarningIcon from '@mui/icons-material/Warning'
import StarIcon from '@mui/icons-material/Star'
import AnalyticsIcon from '@mui/icons-material/Analytics'

interface GridStewardAnalyticsSectionProps {
  startDate?: Date | null
  endDate?: Date | null
  selectedGroup?: string | null
  selectedArea?: string | null
  selectedCompany?: string | null
  selectedProject?: string | null
}

interface AnalyticsData {
  total_active_grids: number
  total_active_stewards: number
  avg_conversations_per_grid: number
  avg_conversations_per_steward: number
  certification_analysis: any[]
  experience_analysis: any[]
  region_analysis: any[]
  difficult_grid_analysis: any[]
  mentor_analysis: any[]
  top_performing_grids: any[]
  top_performing_stewards: any[]
}

export function GridStewardAnalyticsSection({
  startDate,
  endDate,
  selectedGroup,
  selectedArea,
  selectedCompany,
  selectedProject,
}: GridStewardAnalyticsSectionProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)

  const fetchAnalyticsData = useCallback(async () => {
    setIsLoading(true)
    setError('')
    
    try {
      const params = new URLSearchParams()
      if (startDate) params.append('startDate', startDate.toISOString().split('T')[0])
      if (endDate) params.append('endDate', endDate.toISOString().split('T')[0])
      if (selectedGroup) params.append('group', selectedGroup)
      if (selectedArea) params.append('area', selectedArea)
      if (selectedCompany) params.append('company', selectedCompany)
      if (selectedProject) params.append('project', selectedProject)

      const response = await fetch(`/api/grid-steward-analytics?${params}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      setAnalyticsData(result.data)
    } catch (err: any) {
      setError(err.message || 'Failed to load analytics data')
    } finally {
      setIsLoading(false)
    }
  }, [startDate, endDate, selectedGroup, selectedArea, selectedCompany, selectedProject])

  useEffect(() => {
    fetchAnalyticsData()
  }, [fetchAnalyticsData])

  const calculateRate = (numerator: number, denominator: number) => {
    return denominator > 0 ? ((numerator / denominator) * 100).toFixed(1) : '0.0'
  }

  const getCertificationRate = () => {
    if (!analyticsData?.certification_analysis) return 0
    const certifiedStewards = analyticsData.certification_analysis.find(item => item.certification_status === '是')?.steward_count || 0
    const totalStewards = analyticsData.certification_analysis.reduce((sum, item) => sum + item.steward_count, 0)
    return totalStewards > 0 ? Math.round((certifiedStewards / totalStewards) * 100) : 0
  }

  const getMentorCount = () => {
    return analyticsData?.mentor_analysis?.find(item => item.is_mentor === '是')?.steward_count || 0
  }

  const getDifficultGridCount = () => {
    return analyticsData?.difficult_grid_analysis?.find(item => item.is_difficult_grid === '是')?.grid_count || 0
  }

  if (isLoading) {
    return (
      <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
        <CardContent sx={{ p: { xs: 3, md: 4 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
        <CardContent sx={{ p: { xs: 3, md: 4 } }}>
          <Alert severity="error" sx={{ my: 2 }}>
            加载网格管家分析数据失败: {error}
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
        <CardContent sx={{ p: { xs: 3, md: 4 } }}>
          <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
            🏠 高级网格管家分析系统
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3} textAlign="center">
            基于万科服务网格映射表的深度分析，包含认证、岗龄、区域、困难网格和导师体系等多维度分析
          </Typography>

          {analyticsData ? (
            <>
              {/* 关键指标卡片 */}
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr 1fr', md: '1fr 1fr 1fr 1fr' }, gap: 2, mb: 3 }}>
                <Card
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: 'primary.50',
                    borderColor: 'primary.200',
                    transition: 'transform 0.3s',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <GridOnIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">活跃网格</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="primary.main">
                    {analyticsData.total_active_grids || 0}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    平均 {analyticsData.avg_conversations_per_grid || 0} 对话/网格
                  </Typography>
                </Card>

                <Card
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: 'success.50',
                    borderColor: 'success.200',
                    transition: 'transform 0.3s',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PeopleIcon color="success" sx={{ mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">活跃管家</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {analyticsData.total_active_stewards || 0}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    平均 {analyticsData.avg_conversations_per_steward || 0} 对话/管家
                  </Typography>
                </Card>

                <Card
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: 'warning.50',
                    borderColor: 'warning.200',
                    transition: 'transform 0.3s',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <VerifiedIcon color="warning" sx={{ mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">认证率</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {getCertificationRate()}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    已通过认证的管家占比
                  </Typography>
                </Card>

                <Card
                  variant="outlined"
                  sx={{
                    p: 2,
                    bgcolor: 'info.50',
                    borderColor: 'info.200',
                    transition: 'transform 0.3s',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 2 },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <SchoolIcon color="info" sx={{ mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">导师管家</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    {getMentorCount()}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    担任导师角色的管家
                  </Typography>
                </Card>
              </Box>

              {/* 特殊分析区域 */}
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3, mb: 3 }}>
                {/* 困难网格分析 */}
                <Card variant="outlined" sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <WarningIcon color="error" sx={{ mr: 1 }} />
                    <Typography variant="h6" fontWeight="600">困难网格分析</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="error.main" mb={1}>
                    {getDifficultGridCount()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    困难网格数量，需要重点关注和支持
                  </Typography>
                </Card>

                {/* 区域分布 */}
                <Card variant="outlined" sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationOnIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6" fontWeight="600">区域分布</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold" color="primary.main" mb={1}>
                    {analyticsData.region_analysis?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    覆盖的区域数量，实现全域服务
                  </Typography>
                </Card>
              </Box>

              {/* 操作按钮 */}
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AnalyticsIcon />}
                  onClick={() => setDetailDialogOpen(true)}
                  size="large"
                >
                  查看详细分析
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  href="/grid-steward-analytics"
                  size="large"
                >
                  进入专业分析页面
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    const baseUrl = window.location.origin
                    const apiUrl = `${baseUrl}/api/grid-steward-analytics`
                    window.open(apiUrl, '_blank')
                  }}
                >
                  查看API数据
                </Button>
                <Button
                  variant="outlined"
                  color="info"
                  onClick={() => {
                    const baseUrl = window.location.origin
                    const dbSchemaUrl = `${baseUrl}/api/db-schema`
                    window.open(dbSchemaUrl, '_blank')
                  }}
                >
                  查看数据库结构
                </Button>
              </Box>
            </>
          ) : (
            <Alert severity="info" sx={{ my: 2 }}>
              暂无网格管家分析数据
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 详细分析对话框 */}
      <Dialog 
        open={detailDialogOpen} 
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="600">
            🏠 网格管家详细分析报告
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {startDate && endDate
              ? `${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`
              : '全部时间范围'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {analyticsData && (
            <Box sx={{ py: 2 }}>
              {/* 认证分析 */}
              <Typography variant="h6" fontWeight="600" mb={2} sx={{ display: 'flex', alignItems: 'center' }}>
                <VerifiedIcon sx={{ mr: 1 }} />
                管家认证分析
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 4 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>认证状态</TableCell>
                      <TableCell align="right">管家数量</TableCell>
                      <TableCell align="right">总对话数</TableCell>
                      <TableCell align="right">工单转化率</TableCell>
                      <TableCell align="right">满意率</TableCell>
                      <TableCell align="right">平均岗龄</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.certification_analysis?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Chip 
                            label={item.certification_status === '是' ? '已认证' : '未认证'}
                            color={item.certification_status === '是' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">{item.steward_count}</TableCell>
                        <TableCell align="right">{item.total_conversations}</TableCell>
                        <TableCell align="right">
                          {calculateRate(item.work_order_count, item.total_conversations)}%
                        </TableCell>
                        <TableCell align="right">
                          {calculateRate(item.satisfied_count, item.total_survey_count)}%
                        </TableCell>
                        <TableCell align="right">{item.avg_position_years || 0}年</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* 岗龄分析 */}
              <Typography variant="h6" fontWeight="600" mb={2} sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 1 }} />
                岗龄效率分析
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 4 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>岗龄级别</TableCell>
                      <TableCell align="right">管家数量</TableCell>
                      <TableCell align="right">总对话数</TableCell>
                      <TableCell align="right">工单转化率</TableCell>
                      <TableCell align="right">满意率</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.experience_analysis?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Chip 
                            label={item.experience_level}
                            color={
                              item.experience_level.includes('专家') ? 'success' :
                              item.experience_level.includes('资深') ? 'primary' :
                              item.experience_level.includes('熟练') ? 'warning' : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">{item.steward_count}</TableCell>
                        <TableCell align="right">{item.total_conversations}</TableCell>
                        <TableCell align="right">
                          {calculateRate(item.work_order_count, item.total_conversations)}%
                        </TableCell>
                        <TableCell align="right">
                          {calculateRate(item.satisfied_count, item.total_survey_count)}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* 顶级表现管家 */}
              <Typography variant="h6" fontWeight="600" mb={2} sx={{ display: 'flex', alignItems: 'center' }}>
                <StarIcon sx={{ mr: 1 }} />
                顶级表现管家 (Top 10)
              </Typography>
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>管家姓名</TableCell>
                      <TableCell align="right">对话数</TableCell>
                      <TableCell align="right">管理网格</TableCell>
                      <TableCell align="right">满意度</TableCell>
                      <TableCell align="right">工单率</TableCell>
                      <TableCell align="right">线索率</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.top_performing_stewards?.slice(0, 10).map((steward: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2" fontWeight={500}>
                            {steward.steward_real_name || steward.agent_name}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{steward.conversation_count}</TableCell>
                        <TableCell align="right">{steward.managed_grids}</TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={`${steward.satisfaction_rate || 0}%`}
                            size="small"
                            color={steward.satisfaction_rate >= 80 ? 'success' : steward.satisfaction_rate >= 60 ? 'warning' : 'error'}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={`${steward.work_order_rate || 0}%`}
                            size="small"
                            color={steward.work_order_rate >= 20 ? 'success' : steward.work_order_rate >= 10 ? 'warning' : 'default'}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={`${steward.lead_rate || 0}%`}
                            size="small"
                            color={steward.lead_rate >= 5 ? 'success' : steward.lead_rate >= 2 ? 'warning' : 'default'}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>关闭</Button>
          <Button 
            variant="contained"
            onClick={() => {
              const baseUrl = window.location.origin
              const apiUrl = `${baseUrl}/api/grid-steward-analytics`
              window.open(apiUrl, '_blank')
            }}
          >
            查看完整API数据
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
} 