import React, { useState, useEffect } from 'react'
import { InactiveProjectsData } from '@/hooks/use-analytics-data'
import * as XLSX from 'xlsx'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Chip,
  CircularProgress,
  Tooltip,
  InputBase,
  InputAdornment,
  useTheme,
  alpha,
} from '@mui/material'
import {
  Search as SearchIcon,
  CloudDownload as DownloadIcon,
  NoAccounts as InactiveIcon,
  CalendarMonth as DateIcon,
  Refresh as RefreshIcon,
  Business as BusinessIcon,
  Source as ProjectIcon,
  ArrowDownward as ArrowDownwardIcon,
  ArrowUpward as ArrowUpwardIcon,
} from '@mui/icons-material'

interface InactiveProjectListProps {
  data: InactiveProjectsData[]
  totalCount: number
  isLoading: boolean
  hasMore: boolean
  onLoadMore: () => void
  isLoadingMore: boolean
}

export function InactiveProjectList({
  data,
  totalCount,
  isLoading,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: InactiveProjectListProps) {
  const theme = useTheme()
  const [search, setSearch] = useState('')
  const [searchResults, setSearchResults] = useState<InactiveProjectsData[]>([])
  const [sortField, setSortField] = useState<string>('last_active')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [showAll, setShowAll] = useState(false)

  // Filter data based on search term
  useEffect(() => {
    if (!search.trim()) {
      setSearchResults(data)
      return
    }

    const lowercasedSearch = search.toLowerCase()
    const filtered = data.filter(project => {
      return (
        project.project_name.toLowerCase().includes(lowercasedSearch) ||
        project.company.toLowerCase().includes(lowercasedSearch)
      )
    })
    setSearchResults(filtered)
  }, [search, data])

  // Sort data based on sort field and direction
  const sortedData = React.useMemo(() => {
    return [...searchResults].sort((a, b) => {
      let aValue = (a as any)[sortField]
      let bValue = (b as any)[sortField]

      // Special handling for last_active dates
      if (sortField === 'last_active') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }, [searchResults, sortField, sortDirection])

  // Only show first 10 items unless showAll is true
  const displayedData = showAll ? sortedData : sortedData.slice(0, 10)

  const handleSortClick = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(searchResults)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'InactiveProjects')

    // Generate filename with date
    const today = new Date()
    const dateStr = today.toISOString().split('T')[0]
    XLSX.writeFile(workbook, `inactive-projects-${dateStr}.xlsx`)
  }

  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null
    return sortDirection === 'asc' ? (
      <ArrowUpwardIcon fontSize="small" />
    ) : (
      <ArrowDownwardIcon fontSize="small" />
    )
  }

  return (
    <Card variant="outlined" sx={{ boxShadow: 1 }}>
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InactiveIcon sx={{ color: theme.palette.warning.main, mr: 1, fontSize: '1.75rem' }} />
            <Typography variant="h5" fontWeight="600">
              非活跃项目列表
            </Typography>
            <Chip
              label={`共 ${totalCount} 个项目`}
              size="small"
              sx={{
                ml: 1.5,
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                fontWeight: 'medium',
              }}
            />
          </Box>
          <Box sx={{ display: 'flex', gap: 1.5 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<DownloadIcon />}
              onClick={exportToExcel}
              sx={{
                borderRadius: 2,
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 1,
                },
              }}
            >
              导出Excel
            </Button>
          </Box>
        </Box>

        {/* Search bar */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <Paper
            sx={{
              p: 1,
              display: 'flex',
              alignItems: 'center',
              width: 350,
              boxShadow: 1,
              borderRadius: 2,
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1 }}
              placeholder="搜索项目"
              value={search}
              onChange={e => setSearch(e.target.value)}
              inputProps={{ 'aria-label': '搜索项目' }}
              startAdornment={
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              }
            />
          </Paper>
          <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
            当前显示: {searchResults.length} 个项目
          </Typography>
        </Box>

        {/* Data table */}
        <TableContainer
          component={Paper}
          elevation={0}
          sx={{
            border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
            borderRadius: 2,
            minHeight: 300,
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow
                sx={{
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  '& th': { fontWeight: 'bold' },
                }}
              >
                <TableCell
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleSortClick('project_name')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ProjectIcon sx={{ fontSize: '1.1rem', mr: 0.5 }} />
                    项目名称
                    {renderSortIcon('project_name')}
                  </Box>
                </TableCell>
                <TableCell sx={{ cursor: 'pointer' }} onClick={() => handleSortClick('company')}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BusinessIcon sx={{ fontSize: '1.1rem', mr: 0.5 }} />
                    所属公司
                    {renderSortIcon('company')}
                  </Box>
                </TableCell>
                <TableCell
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleSortClick('last_active')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DateIcon sx={{ fontSize: '1.1rem', mr: 0.5 }} />
                    最后活跃日期
                    {renderSortIcon('last_active')}
                  </Box>
                </TableCell>
                <TableCell
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleSortClick('conversation_count')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ fontSize: '1.1rem', mr: 0.5 }}>
                      💬
                    </Box>
                    对话总数
                    {renderSortIcon('conversation_count')}
                  </Box>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center" sx={{ py: 5 }}>
                    <CircularProgress size={40} />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                      加载数据中...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : displayedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center" sx={{ py: 5 }}>
                    <Typography variant="body1">无非活跃项目数据</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                <>
                  {displayedData.map((project, index) => (
                    <TableRow
                      key={`${project.project_name}-${index}`}
                      hover
                      sx={{
                        '&:nth-of-type(odd)': {
                          bgcolor: alpha(theme.palette.background.default, 0.4),
                        },
                        transition: 'background-color 0.2s',
                      }}
                    >
                      <TableCell>
                        <Tooltip title={project.project_name}>
                          <Typography
                            variant="body2"
                            component="div"
                            noWrap
                            sx={{
                              maxWidth: 200,
                              fontWeight: 'medium',
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            <ProjectIcon
                              fontSize="small"
                              sx={{
                                mr: 0.75,
                                color: theme.palette.warning.main,
                                opacity: 0.8,
                              }}
                            />
                            {project.project_name}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={project.company}
                          size="small"
                          sx={{
                            fontSize: '0.75rem',
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <DateIcon
                            fontSize="small"
                            sx={{
                              mr: 0.5,
                              color: theme.palette.error.main,
                              opacity: 0.7,
                            }}
                          />
                          <Typography variant="body2">{project.last_active}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={project.conversation_count}
                          size="small"
                          sx={{
                            fontWeight: 'medium',
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Load more */}
        {hasMore && !isLoading && (
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button
              variant="outlined"
              onClick={onLoadMore}
              disabled={isLoadingMore}
              startIcon={isLoadingMore ? <CircularProgress size={20} /> : <RefreshIcon />}
              sx={{ px: 3 }}
            >
              {isLoadingMore ? '加载中...' : '加载更多'}
            </Button>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              已加载 {displayedData.length} / {totalCount} 个项目
            </Typography>
          </Box>
        )}

        {/* Show All button */}
        {sortedData.length > 10 && !isLoading && (
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button variant="outlined" onClick={() => setShowAll(!showAll)} sx={{ px: 3 }}>
              {showAll ? '收起' : '显示全部'}
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}
