import { useState } from 'react'
import { ProjectRanking } from '@/hooks/use-analytics-data'
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  IconButton,
  Tooltip,
  alpha,
  useTheme,
  Chip,
} from '@mui/material'
import {
  ArrowDownward as ArrowDownIcon,
  ArrowUpward as ArrowUpIcon,
  ImportExport as SortIcon,
  KeyboardArrowDown as ChevronDownIcon,
  KeyboardArrowUp as ChevronUpIcon,
  FileDownload as DownloadIcon,
} from '@mui/icons-material'

// Column configuration with icons
const COLUMNS = [
  { id: 'project', label: '项目', icon: '🏘️', tooltip: '项目名称' },
  { id: 'conversation_count', label: '对话量', icon: '💬', tooltip: '项目总对话数量' },
  { id: 'avg_messages', label: '消息均值', icon: '📊', tooltip: '每次对话的平均消息数量' },
  { id: 'work_order_rate', label: '工单转化率', icon: '📋', tooltip: '对话转化为工单的百分比' },
  { id: 'lead_rate', label: '线索转化率', icon: '🔍', tooltip: '对话转化为销售线索的百分比' },
  { id: 'satisfaction_rate', label: '满意度', icon: '😊', tooltip: '用户满意度评分百分比' },
]

interface ProjectRankingTableProps {
  data: ProjectRanking[]
  title: string
  fileName: string
}

export function ProjectRankingTable({ data, title, fileName }: ProjectRankingTableProps) {
  const theme = useTheme()
  const [sortColumn, setSortColumn] = useState<string>('conversation_count')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [isExpanded, setIsExpanded] = useState(false)

  // Handles sorting when a column header is clicked
  function handleSort(column: string) {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(column)
      setSortDirection('desc')
    }
  }

  // Sort the data based on current sort column and direction
  const sortedData = [...data].sort((a, b) => {
    const aValue = a[sortColumn as keyof ProjectRanking]
    const bValue = b[sortColumn as keyof ProjectRanking]

    if (aValue === bValue) return 0

    const modifier = sortDirection === 'asc' ? 1 : -1
    return aValue > bValue ? modifier : -modifier
  })

  // Get the display data based on expanded state
  const displayData = isExpanded ? sortedData : sortedData.slice(0, 10)

  // Helper function to export data to CSV
  const exportToExcel = () => {
    import('xlsx').then(xlsx => {
      // 格式化数据为Excel格式
      const excelData = sortedData.map(row => ({
        项目: row.project,
        对话量: row.conversation_count,
        消息均值: row.avg_messages,
        '工单转化率(%)': row.work_order_rate,
        '线索转化率(%)': row.lead_rate,
        '满意度(%)': row.satisfaction_rate,
        对话量排名: row.volume_rank,
        满意度排名: row.satisfaction_rank,
        工单转化排名: row.work_order_rank,
        线索转化排名: row.lead_rank,
        效率排名: row.efficiency_rank,
      }))

      const worksheet = xlsx.utils.json_to_sheet(excelData)
      const workbook = xlsx.utils.book_new()
      xlsx.utils.book_append_sheet(workbook, worksheet, '项目表现排名')
      xlsx.writeFile(workbook, `${fileName}-${new Date().toISOString().slice(0, 10)}.xlsx`, {
        bookType: 'xlsx',
        type: 'buffer',
        bookSST: false,
        compression: true,
      })
    })
  }

  // Function to render sort indicator next to column header
  const renderSortIndicator = (column: string) => {
    if (sortColumn !== column) return <SortIcon fontSize="small" color="action" />
    return sortDirection === 'asc' ? (
      <ArrowUpIcon fontSize="small" color="primary" />
    ) : (
      <ArrowDownIcon fontSize="small" color="primary" />
    )
  }

  // Function to get cell color based on value (for percentage values)
  const getValueColor = (value: number, columnId: string) => {
    if (!['satisfaction_rate', 'work_order_rate', 'lead_rate'].includes(columnId)) return undefined

    if (value >= 80) return theme.palette.success.main
    if (value >= 60) return theme.palette.warning.main
    if (value < 40) return theme.palette.error.main
    return theme.palette.text.primary
  }

  return (
    <Paper
      elevation={1}
      sx={{
        mb: 3,
        p: 3,
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: 3,
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
        }}
      >
        <Typography variant="h6" fontWeight="600" sx={{ display: 'flex', alignItems: 'center' }}>
          <Box component="span" sx={{ mr: 1, fontSize: '1.5rem' }}>
            📋
          </Box>
          {title}
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<DownloadIcon />}
          onClick={exportToExcel}
          sx={{
            borderRadius: 4,
            px: 2,
            transition: 'all 0.2s',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: 1,
            },
          }}
        >
          导出Excel
        </Button>
      </Box>

      <TableContainer
        component={Paper}
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        }}
      >
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
              {COLUMNS.map(column => (
                <TableCell
                  key={column.id}
                  onClick={() => handleSort(column.id)}
                  align={column.id === 'project' ? 'left' : 'right'}
                  sx={{
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    py: 1.5,
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                    },
                  }}
                >
                  <Tooltip title={column.tooltip}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: column.id === 'project' ? 'flex-start' : 'flex-end',
                      }}
                    >
                      {column.id === 'project' && (
                        <Box component="span" sx={{ mr: 0.5, fontSize: '1.2rem' }}>
                          {column.icon}
                        </Box>
                      )}
                      {column.label}
                      {column.id !== 'project' && (
                        <Box component="span" sx={{ ml: 0.5, fontSize: '1.2rem' }}>
                          {column.icon}
                        </Box>
                      )}
                      <IconButton size="small" sx={{ ml: 0.5 }}>
                        {renderSortIndicator(column.id)}
                      </IconButton>
                    </Box>
                  </Tooltip>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {displayData.map((row, index) => (
              <TableRow
                key={row.project}
                sx={{
                  '&:nth-of-type(odd)': { bgcolor: alpha(theme.palette.background.default, 0.4) },
                  '&:hover': {
                    bgcolor: alpha(theme.palette.action.hover, 0.7),
                    cursor: 'pointer',
                  },
                  transition: 'background-color 0.2s',
                }}
              >
                <TableCell
                  sx={{
                    fontWeight: 'medium',
                    py: 1.5,
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Chip
                      label={`#${row.volume_rank}`}
                      size="small"
                      sx={{
                        minWidth: 40,
                        mr: 1,
                        bgcolor:
                          row.volume_rank <= 3
                            ? alpha(theme.palette.success.main, 0.1)
                            : alpha(theme.palette.grey[500], 0.1),
                        color:
                          row.volume_rank <= 3
                            ? theme.palette.success.main
                            : theme.palette.text.secondary,
                        fontWeight: 'bold',
                      }}
                    />
                    {row.project}
                  </Box>
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'medium' }}>
                  {row.conversation_count}
                </TableCell>
                <TableCell align="right">{row.avg_messages}</TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.work_order_rate, 'work_order_rate'),
                    fontWeight: 'medium',
                  }}
                >
                  {row.work_order_rate}%
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.lead_rate, 'lead_rate'),
                    fontWeight: 'medium',
                  }}
                >
                  {row.lead_rate}%
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.satisfaction_rate, 'satisfaction_rate'),
                    fontWeight: 'medium',
                  }}
                >
                  {row.satisfaction_rate}%
                </TableCell>
              </TableRow>
            ))}

            {displayData.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                  <Typography color="text.secondary">暂无数据</Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        {sortedData.length > 10 && (
          <Box sx={{ mt: 2, textAlign: 'center', pb: 2, pt: 1 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => setIsExpanded(!isExpanded)}
              endIcon={isExpanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
              sx={{
                borderRadius: 4,
                px: 2,
                transition: 'all 0.2s',
                '&:hover': {
                  transform: isExpanded ? 'translateY(2px)' : 'translateY(-2px)',
                  boxShadow: 1,
                },
              }}
            >
              {isExpanded ? '收起列表' : '查看更多'}
            </Button>
          </Box>
        )}
      </TableContainer>
    </Paper>
  )
}
