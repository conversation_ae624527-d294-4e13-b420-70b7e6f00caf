'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Paper,
  List,
  ListItemButton,
  ListItemText,
  IconButton,
  Popper,
  Grow,
  ClickAwayListener,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
} from '@mui/material'
import { Search as SearchIcon, Clear as ClearIcon } from '@mui/icons-material'

interface SearchDropdownProps {
  options: string[]
  value: string | null
  onChange: (value: string | null) => void
  label: string
  placeholder?: string
}

export function SearchDropdown({
  options,
  value,
  onChange,
  label,
  placeholder = '搜索...',
}: SearchDropdownProps) {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const anchorRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Focus on search input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // Handle option selection
  const handleSelect = (option: string) => {
    onChange(option)
    setSearchTerm('')
    setIsOpen(false)
  }

  // Clear selection
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(null)
    setSearchTerm('')
  }

  return (
    <Box sx={{ mb: 2 }}>
      <FormControl fullWidth variant="outlined" size="small">
        <InputLabel id={`${label}-label`}>{label}</InputLabel>
        <Select
          labelId={`${label}-label`}
          value={value || ''}
          label={label}
          displayEmpty
          ref={anchorRef}
          open={isOpen}
          onOpen={() => setIsOpen(true)}
          onClose={() => setIsOpen(false)}
          renderValue={selected =>
            selected ? selected : <Typography color="text.secondary">全部</Typography>
          }
          endAdornment={
            value ? (
              <InputAdornment position="end">
                <IconButton size="small" onClick={handleClear} edge="end" sx={{ mr: 0.5 }}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ) : null
          }
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
            },
          }}
        >
          <Box
            sx={{
              p: 1,
              position: 'sticky',
              top: 0,
              zIndex: 1,
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderColor: 'divider',
            }}
          >
            <TextField
              inputRef={inputRef}
              size="small"
              fullWidth
              placeholder={placeholder}
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              onClick={e => e.stopPropagation()}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchTerm('')} edge="end">
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null,
              }}
            />
          </Box>

          <MenuItem value="" sx={{ fontWeight: value === null ? 'medium' : 'normal' }}>
            全部
          </MenuItem>

          <Divider />

          {filteredOptions.length > 0 ? (
            filteredOptions.map(option => (
              <MenuItem key={option} value={option} selected={option === value}>
                {option}
              </MenuItem>
            ))
          ) : (
            <Box sx={{ py: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                没有匹配结果
              </Typography>
            </Box>
          )}
        </Select>
      </FormControl>
    </Box>
  )
}
