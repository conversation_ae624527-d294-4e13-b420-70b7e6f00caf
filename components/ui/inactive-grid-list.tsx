import React, { useState, useEffect } from 'react'
import { InactiveGridsData } from '@/hooks/use-analytics-data'
import * as XLSX from 'xlsx'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Chip,
  CircularProgress,
  Tooltip,
  InputBase,
  InputAdornment,
  useTheme,
  alpha,
} from '@mui/material'
import {
  Search as SearchIcon,
  CloudDownload as DownloadIcon,
  NoAccounts as InactiveIcon,
  CalendarMonth as DateIcon,
  Refresh as RefreshIcon,
  Business as BusinessIcon,
  Source as ProjectIcon,
  ArrowDownward as ArrowDownwardIcon,
  ArrowUpward as ArrowUpwardIcon,
  Grid4x4 as GridIcon,
} from '@mui/icons-material'

interface InactiveGridListProps {
  data: InactiveGridsData[]
  totalCount: number
  isLoading: boolean
  hasMore: boolean
  onLoadMore: () => void
  isLoadingMore: boolean
}

export function InactiveGridList({
  data,
  totalCount,
  isLoading,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: InactiveGridListProps) {
  const theme = useTheme()
  const [searchTerm, setSearchTerm] = useState('')
  const [showAll, setShowAll] = useState(false)
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  }>({ key: 'last_active', direction: 'descending' }) // Default sort by last active time

  // 过滤网格数据
  const filteredGrids = data.filter(grid => {
    const searchLower = searchTerm.toLowerCase()
    return (
      grid.grid_name?.toLowerCase().includes(searchLower) ||
      grid.grid_code?.toLowerCase().includes(searchLower) ||
      grid.project_name?.toLowerCase().includes(searchLower) ||
      grid.company?.toLowerCase().includes(searchLower)
    )
  })

  // 排序功能
  const sortedGrids = React.useMemo(() => {
    let sortableItems = [...filteredGrids]
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        // 处理不同字段的排序
        let aValue: any = (a as any)[sortConfig.key]
        let bValue: any = (b as any)[sortConfig.key]

        // 处理日期字段的特殊排序
        if (sortConfig.key === 'last_active') {
          if (!aValue || aValue === '从未活跃') return sortConfig.direction === 'ascending' ? 1 : -1
          if (!bValue || bValue === '从未活跃') return sortConfig.direction === 'ascending' ? -1 : 1
        }

        // 数字字段的排序
        if (sortConfig.key === 'conversation_count') {
          aValue = Number(aValue || 0)
          bValue = Number(bValue || 0)
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1
        }
        return 0
      })
    }
    return sortableItems
  }, [filteredGrids, sortConfig])

  // 计算显示的网格数量
  const displayedGrids = showAll ? sortedGrids : sortedGrids.slice(0, 10)

  // 排序切换
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending'
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending'
    } else if (sortConfig && sortConfig.key === key && sortConfig.direction === 'descending') {
      // 第三次点击同一列时切换回升序
      direction = 'ascending'
    }
    setSortConfig({ key, direction })
  }

  // 获取排序图标
  const getSortIcon = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) {
      return null
    }
    return sortConfig.direction === 'ascending' ? (
      <ArrowUpwardIcon fontSize="small" sx={{ ml: 0.5, fontSize: '0.9rem' }} />
    ) : (
      <ArrowDownwardIcon fontSize="small" sx={{ ml: 0.5, fontSize: '0.9rem' }} />
    )
  }

  // 导出为Excel
  const exportToExcel = () => {
    if (filteredGrids.length === 0) return

    const dataToExport = filteredGrids.map(grid => ({
      网格名称: grid.grid_name,
      网格编号: grid.grid_code,
      所属项目: grid.project_name,
      所属公司: grid.company,
      最后活跃时间: grid.last_active,
      历史对话数: grid.conversation_count,
    }))

    const worksheet = XLSX.utils.json_to_sheet(dataToExport)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '非活跃网格')

    // 生成带日期的文件名
    const today = new Date()
    const dateStr = today.toISOString().split('T')[0]
    XLSX.writeFile(workbook, `非活跃网格列表-${dateStr}.xlsx`)
  }

  return (
    <Card sx={{ width: '100%', overflow: 'hidden', p: 0 }}>
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 2,
            mb: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InactiveIcon
              sx={{
                mr: 1,
                color: theme.palette.warning.main,
                fontSize: '1.75rem',
              }}
            />
            <Typography variant="h6" fontWeight="bold">
              非活跃网格
            </Typography>

            <Tooltip title="最近一段时间没有对话数据的网格">
              <Chip
                size="small"
                label={`${totalCount || 0}个`}
                sx={{
                  ml: 1,
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  color: theme.palette.warning.main,
                  fontWeight: 'medium',
                }}
              />
            </Tooltip>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
            {/* 搜索框 */}
            <Paper
              sx={{
                p: '2px 4px',
                display: 'flex',
                alignItems: 'center',
                width: { xs: '100%', sm: 240 },
              }}
            >
              <InputBase
                sx={{ ml: 1, flex: 1 }}
                placeholder="搜索网格/项目..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                startAdornment={
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                }
              />
            </Paper>

            {/* 导出按钮 */}
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              disabled={filteredGrids.length === 0}
              onClick={exportToExcel}
              size="small"
            >
              导出
            </Button>
          </Box>
        </Box>

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={40} />
          </Box>
        ) : filteredGrids.length > 0 ? (
          <>
            <TableContainer component={Paper} sx={{ maxHeight: 600, boxShadow: 1 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow
                    sx={{
                      backgroundColor:
                        theme.palette.mode === 'light'
                          ? alpha(theme.palette.primary.light, 0.1)
                          : alpha(theme.palette.primary.dark, 0.2),
                      '& th': {
                        fontWeight: 'bold',
                        borderBottom: `1px solid ${theme.palette.divider}`,
                      },
                    }}
                  >
                    <TableCell
                      onClick={() => requestSort('grid_name')}
                      sx={{
                        cursor: 'pointer',
                        userSelect: 'none',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <GridIcon fontSize="small" sx={{ mr: 0.5 }} />
                        网格名称/编号
                        {getSortIcon('grid_name')}
                      </Box>
                    </TableCell>
                    <TableCell
                      onClick={() => requestSort('project_name')}
                      sx={{
                        cursor: 'pointer',
                        userSelect: 'none',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ProjectIcon fontSize="small" sx={{ mr: 0.5 }} />
                        所属项目
                        {getSortIcon('project_name')}
                      </Box>
                    </TableCell>
                    <TableCell
                      onClick={() => requestSort('company')}
                      sx={{
                        cursor: 'pointer',
                        userSelect: 'none',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon fontSize="small" sx={{ mr: 0.5 }} />
                        所属公司
                        {getSortIcon('company')}
                      </Box>
                    </TableCell>
                    <TableCell
                      onClick={() => requestSort('last_active')}
                      sx={{
                        cursor: 'pointer',
                        userSelect: 'none',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <DateIcon fontSize="small" sx={{ mr: 0.5 }} />
                        最后活跃时间
                        {getSortIcon('last_active')}
                      </Box>
                    </TableCell>
                    <TableCell
                      align="right"
                      onClick={() => requestSort('conversation_count')}
                      sx={{
                        cursor: 'pointer',
                        userSelect: 'none',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      历史对话数
                      {getSortIcon('conversation_count')}
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {displayedGrids.map((grid, index) => (
                    <TableRow
                      key={`${grid.grid_code}-${index}`}
                      sx={{
                        '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) },
                        transition: 'background-color 0.2s',
                      }}
                    >
                      <TableCell sx={{ whiteSpace: 'nowrap' }}>
                        <Typography variant="body2" fontWeight="medium">
                          {grid.grid_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {grid.grid_code}
                        </Typography>
                      </TableCell>
                      <TableCell>{grid.project_name}</TableCell>
                      <TableCell>{grid.company}</TableCell>
                      <TableCell>{grid.last_active || '从未活跃'}</TableCell>
                      <TableCell align="right">{grid.conversation_count}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
              {filteredGrids.length > 10 && (
                <Button variant="outlined" color="primary" onClick={() => setShowAll(!showAll)}>
                  {showAll ? '收起' : '显示全部'}
                </Button>
              )}

              {hasMore && (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onLoadMore}
                  disabled={isLoadingMore}
                  startIcon={isLoadingMore ? <CircularProgress size={20} /> : null}
                >
                  {isLoadingMore ? '加载中...' : `加载更多`}
                </Button>
              )}
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
              显示 {displayedGrids.length} 个网格{' '}
              {hasMore || filteredGrids.length > displayedGrids.length
                ? `(共 ${totalCount || filteredGrids.length} 个)`
                : ''}
            </Typography>
          </>
        ) : (
          <Box
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
              bgcolor: theme.palette.background.default,
              borderRadius: 1,
            }}
          >
            <InactiveIcon sx={{ fontSize: '3rem', color: 'text.secondary', opacity: 0.5 }} />
            <Typography variant="body1" color="text.secondary">
              没有找到非活跃网格数据
            </Typography>
            {searchTerm && (
              <Button
                startIcon={<RefreshIcon />}
                onClick={() => setSearchTerm('')}
                variant="outlined"
                size="small"
                sx={{ mt: 1 }}
              >
                清除搜索
              </Button>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  )
}
