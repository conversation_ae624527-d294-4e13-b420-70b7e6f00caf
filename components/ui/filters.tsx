import { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  InputAdornment,
  Chip,
  Button,
  IconButton,
  Drawer,
  Paper,
  Divider,
  Collapse,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Checkbox,
  Fab,
  Tooltip,
  Backdrop,
  useTheme,
  Switch,
  FormControlLabel,
} from '@mui/material'
import {
  FilterAlt as FilterIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  CalendarMonth as CalendarIcon,
  GridView as GridIcon,
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { zhCN } from 'date-fns/locale'
import { format } from 'date-fns'

interface FiltersProps {
  months: string[]
  groups: string[]
  areas: string[]
  companies: string[]
  projects: string[]
  grids?: { grid_name: string; grid_code: string }[]
  selectedMonth: string | null
  selectedGroup: string | null
  selectedArea: string | null
  selectedCompany: string | null
  selectedProject: string | null
  selectedGrid?: string | null
  showGrids?: boolean
  startDate?: Date | null
  endDate?: Date | null
  onMonthChange: (month: string | null) => void
  onGroupChange: (group: string | null) => void
  onAreaChange: (area: string | null) => void
  onCompanyChange: (company: string | null) => void
  onProjectChange: (projects: string | null) => void
  onGridChange?: (grid: string | null) => void
  onShowGridsChange?: (showGrids: boolean) => void
  onDateRangeChange?: (start: Date | null, end: Date | null) => void
}

export function Filters({
  months,
  groups,
  areas,
  companies,
  projects,
  grids = [],
  selectedMonth,
  selectedGroup,
  selectedArea,
  selectedCompany,
  selectedProject,
  selectedGrid = null,
  showGrids = false,
  startDate = null,
  endDate = null,
  onMonthChange,
  onGroupChange,
  onAreaChange,
  onCompanyChange,
  onProjectChange,
  onGridChange = () => {},
  onShowGridsChange = () => {},
  onDateRangeChange,
}: FiltersProps) {
  const theme = useTheme()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false) // Control drawer expansion
  const [dateRange, setDateRange] = useState<{
    startDate: Date | null
    endDate: Date | null
  }>({
    startDate: startDate,
    endDate: endDate,
  })

  // Initialize selected projects from selectedProject string (comma-separated)
  useEffect(() => {
    if (selectedProject) {
      setSelectedProjects(selectedProject.split(',').map(p => p.trim()))
    } else {
      setSelectedProjects([])
    }
  }, [selectedProject])

  // 同步外部日期范围变更
  useEffect(() => {
    setDateRange({
      startDate,
      endDate,
    })
  }, [startDate, endDate])

  // Filter projects based on search term (safeguard against undefined projects)
  const filteredProjects = (projects ?? []).filter(project =>
    project.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle project selection
  const handleProjectToggle = (project: string) => {
    const newSelectedProjects = selectedProjects.includes(project)
      ? selectedProjects.filter(p => p !== project)
      : [...selectedProjects, project]

    setSelectedProjects(newSelectedProjects)
    onProjectChange(newSelectedProjects.length > 0 ? newSelectedProjects.join(',') : null)
  }

  // Remove a single project from selection
  const removeProject = (project: string) => {
    const newSelectedProjects = selectedProjects.filter(p => p !== project)
    setSelectedProjects(newSelectedProjects)
    onProjectChange(newSelectedProjects.length > 0 ? newSelectedProjects.join(',') : null)
  }

  // 处理日期变更
  const handleStartDateChange = (date: Date | null) => {
    const newDateRange = { ...dateRange, startDate: date }
    setDateRange(newDateRange)

    if (onDateRangeChange) {
      onDateRangeChange(date, dateRange.endDate)
    }

    // 如果只使用月份字符串，也可以更新月份
    if (date && onMonthChange) {
      onMonthChange(format(date, 'yyyy-MM'))
    } else if (!date && onMonthChange) {
      onMonthChange(null)
    }
  }

  const handleEndDateChange = (date: Date | null) => {
    const newDateRange = { ...dateRange, endDate: date }
    setDateRange(newDateRange)

    if (onDateRangeChange) {
      onDateRangeChange(dateRange.startDate, date)
    }
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
      {/* Floating filter button */}
      {!isExpanded && (
        <Fab
          color="primary"
          size="medium"
          aria-label="filter"
          onClick={() => setIsExpanded(true)}
          sx={{
            position: 'fixed',
            top: 24,
            left: 24,
            zIndex: 1300,
            boxShadow: theme.shadows[4],
          }}
        >
          <FilterIcon />
        </Fab>
      )}

      {/* Filters drawer */}
      <Drawer
        anchor="left"
        open={isExpanded}
        onClose={() => setIsExpanded(false)}
        PaperProps={{
          sx: {
            width: 320,
            p: 0,
            borderRadius: 0,
            boxShadow: theme.shadows[4],
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box
            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  mr: 2,
                  p: 1,
                  borderRadius: 2,
                  bgcolor: 'primary.light',
                  color: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <FilterIcon />
              </Box>
              <Typography variant="h6" fontWeight="medium">
                筛选条件
              </Typography>
            </Box>
            <IconButton
              edge="end"
              color="inherit"
              onClick={() => setIsExpanded(false)}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 3 }} />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* 日期筛选 */}
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                日期范围
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <DatePicker
                  label="开始日期"
                  value={dateRange.startDate}
                  onChange={handleStartDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarIcon fontSize="small" />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                />
                <DatePicker
                  label="结束日期"
                  value={dateRange.endDate}
                  onChange={handleEndDateChange}
                  minDate={dateRange.startDate || undefined}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarIcon fontSize="small" />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                />
              </Box>

              {/* 快速日期选择按钮 */}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const today = new Date()
                    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
                    onDateRangeChange?.(startOfMonth, today)
                  }}
                >
                  本月
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const today = new Date()
                    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
                    const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
                    onDateRangeChange?.(lastMonth, endOfLastMonth)
                  }}
                >
                  上月
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const today = new Date()
                    const oneWeekAgo = new Date(today)
                    oneWeekAgo.setDate(today.getDate() - 7)
                    onDateRangeChange?.(oneWeekAgo, today)
                  }}
                >
                  近7天
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    onDateRangeChange?.(null, null)
                  }}
                >
                  清除
                </Button>
              </Box>
            </Box>

            {/* Group Filter */}
            <FormControl variant="outlined" size="small" fullWidth>
              <InputLabel id="group-filter-label">项目集团</InputLabel>
              <Select
                labelId="group-filter-label"
                id="group-filter"
                value={selectedGroup || ''}
                onChange={e => {
                  const newGroup = e.target.value || null
                  onGroupChange(newGroup)
                  // When group changes, reset area, company and project selections
                  onAreaChange(null)
                  onCompanyChange(null)
                  onProjectChange(null)
                  setSelectedProjects([])
                }}
                label="项目集团"
              >
                <MenuItem value="">全部集团</MenuItem>
                {groups.map(group => (
                  <MenuItem key={group} value={group}>
                    {group}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Area Filter (for both 万科物业 and 伯恩) */}
            {(selectedGroup === '万科物业' || selectedGroup === '伯恩') && (
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel id="area-filter-label">区域</InputLabel>
                <Select
                  labelId="area-filter-label"
                  id="area-filter"
                  value={selectedArea || ''}
                  onChange={e => {
                    const newArea = e.target.value || null
                    onAreaChange(newArea)
                    // When area changes, reset company and project selections
                    onCompanyChange(null)
                    onProjectChange(null)
                    setSelectedProjects([])
                  }}
                  label="区域"
                >
                  <MenuItem value="">全部区域</MenuItem>
                  {areas.map(area => (
                    <MenuItem key={area} value={area}>
                      {area}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            {/* Company Filter (for 万科物业) */}
            {selectedGroup === '万科物业' && selectedArea && (
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel id="company-filter-label">项目公司</InputLabel>
                <Select
                  labelId="company-filter-label"
                  id="company-filter"
                  value={selectedCompany || ''}
                  onChange={e => {
                    const newCompany = e.target.value || null
                    onCompanyChange(newCompany)
                    // When company changes, reset project selection
                    onProjectChange(null)
                    setSelectedProjects([])
                  }}
                  label="项目公司"
                >
                  <MenuItem value="">全部公司</MenuItem>
                  {companies.map(company => (
                    <MenuItem key={company} value={company}>
                      {company}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            {/* Project Filter - Searchable Multiselect */}
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                项目 {selectedProjects.length > 0 ? `(${selectedProjects.length})` : '(全部)'}
              </Typography>

              <Box
                onClick={() => setIsProjectDropdownOpen(!isProjectDropdownOpen)}
                sx={{
                  p: 1.5,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  bgcolor: 'action.hover',
                  cursor: 'pointer',
                  minHeight: 42,
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 0.5,
                }}
              >
                {selectedProjects.length > 0 ? (
                  selectedProjects.slice(0, 3).map(project => (
                    <Chip
                      key={project}
                      label={project}
                      size="small"
                      variant="outlined"
                      color="primary"
                      onDelete={e => {
                        e.stopPropagation()
                        removeProject(project)
                      }}
                      onClick={e => e.stopPropagation()}
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    全部项目
                  </Typography>
                )}
                {selectedProjects.length > 3 && (
                  <Chip label={`+${selectedProjects.length - 3}个`} size="small" color="info" />
                )}
              </Box>

              <Collapse in={isProjectDropdownOpen}>
                <Paper
                  elevation={3}
                  sx={{
                    mt: 1,
                    maxHeight: 300,
                    overflow: 'auto',
                    border: 1,
                    borderColor: 'divider',
                  }}
                >
                  <Box
                    sx={{
                      p: 1.5,
                      position: 'sticky',
                      top: 0,
                      bgcolor: 'background.paper',
                      zIndex: 1,
                      borderBottom: 1,
                      borderColor: 'divider',
                    }}
                  >
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="搜索项目..."
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon fontSize="small" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <List disablePadding dense>
                    {filteredProjects.length > 0 ? (
                      filteredProjects.map(project => (
                        <ListItemButton
                          key={project}
                          dense
                          onClick={() => handleProjectToggle(project)}
                          sx={{
                            '&:hover': { bgcolor: 'action.hover' },
                            py: 0.5,
                          }}
                        >
                          <Checkbox
                            edge="start"
                            checked={selectedProjects.includes(project)}
                            tabIndex={-1}
                            disableRipple
                            size="small"
                          />
                          <ListItemText
                            primary={project}
                            primaryTypographyProps={{
                              variant: 'body2',
                              noWrap: true,
                            }}
                          />
                        </ListItemButton>
                      ))
                    ) : (
                      <ListItem>
                        <ListItemText
                          primary="没有匹配的项目"
                          primaryTypographyProps={{
                            variant: 'body2',
                            color: 'text.secondary',
                            align: 'center',
                          }}
                        />
                      </ListItem>
                    )}
                  </List>
                </Paper>
              </Collapse>
            </Box>

            {/* Grid Filter Toggle */}
            <Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={showGrids}
                    onChange={e => onShowGridsChange(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <GridIcon fontSize="small" />
                    <Typography variant="body2">按网格筛选数据</Typography>
                  </Box>
                }
              />

              {showGrids && grids.length === 0 && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ display: 'block', mt: 1 }}
                >
                  请先选择项目或公司以查看网格数据
                </Typography>
              )}
            </Box>

            {/* Reset Filters Button */}
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                color="error"
                fullWidth
                onClick={() => {
                  onMonthChange(null)
                  onGroupChange(null)
                  onAreaChange(null)
                  onCompanyChange(null)
                  onProjectChange(null)
                  setSelectedProjects([])
                }}
              >
                重置筛选条件
              </Button>
            </Box>
          </Box>
        </Box>
      </Drawer>

      {/* Backdrop for mobile */}
      <Backdrop
        sx={{ color: '#fff', zIndex: 1200 }}
        open={isProjectDropdownOpen}
        onClick={() => setIsProjectDropdownOpen(false)}
      />
    </LocalizationProvider>
  )
}
