import { useState } from 'react'
import { ArrowPathIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline'
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material'

export interface CompanyRanking {
  company: string
  conversation_count: number
  avg_messages: number
  work_order_rate: number
  lead_rate: number
  satisfaction_rate: number
  volume_rank: number
  satisfaction_rank: number
  work_order_rank: number
  lead_rank: number
  efficiency_rank: number
}

interface CompanyRankingTableProps {
  data: CompanyRanking[]
  title: string
  fileName: string
  isLoading?: boolean
  error?: string | null
}

export function CompanyRankingTable({
  data,
  title,
  fileName,
  isLoading = false,
  error = null,
}: CompanyRankingTableProps) {
  const [sortField, setSortField] = useState<keyof CompanyRanking>('conversation_count')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [visibleCount, setVisibleCount] = useState(10)

  // Handle sort click
  const handleSort = (field: keyof CompanyRanking) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Sort data
  const sortedData = [...data].sort((a, b) => {
    const aValue = a[sortField]
    const bValue = b[sortField]

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  // Get visible data
  const visibleData = sortedData.slice(0, visibleCount)

  // Download CSV
  const downloadCSV = () => {
    const headers = [
      '项目公司',
      '对话量',
      '平均消息数',
      '工单转化率',
      '销售线索转化率',
      '满意度',
      '对话量排名',
      '满意度排名',
      '工单转化排名',
      '销售线索排名',
      '效率排名',
    ]

    const csvRows = [
      headers.join(','),
      ...sortedData.map(item =>
        [
          `"${item.company}"`,
          item.conversation_count,
          item.avg_messages,
          item.work_order_rate,
          item.lead_rate,
          item.satisfaction_rate,
          item.volume_rank,
          item.satisfaction_rank,
          item.work_order_rank,
          item.lead_rank,
          item.efficiency_rank,
        ].join(',')
      ),
    ]

    const csvString = csvRows.join('\n')
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `${fileName}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          正在加载公司排名数据，请稍候...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Typography variant="body2">数据加载失败，请尝试选择更短的时间范围或稍后再试。</Typography>
      </Box>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info">没有找到公司排名数据。</Alert>
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" fontWeight="600">
          {title}
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<ArrowPathIcon className="w-4 h-4" />}
          onClick={downloadCSV}
          sx={{
            borderRadius: 2,
            px: 2,
            transition: 'all 0.2s',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: 1,
            },
          }}
        >
          导出CSV
        </Button>
      </Box>

      <TableContainer
        component={Paper}
        elevation={0}
        sx={{ borderRadius: 2, overflow: 'auto', border: '1px solid rgba(0,0,0,0.1)' }}
      >
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: 'rgba(0,0,0,0.03)' }}>
              <TableCell
                onClick={() => handleSort('company')}
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  项目公司
                  {sortField === 'company' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
              <TableCell
                onClick={() => handleSort('conversation_count')}
                align="right"
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  对话量
                  {sortField === 'conversation_count' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
              <TableCell
                onClick={() => handleSort('satisfaction_rate')}
                align="right"
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  满意度 (%)
                  {sortField === 'satisfaction_rate' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
              <TableCell
                onClick={() => handleSort('work_order_rate')}
                align="right"
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  工单转化率 (%)
                  {sortField === 'work_order_rate' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
              <TableCell
                onClick={() => handleSort('lead_rate')}
                align="right"
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  销售线索转化率 (%)
                  {sortField === 'lead_rate' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
              <TableCell
                onClick={() => handleSort('avg_messages')}
                align="right"
                sx={{ cursor: 'pointer', fontWeight: 'bold', py: 1.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  平均消息数
                  {sortField === 'avg_messages' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUpIcon className="w-3 h-3 ml-1" />
                    ) : (
                      <ArrowDownIcon className="w-3 h-3 ml-1" />
                    ))}
                </Box>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleData.map((row, index) => (
              <TableRow
                key={row.company}
                sx={{ '&:nth-of-type(odd)': { bgcolor: 'rgba(0,0,0,0.02)' } }}
              >
                <TableCell sx={{ py: 1.5 }}>{row.company}</TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.conversation_count}
                </TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.satisfaction_rate}%
                </TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.work_order_rate}%
                </TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.lead_rate}%
                </TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.avg_messages}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {data.length > 10 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => setVisibleCount(visibleCount === data.length ? 10 : data.length)}
            sx={{ borderRadius: 2, px: 2 }}
          >
            {visibleCount === data.length ? '显示较少' : `显示全部 ${data.length} 个公司`}
          </Button>
        </Box>
      )}
    </Box>
  )
}
