import React from 'react'
import { useState, useEffect } from 'react'
import {
  Box,
  Drawer,
  IconButton,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Collapse,
  OutlinedInput,
  Chip,
  InputAdornment,
  ListItemButton,
  Tooltip,
  Badge,
  Button,
  FormControlLabel,
  Switch,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import { alpha } from '@mui/material/styles'
import {
  FilterAlt as FilterIcon,
  KeyboardArrowRight as ArrowRightIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  CalendarMonth as CalendarIcon,
} from '@mui/icons-material'
import { ThemeToggle } from './theme-toggle'
// 导入日期选择器相关组件
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { zhCN } from 'date-fns/locale'
import { format } from 'date-fns'

const drawerWidth = 280

// Emoji icons with labels for better UX
const FILTER_ICONS = {
  date: { icon: '📆', label: '日期范围' },
  group: { icon: '🏢', label: '集团筛选' },
  area: { icon: '🌏', label: '区域筛选' },
  company: { icon: '🏗️', label: '公司筛选' },
  project: { icon: '🏘️', label: '项目筛选' },
  grid: { icon: '🧩', label: '网格筛选' },
}

interface SidebarProps {
  months: string[] // 保留但不再使用
  groups: string[]
  areas: string[]
  companies: string[]
  projects: string[]
  grids?: { grid_name: string; grid_code: string }[]
  selectedMonth: string | null // 保留但不再显示在UI中
  selectedGroup: string | null
  selectedArea: string | null
  selectedCompany: string | null
  selectedProject: string | null
  selectedGrid?: string | null
  showGrids?: boolean
  startDate?: Date | null
  endDate?: Date | null
  onMonthChange: (month: string | null) => void // 保留但不直接使用
  onGroupChange: (group: string | null) => void
  onAreaChange: (area: string | null) => void
  onCompanyChange: (company: string | null) => void
  onProjectChange: (projects: string | null) => void
  onGridChange?: (grid: string | null) => void
  onShowGridsChange?: (showGrids: boolean) => void
  onDateRangeChange?: (start: Date | null, end: Date | null) => void
  open?: boolean
  onToggle?: (isOpen: boolean) => void
}

export function Sidebar({
  months,
  groups,
  areas,
  companies,
  projects,
  grids = [],
  selectedMonth,
  selectedGroup,
  selectedArea,
  selectedCompany,
  selectedProject,
  selectedGrid = null,
  showGrids = false,
  startDate,
  endDate,
  onMonthChange,
  onGroupChange,
  onAreaChange,
  onCompanyChange,
  onProjectChange,
  onGridChange = () => {},
  onShowGridsChange = () => {},
  onDateRangeChange,
  open = true,
  onToggle,
}: SidebarProps) {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  // Use the passed open state if provided, otherwise use internal state
  const [internalOpen, setInternalOpen] = useState(true)
  const isOpen = onToggle ? open : internalOpen

  const [projectsOpen, setProjectsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  // 添加日期范围折叠状态
  const [dateRangeOpen, setDateRangeOpen] = useState(false)
  // 添加内部日期状态
  const [dateRange, setDateRange] = useState<{
    startDate: Date | null
    endDate: Date | null
  }>({
    startDate: startDate || null,
    endDate: endDate || null,
  })

  // Initialize selected projects from selectedProject string (comma-separated)
  useEffect(() => {
    if (selectedProject) {
      setSelectedProjects(selectedProject.split(',').map(p => p.trim()))
    } else {
      setSelectedProjects([])
    }
  }, [selectedProject])

  // 同步外部日期范围变更
  useEffect(() => {
    setDateRange({
      startDate: startDate || null,
      endDate: endDate || null,
    })
  }, [startDate, endDate])

  // Function to handle sidebar toggle
  const handleToggle = (openState: boolean) => {
    if (onToggle) {
      onToggle(openState)
    } else {
      setInternalOpen(openState)
    }
  }

  // Filter projects based on search term
  const filteredProjects = (projects ?? []).filter(project =>
    project.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle project selection
  const handleProjectToggle = (project: string) => {
    const newSelectedProjects = selectedProjects.includes(project)
      ? selectedProjects.filter(p => p !== project)
      : [...selectedProjects, project]

    setSelectedProjects(newSelectedProjects)
    onProjectChange(newSelectedProjects.length > 0 ? newSelectedProjects.join(',') : null)
  }

  // Remove a single project from selection
  const removeProject = (project: string) => {
    const newSelectedProjects = selectedProjects.filter(p => p !== project)
    setSelectedProjects(newSelectedProjects)
    onProjectChange(newSelectedProjects.length > 0 ? newSelectedProjects.join(',') : null)
  }

  // 处理日期变更
  const handleStartDateChange = (date: Date | null) => {
    const newDateRange = { ...dateRange, startDate: date }
    setDateRange(newDateRange)

    if (onDateRangeChange) {
      onDateRangeChange(date, dateRange.endDate)
    }

    // 如果只使用月份字符串，也可以更新月份
    if (date && onMonthChange) {
      onMonthChange(format(date, 'yyyy-MM'))
    } else if (!date && onMonthChange) {
      onMonthChange(null)
    }
  }

  const handleEndDateChange = (date: Date | null) => {
    const newDateRange = { ...dateRange, endDate: date }
    setDateRange(newDateRange)

    if (onDateRangeChange) {
      onDateRangeChange(dateRange.startDate, date)
    }
  }

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0
    if (selectedMonth) count++
    if (selectedGroup) count++
    if (selectedArea) count++
    if (selectedCompany) count++
    if (selectedProjects.length > 0) count++
    if (dateRange.startDate || dateRange.endDate) count++
    if (selectedGrid) count++
    if (showGrids) count++
    return count
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
      {/* Floating filter button when sidebar is closed */}
      {!isOpen && (
        <Tooltip title="打开筛选菜单">
          <Badge
            badgeContent={getActiveFilterCount()}
            color="primary"
            overlap="circular"
            sx={{
              position: 'fixed',
              top: 16,
              left: 16,
              zIndex: 1300,
            }}
          >
            <IconButton
              onClick={() => handleToggle(true)}
              size="large"
              sx={{
                backgroundColor: 'background.paper',
                boxShadow: 3,
                transition: 'all 0.2s',
                zIndex: 1200,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  transform: 'scale(1.1)',
                  boxShadow: 4,
                },
              }}
            >
              <FilterIcon />
            </IconButton>
          </Badge>
        </Tooltip>
      )}

      {/* Main sidebar drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor="left"
        open={isOpen}
        onClose={() => handleToggle(false)}
        ModalProps={{ keepMounted: true }}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            border: 'none',
            boxShadow: 3,
            background: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(10px)',
          },
        }}
      >
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Badge
              badgeContent={getActiveFilterCount()}
              color="primary"
              overlap="circular"
              sx={{ mr: 1 }}
            >
              <FilterIcon sx={{ color: 'primary.main' }} />
            </Badge>
            <Typography variant="h6" fontWeight="medium">
              筛选条件
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ThemeToggle />
            <IconButton
              onClick={() => handleToggle(false)}
              sx={{
                transition: 'all 0.2s',
                '&:hover': {
                  color: theme.palette.error.main,
                  transform: 'rotate(90deg)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ p: 3 }}>
          <List disablePadding>
            {/* 日期范围选择器 */}
            <ListItem disablePadding sx={{ mb: 1 }}>
              <ListItemButton
                onClick={() => setDateRangeOpen(!dateRangeOpen)}
                sx={{
                  px: 0,
                  borderRadius: 1,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  },
                }}
              >
                <Tooltip title={FILTER_ICONS.date.label} placement="left">
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Box component="span" sx={{ fontSize: '1.5rem' }}>
                      {FILTER_ICONS.date.icon}
                    </Box>
                  </ListItemIcon>
                </Tooltip>
                <ListItemText
                  primary="日期范围"
                  secondary={
                    dateRange.startDate && dateRange.endDate
                      ? `${format(dateRange.startDate, 'yyyy-MM-dd')} 至 ${format(dateRange.endDate, 'yyyy-MM-dd')}`
                      : '选择日期范围'
                  }
                />
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: dateRangeOpen ? theme.palette.primary.main : 'inherit',
                    transition: 'transform 0.3s',
                  }}
                >
                  {dateRangeOpen ? <ArrowDownIcon /> : <ArrowRightIcon />}
                </Box>
              </ListItemButton>
            </ListItem>

            <Collapse in={dateRangeOpen} timeout="auto" unmountOnExit>
              <Box sx={{ ml: 4, mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 2 }}>
                  <DatePicker
                    label="开始日期"
                    value={dateRange.startDate}
                    onChange={handleStartDateChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        size: 'small',
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <CalendarIcon fontSize="small" />
                            </InputAdornment>
                          ),
                        },
                      },
                    }}
                  />
                  <DatePicker
                    label="结束日期"
                    value={dateRange.endDate}
                    onChange={handleEndDateChange}
                    minDate={dateRange.startDate || undefined}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        size: 'small',
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <CalendarIcon fontSize="small" />
                            </InputAdornment>
                          ),
                        },
                      },
                    }}
                  />
                </Box>

                {/* 快速日期选择按钮 */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      const today = new Date()
                      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
                      onDateRangeChange?.(startOfMonth, today)
                    }}
                  >
                    本月
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      const today = new Date()
                      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
                      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
                      onDateRangeChange?.(lastMonth, endOfLastMonth)
                    }}
                  >
                    上月
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      const today = new Date()
                      const oneWeekAgo = new Date(today)
                      oneWeekAgo.setDate(today.getDate() - 7)
                      onDateRangeChange?.(oneWeekAgo, today)
                    }}
                  >
                    近7天
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      onDateRangeChange?.(null, null)
                    }}
                  >
                    清除
                  </Button>
                </Box>
              </Box>
            </Collapse>

            {/* Group Filter */}
            <ListItem disablePadding sx={{ mb: 2 }}>
              <Tooltip title={FILTER_ICONS.group.label} placement="left">
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Box component="span" sx={{ fontSize: '1.5rem' }}>
                    {FILTER_ICONS.group.icon}
                  </Box>
                </ListItemIcon>
              </Tooltip>
              <FormControl fullWidth size="small">
                <InputLabel id="group-select-label">项目集团</InputLabel>
                <Select
                  labelId="group-select-label"
                  id="group-select"
                  value={selectedGroup || ''}
                  label="项目集团"
                  onChange={e => {
                    const newGroup = e.target.value || null
                    onGroupChange(newGroup)
                    // When group changes, reset area, company and project selections
                    onAreaChange(null)
                    onCompanyChange(null)
                    onProjectChange(null)
                    setSelectedProjects([])
                  }}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: selectedGroup ? theme.palette.primary.main : undefined,
                      borderWidth: selectedGroup ? 2 : 1,
                    },
                  }}
                >
                  <MenuItem value="">全部集团</MenuItem>
                  {groups.map(group => (
                    <MenuItem key={group} value={group}>
                      {group}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </ListItem>

            {/* Area Filter (for both 万科物业 and 伯恩) */}
            {(selectedGroup === '万科物业' || selectedGroup === '伯恩') && (
              <ListItem disablePadding sx={{ mb: 2 }}>
                <Tooltip title={FILTER_ICONS.area.label} placement="left">
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Box component="span" sx={{ fontSize: '1.5rem' }}>
                      {FILTER_ICONS.area.icon}
                    </Box>
                  </ListItemIcon>
                </Tooltip>
                <FormControl fullWidth size="small">
                  <InputLabel id="area-select-label">区域</InputLabel>
                  <Select
                    labelId="area-select-label"
                    id="area-select"
                    value={selectedArea || ''}
                    label="区域"
                    onChange={e => {
                      const newArea = e.target.value || null
                      onAreaChange(newArea)
                      // When area changes, reset company and project selections
                      onCompanyChange(null)
                      onProjectChange(null)
                      setSelectedProjects([])
                    }}
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: selectedArea ? theme.palette.primary.main : undefined,
                        borderWidth: selectedArea ? 2 : 1,
                      },
                    }}
                  >
                    <MenuItem value="">全部区域</MenuItem>
                    {areas.map(area => (
                      <MenuItem key={area} value={area}>
                        {area}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </ListItem>
            )}

            {/* Company Filter (when an area is selected) */}
            {selectedArea && (
              <ListItem disablePadding sx={{ mb: 2 }}>
                <Tooltip title={FILTER_ICONS.company.label} placement="left">
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Box component="span" sx={{ fontSize: '1.5rem' }}>
                      {FILTER_ICONS.company.icon}
                    </Box>
                  </ListItemIcon>
                </Tooltip>
                <FormControl fullWidth size="small">
                  <InputLabel id="company-select-label">公司</InputLabel>
                  <Select
                    labelId="company-select-label"
                    id="company-select"
                    value={selectedCompany || ''}
                    label="公司"
                    onChange={e => {
                      const newCompany = e.target.value || null
                      onCompanyChange(newCompany)
                      // When company changes, reset project selection
                      onProjectChange(null)
                      setSelectedProjects([])
                    }}
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: selectedCompany ? theme.palette.primary.main : undefined,
                        borderWidth: selectedCompany ? 2 : 1,
                      },
                    }}
                  >
                    <MenuItem value="">全部公司</MenuItem>
                    {companies.map(company => (
                      <MenuItem key={company} value={company}>
                        {company}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </ListItem>
            )}

            {/* Project Filter - Expandable Multiselect */}
            <ListItem disablePadding sx={{ mb: 1 }}>
              <ListItemButton
                onClick={() => setProjectsOpen(!projectsOpen)}
                sx={{
                  px: 0,
                  borderRadius: 1,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  },
                }}
              >
                <Tooltip title={FILTER_ICONS.project.label} placement="left">
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <Box component="span" sx={{ fontSize: '1.5rem' }}>
                      {FILTER_ICONS.project.icon}
                    </Box>
                  </ListItemIcon>
                </Tooltip>
                <ListItemText
                  primary={`项目 ${selectedProjects.length > 0 ? `(${selectedProjects.length})` : ''}`}
                  secondary={selectedProjects.length === 0 ? '全部项目' : undefined}
                />
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: projectsOpen ? theme.palette.primary.main : 'inherit',
                    transition: 'transform 0.3s',
                  }}
                >
                  {projectsOpen ? <ArrowDownIcon /> : <ArrowRightIcon />}
                </Box>
              </ListItemButton>
            </ListItem>

            <Collapse in={projectsOpen} timeout="auto" unmountOnExit>
              <Box sx={{ ml: 4, mb: 2 }}>
                {/* Project search input */}
                <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                  <OutlinedInput
                    placeholder="搜索项目..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    startAdornment={
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" color="primary" />
                      </InputAdornment>
                    }
                    endAdornment={
                      searchTerm ? (
                        <InputAdornment position="end">
                          <IconButton size="small" onClick={() => setSearchTerm('')} edge="end">
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ) : null
                    }
                    sx={{
                      transition: 'all 0.2s',
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.background.default, 0.5),
                      },
                      '&.Mui-focused': {
                        boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
                      },
                    }}
                  />
                </FormControl>

                {/* Selected projects display */}
                {selectedProjects.length > 0 && (
                  <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedProjects.map(project => (
                      <Chip
                        key={project}
                        label={project}
                        size="small"
                        onDelete={() => removeProject(project)}
                        sx={{
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          borderColor: theme.palette.primary.main,
                          borderWidth: 1,
                          borderStyle: 'solid',
                          fontWeight: 'medium',
                          transition: 'all 0.2s',
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.2),
                            transform: 'translateY(-2px)',
                          },
                          '& .MuiChip-deleteIcon': {
                            color: theme.palette.primary.main,
                            '&:hover': {
                              color: theme.palette.error.main,
                            },
                          },
                        }}
                      />
                    ))}
                  </Box>
                )}

                {/* Project list */}
                <List dense disablePadding>
                  {filteredProjects.length > 0 ? (
                    filteredProjects.map(project => (
                      <ListItem key={project} disablePadding sx={{ mb: 0.5 }}>
                        <ListItemButton
                          onClick={() => handleProjectToggle(project)}
                          selected={selectedProjects.includes(project)}
                          sx={{
                            borderRadius: 1,
                            py: 0.5,
                            transition: 'all 0.15s',
                            '&.Mui-selected': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.8),
                              color: theme.palette.primary.contrastText,
                              '&:hover': {
                                backgroundColor: theme.palette.primary.main,
                              },
                            },
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.1),
                              transform: 'translateX(4px)',
                            },
                          }}
                        >
                          <ListItemText
                            primary={project}
                            primaryTypographyProps={{
                              fontSize: '0.9rem',
                            }}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))
                  ) : (
                    <ListItem>
                      <ListItemText
                        primary="没有找到匹配的项目"
                        primaryTypographyProps={{
                          variant: 'body2',
                          color: 'text.secondary',
                          textAlign: 'center',
                          fontStyle: 'italic',
                        }}
                      />
                    </ListItem>
                  )}
                </List>
              </Box>
            </Collapse>

            {/* Grid Filter Toggle */}
            <ListItem>
              <FormControlLabel
                control={
                  <Switch
                    checked={showGrids}
                    onChange={e => onShowGridsChange(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ListItemIcon sx={{ minWidth: 'auto' }}>{FILTER_ICONS.grid.icon}</ListItemIcon>
                    <Typography variant="body2">{FILTER_ICONS.grid.label}</Typography>
                  </Box>
                }
              />
            </ListItem>

            {showGrids && grids.length === 0 && (
              <ListItem>
                <Typography variant="caption" color="text.secondary">
                  请先选择项目或公司以查看网格数据
                </Typography>
              </ListItem>
            )}
          </List>
        </Box>
      </Drawer>
    </LocalizationProvider>
  )
}
