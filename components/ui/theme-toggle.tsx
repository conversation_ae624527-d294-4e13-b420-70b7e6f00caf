'use client'

import React from 'react'
import { Icon<PERSON>utton, Tooltip, useTheme } from '@mui/material'
import { Brightness7 as LightModeIcon } from '@mui/icons-material'
import { useThemeContext } from '@/lib/theme-provider'

// 此组件已简化为只显示图标，因为我们移除了暗色模式
export function ThemeToggle() {
  const theme = useTheme()
  const { setMode } = useThemeContext()

  return (
    <Tooltip title="明亮模式">
      <span>
        <IconButton color="inherit" sx={{ ml: 1 }} disabled>
          <LightModeIcon />
        </IconButton>
      </span>
    </Tooltip>
  )
}
