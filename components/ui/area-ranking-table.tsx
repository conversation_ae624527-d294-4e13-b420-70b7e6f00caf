import { useState } from 'react'
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  IconButton,
  Collapse,
  Chip,
  alpha,
  useTheme,
  Tooltip,
  Alert,
  CircularProgress,
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  FileDownload as DownloadIcon,
  InfoOutlined as InfoIcon,
} from '@mui/icons-material'

// Column configuration with icons
const COLUMNS = [
  { id: 'area', label: '区域', icon: '🌏', tooltip: '区域名称' },
  { id: 'conversation_count', label: '对话量', icon: '💬', tooltip: '区域总对话数量' },
  { id: 'satisfaction_rate', label: '满意度 (%)', icon: '😊', tooltip: '用户满意度评分百分比' },
  { id: 'work_order_rate', label: '工单转化率 (%)', icon: '📋', tooltip: '对话转化为工单的百分比' },
  {
    id: 'lead_rate',
    label: '销售线索转化率 (%)',
    icon: '🔍',
    tooltip: '对话转化为销售线索的百分比',
  },
  { id: 'avg_messages', label: '平均消息数', icon: '📊', tooltip: '每次对话的平均消息数量' },
]

// Extended interface to include debug info
export interface AreaRanking {
  area: string
  conversation_count: number
  avg_messages: number
  work_order_rate: number
  lead_rate: number
  satisfaction_rate: number
  volume_rank: number
  satisfaction_rank: number
  work_order_rank: number
  lead_rank: number
  efficiency_rank: number
  hangzhou_debug?: {
    project: string
    original_area: string
    normalized_area: string
    conversation_count: number
  }[]
}

interface AreaRankingTableProps {
  data: AreaRanking[]
  title: string
  fileName: string
  isLoading?: boolean
  error?: string | null
}

export function AreaRankingTable({
  data,
  title,
  fileName,
  isLoading = false,
  error = null,
}: AreaRankingTableProps) {
  const theme = useTheme()
  const [sortField, setSortField] = useState<keyof AreaRanking>('conversation_count')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [visibleCount, setVisibleCount] = useState(10)
  const [showHangzhouDetails, setShowHangzhouDetails] = useState(false)

  // Handle sort click
  const handleSort = (field: keyof AreaRanking) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Sort data
  const sortedData = [...data].sort((a, b) => {
    const aValue = a[sortField] as number | string
    const bValue = b[sortField] as number | string

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  // Get visible data
  const visibleData = sortedData.slice(0, visibleCount)

  // Find the Hangzhou data
  const hangzhouData = visibleData.find(
    item => item.area.toLowerCase().includes('杭州') || item.area.toLowerCase().includes('hangzhou')
  )

  // Function to get cell color based on value (for percentage values)
  const getValueColor = (value: number, columnId: string) => {
    if (!['satisfaction_rate', 'work_order_rate', 'lead_rate'].includes(columnId)) return undefined

    if (value >= 80) return theme.palette.success.main
    if (value >= 60) return theme.palette.warning.main
    if (value < 40) return theme.palette.error.main
    return theme.palette.text.primary
  }

  // Download CSV
  const downloadCSV = () => {
    const headers = [
      '区域',
      '对话量',
      '平均消息数',
      '工单转化率',
      '销售线索转化率',
      '满意度',
      '对话量排名',
      '满意度排名',
      '工单转化排名',
      '销售线索排名',
      '效率排名',
    ]

    const csvRows = [
      headers.join(','),
      ...sortedData.map(item =>
        [
          `"${item.area}"`,
          item.conversation_count,
          item.avg_messages,
          item.work_order_rate,
          item.lead_rate,
          item.satisfaction_rate,
          item.volume_rank,
          item.satisfaction_rank,
          item.work_order_rank,
          item.lead_rank,
          item.efficiency_rank,
        ].join(',')
      ),
    ]

    const csvString = csvRows.join('\n')
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `${fileName}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          正在加载区域排名数据，请稍候...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Typography variant="body2">数据加载失败，请尝试选择更短的时间范围或稍后再试。</Typography>
      </Box>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info">没有找到区域排名数据。</Alert>
      </Box>
    )
  }

  return (
    <Paper
      elevation={1}
      sx={{
        p: 3,
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: 3,
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2.5,
        }}
      >
        <Typography variant="h6" fontWeight="600" sx={{ display: 'flex', alignItems: 'center' }}>
          <Box component="span" sx={{ mr: 1, fontSize: '1.5rem' }}>
            🌏
          </Box>
          {title}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          {hangzhouData?.hangzhou_debug && hangzhouData.hangzhou_debug.length > 0 && (
            <Button
              variant="outlined"
              size="small"
              sx={{
                borderRadius: 4,
                px: 2,
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 1,
                },
              }}
              onClick={() => setShowHangzhouDetails(!showHangzhouDetails)}
              startIcon={showHangzhouDetails ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            >
              {showHangzhouDetails ? '隐藏杭州详情' : '查看杭州详情'}
            </Button>
          )}
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={downloadCSV}
            sx={{
              borderRadius: 4,
              px: 2,
              transition: 'all 0.2s',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 1,
              },
            }}
          >
            导出CSV
          </Button>
        </Box>
      </Box>
      {/* 杭州区域详情调试面板 */}
      <Collapse in={showHangzhouDetails && !!hangzhouData?.hangzhou_debug?.length}>
        <Paper
          variant="outlined"
          sx={{
            mb: 2.5,
            p: 2,
            bgcolor: alpha(theme.palette.primary.light, 0.05),
            borderRadius: 2,
            borderColor: alpha(theme.palette.primary.main, 0.2),
          }}
        >
          <Typography
            variant="subtitle1"
            fontWeight="600"
            mb={1}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <Box component="span" sx={{ mr: 1, fontSize: '1.2rem' }}>
              ℹ️
            </Box>
            杭州区域详情
            <Tooltip title="显示杭州区域包含的项目详情">
              <IconButton size="small" sx={{ ml: 1 }}>
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Typography>
          <TableContainer sx={{ borderRadius: 1, overflow: 'hidden' }}>
            <Table size="small">
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableCell sx={{ fontSize: '0.75rem', fontWeight: 600 }}>项目</TableCell>
                  <TableCell sx={{ fontSize: '0.75rem', fontWeight: 600 }}>原始区域名</TableCell>
                  <TableCell sx={{ fontSize: '0.75rem', fontWeight: 600 }}>规范化区域名</TableCell>
                  <TableCell sx={{ fontSize: '0.75rem', fontWeight: 600 }}>对话数</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {hangzhouData?.hangzhou_debug?.map((debug, i) => (
                  <TableRow
                    key={debug.project}
                    sx={{
                      '&:nth-of-type(odd)': {
                        bgcolor: alpha(theme.palette.background.default, 0.4),
                      },
                    }}
                  >
                    <TableCell sx={{ fontSize: '0.75rem' }}>{debug.project}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{debug.original_area}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{debug.normalized_area}</TableCell>
                    <TableCell sx={{ fontSize: '0.75rem' }}>{debug.conversation_count}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Collapse>
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        }}
      >
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
              {COLUMNS.map(column => (
                <TableCell
                  key={column.id}
                  onClick={() => handleSort(column.id as keyof AreaRanking)}
                  align={column.id === 'area' ? 'left' : 'right'}
                  sx={{
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    py: 1.5,
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                    },
                  }}
                >
                  <Tooltip title={column.tooltip}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: column.id === 'area' ? 'flex-start' : 'flex-end',
                      }}
                    >
                      {column.id === 'area' && (
                        <Box component="span" sx={{ mr: 0.5, fontSize: '1.2rem' }}>
                          {column.icon}
                        </Box>
                      )}
                      {column.label}
                      {column.id !== 'area' && (
                        <Box component="span" sx={{ ml: 0.5, fontSize: '1.2rem' }}>
                          {column.icon}
                        </Box>
                      )}
                      <IconButton size="small" sx={{ ml: 0.5 }}>
                        {sortField === column.id ? (
                          sortDirection === 'asc' ? (
                            <ArrowUpIcon fontSize="small" color="primary" />
                          ) : (
                            <ArrowDownIcon fontSize="small" color="primary" />
                          )
                        ) : (
                          <Box sx={{ width: 24, height: 24 }} /> // Empty space for alignment
                        )}
                      </IconButton>
                    </Box>
                  </Tooltip>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleData.map((row, index) => (
              <TableRow
                key={row.area}
                sx={{
                  '&:nth-of-type(odd)': { bgcolor: alpha(theme.palette.background.default, 0.4) },
                  '&:hover': {
                    bgcolor: alpha(theme.palette.action.hover, 0.7),
                    cursor: 'pointer',
                  },
                  transition: 'background-color 0.2s',
                }}
              >
                <TableCell sx={{ py: 1.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Chip
                      label={`#${row.volume_rank}`}
                      size="small"
                      sx={{
                        minWidth: 40,
                        mr: 1,
                        bgcolor:
                          row.volume_rank <= 3
                            ? alpha(theme.palette.success.main, 0.1)
                            : alpha(theme.palette.grey[500], 0.1),
                        color:
                          row.volume_rank <= 3
                            ? theme.palette.success.main
                            : theme.palette.text.secondary,
                        fontWeight: 'bold',
                      }}
                    />
                    {row.area}
                  </Box>
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'medium', py: 1.5 }}>
                  {row.conversation_count}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.satisfaction_rate, 'satisfaction_rate'),
                    fontWeight: 'medium',
                    py: 1.5,
                  }}
                >
                  {row.satisfaction_rate}%
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.work_order_rate, 'work_order_rate'),
                    fontWeight: 'medium',
                    py: 1.5,
                  }}
                >
                  {row.work_order_rate}%
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    color: getValueColor(row.lead_rate, 'lead_rate'),
                    fontWeight: 'medium',
                    py: 1.5,
                  }}
                >
                  {row.lead_rate}%
                </TableCell>
                <TableCell align="right" sx={{ py: 1.5 }}>
                  {row.avg_messages}
                </TableCell>
              </TableRow>
            ))}

            {visibleData.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography color="text.secondary">暂无数据</Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {sortedData.length > visibleCount && (
        <Box sx={{ mt: 2, textAlign: 'center', pb: 1, pt: 1 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => setVisibleCount(prev => (prev === 10 ? sortedData.length : 10))}
            endIcon={visibleCount === 10 ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />}
            sx={{
              borderRadius: 4,
              px: 2,
              transition: 'all 0.2s',
              '&:hover': {
                transform: visibleCount === 10 ? 'translateY(-2px)' : 'translateY(2px)',
                boxShadow: 1,
              },
            }}
          >
            {visibleCount === 10 ? '显示全部' : '收起'}
          </Button>
        </Box>
      )}
    </Paper>
  )
}
