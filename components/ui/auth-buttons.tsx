'use client'

import { Box, Button } from '@mui/material'

// 从 @clerk/nextjs 导入客户端组件，确保使用正确的大小写
import { SignInButton, SignUpButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs'

export function AuthButtons() {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      <SignedOut>
        <SignInButton mode="modal">
          <Button variant="outlined" color="primary" size="small">
            登录
          </Button>
        </SignInButton>
        <SignUpButton mode="modal">
          <Button variant="contained" color="primary" size="small">
            注册
          </Button>
        </SignUpButton>
      </SignedOut>
      <SignedIn>
        <UserButton
          signInUrl="/"
          appearance={{
            elements: {
              avatarBox: {
                width: 40,
                height: 40,
              },
            },
          }}
        />
      </SignedIn>
    </Box>
  )
}
