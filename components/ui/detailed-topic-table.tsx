import React, { useState, useMemo } from 'react'
import { DetailedTopicDistribution } from '@/hooks/use-analytics-data'
import * as XLSX from 'xlsx'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Collapse,
  IconButton,
  Chip,
  Grid,
  Tooltip,
  useTheme,
  alpha,
  Stack,
} from '@mui/material'
import {
  KeyboardArrowDown as ExpandMoreIcon,
  KeyboardArrowUp as ExpandLessIcon,
  FileDownload as DownloadIcon,
  InfoOutlined as InfoIcon,
} from '@mui/icons-material'

interface DetailedTopicTableProps {
  data: DetailedTopicDistribution[]
  title: string
  fileName: string
}

export function DetailedTopicTable({ data, title, fileName }: DetailedTopicTableProps) {
  const theme = useTheme()
  const [expandedSummary, setExpandedSummary] = useState<boolean>(true) // Default expanded to true

  // 🔧 修复：根据API返回的数据动态生成排序后的分类
  const categoryGroups = useMemo(() => {
    // 定义分类分组和图标（现在根据新的API字段格式）
    const categoryMappings: { [key: string]: { icon: string; group: string; color: string; displayName: string } } = {
      '供水问题': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '供水问题' },
      '水费缴纳': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '水费缴纳' },
      '卫浴_漏水问题': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '卫浴/漏水问题' },
      '送水服务': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '送水服务' },
      '停电咨询': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '停电咨询' },
      '电费缴纳': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '电费缴纳' },
      '电器维修': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '电器维修' },
      '照明问题': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '照明问题' },
      '燃气问题': { icon: '🏗️', group: '基础设施类问题', color: theme.palette.primary.main, displayName: '燃气问题' },
      
      '物业费': { icon: '🏢', group: '物业服务类', color: theme.palette.secondary.main, displayName: '物业费' },
      '噪音投诉': { icon: '🏢', group: '物业服务类', color: theme.palette.secondary.main, displayName: '噪音投诉' },
      '一般投诉': { icon: '🏢', group: '物业服务类', color: theme.palette.secondary.main, displayName: '一般投诉' },
      '电梯问题': { icon: '🏢', group: '物业服务类', color: theme.palette.secondary.main, displayName: '电梯问题' },
      '卫生清洁': { icon: '🏢', group: '物业服务类', color: theme.palette.secondary.main, displayName: '卫生清洁' },
      
      '门窗维修': { icon: '🔧', group: '维修类服务', color: theme.palette.warning.main, displayName: '门窗维修' },
      '热水器问题': { icon: '🔧', group: '维修类服务', color: theme.palette.warning.main, displayName: '热水器问题' },
      '安装_预约服务': { icon: '🔧', group: '维修类服务', color: theme.palette.warning.main, displayName: '安装/预约服务' },
      '家具维修': { icon: '🔧', group: '维修类服务', color: theme.palette.warning.main, displayName: '家具维修' },
      
      '车辆登记_变更': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '车辆登记/变更' },
      '车辆通行_搬家': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '车辆通行/搬家' },
      '门禁_出入管理': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '门禁/出入管理' },
      '锁具问题': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '锁具问题' },
      '钥匙问题': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '钥匙问题' },
      '车位费用_管理': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '车位费用/管理' },
      '车位被占': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '车位被占' },
      '充电桩': { icon: '🚗', group: '车辆与出入类', color: theme.palette.error.main, displayName: '充电桩' },
      
      '快递服务': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '快递服务' },
      '装修_施工': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '装修/施工' },
      '暖气问题': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '暖气问题' },
      '家政服务': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '家政服务' },
      '搬家服务': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '搬家服务' },
      '打印服务': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '打印服务' },
      '物品借用': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '物品借用' },
      '租房咨询': { icon: '🏠', group: '社区服务类', color: theme.palette.success.main, displayName: '租房咨询' },
      
      '一般咨询': { icon: '💬', group: '一般交互类', color: theme.palette.info.main, displayName: '一般咨询' },
      '转人工服务': { icon: '💬', group: '一般交互类', color: theme.palette.info.main, displayName: '转人工服务' },
      '满意_感谢反馈': { icon: '💬', group: '一般交互类', color: theme.palette.info.main, displayName: '满意/感谢反馈' },
      '问候_简单交流': { icon: '💬', group: '一般交互类', color: theme.palette.info.main, displayName: '问候/简单交流' },
      '无明确诉求_测试': { icon: '💬', group: '一般交互类', color: theme.palette.info.main, displayName: '无明确诉求/测试' },
      
      '紧急情况_安全问题': { icon: '⚠️', group: '异常情况', color: '#FF5722', displayName: '紧急情况/安全问题' },
      '其他问题': { icon: '❓', group: '其他', color: theme.palette.grey[600], displayName: '其他问题' },
    }
    
    // 如果有数据，从第一行数据中获取字段的排序
    if (data && data.length > 0) {
      const firstItem = data[0] as any
      // 查找所有以_count结尾的字段，这些是新API格式的分类字段
      const countFields = Object.keys(firstItem).filter(key => 
        key.endsWith('_count') && key !== 'total_conversations'
      )
      
      // 提取分类名称（移除_count后缀）
      const categoryNames = countFields.map(field => field.replace('_count', ''))
      
      // 按分组重新组织字段
      const groupMap = new Map<string, { icon: string; color: string; fields: Array<{key: string, displayName: string}> }>()
      
      categoryNames.forEach(categoryKey => {
        const categoryInfo = categoryMappings[categoryKey]
        if (categoryInfo) {
          if (!groupMap.has(categoryInfo.group)) {
            groupMap.set(categoryInfo.group, {
              icon: categoryInfo.icon,
              color: categoryInfo.color,
              fields: []
            })
          }
          groupMap.get(categoryInfo.group)!.fields.push({
            key: categoryKey,
            displayName: categoryInfo.displayName
          })
        }
      })
      
      // 转换为最终格式
      return Array.from(groupMap.entries()).map(([name, info]) => ({
        name,
        icon: info.icon,
        color: info.color,
        fields: info.fields
      }))
    }
    
    // 如果没有数据，返回空数组
    return []
  }, [data, theme])

  // Create aggregated data for summary view
  // Combine all data rows into one summary
  const summaryData = data.reduce(
    (acc, curr) => {
      // Skip if the current item is already a summary
      if ('是否汇总' in curr && (curr as any)['是否汇总'] === '是') return acc

      // 新的API格式使用 'date' 字段
      const timeKey = 'date' in curr ? 'date' : '日期'

      // Initialize accumulator if needed
      if (!acc[timeKey]) {
        acc = { [timeKey]: '汇总', 总对话数: 0 }

        // Initialize all the topic fields with 0
        categoryGroups.forEach(group => {
          group.fields.forEach(field => {
            acc[field.key] = 0
          })
        })
      }

      // Sum up the conversation count (新API使用total_conversations)
      acc['总对话数'] += Number((curr as any)['total_conversations'] || (curr as any)['总对话数'] || 0)

      // Sum up all topic fields (新API使用field_count格式)
      categoryGroups.forEach(group => {
        group.fields.forEach(field => {
          const countField = `${field.key}_count`
          if ((curr as any)[countField]) {
            acc[field.key] = (acc[field.key] || 0) + Number((curr as any)[countField])
          }
        })
      })

      return acc
    },
    {} as Record<string, any>
  )

  // Calculate percentages for the summary
  if (summaryData['总对话数'] > 0) {
    categoryGroups.forEach(group => {
      group.fields.forEach(field => {
        summaryData[`${field.key}占比`] =
          ((summaryData[field.key] / summaryData['总对话数']) * 100).toFixed(2) + '%'
      })
    })
  }

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            {title || '问题分类详细分析'}
          </Typography>
          <IconButton
            onClick={() => {
              const workbook = XLSX.utils.book_new()

              // Process data for Excel
              const processedData = { ...summaryData }

              const worksheet = XLSX.utils.json_to_sheet([processedData])
              XLSX.utils.book_append_sheet(workbook, worksheet, 'Summary')

              XLSX.writeFile(workbook, `${fileName || 'topic-analysis'}.xlsx`)
            }}
          >
            <DownloadIcon />
          </IconButton>
        </Box>

        {/* Summary view */}
        <Card variant="outlined" sx={{ mb: 0, bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
          <CardContent>
            <Box
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                问题分类摘要
              </Typography>
              <Typography variant="subtitle1" fontWeight="bold">
                总对话数: {summaryData['总对话数']}
              </Typography>
            </Box>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              {categoryGroups.map(group => {
                // Calculate total for this group
                const groupTotal = group.fields.reduce(
                  (sum, field) => sum + (summaryData[field.key] || 0),
                  0
                )
                const groupPercentage = ((groupTotal / summaryData['总对话数']) * 100).toFixed(2)

                return (
                  <Grid key={`summary-detail-${group.name}`} size={{ xs: 12, sm: 6, md: 4 }}>
                    <Card
                      variant="outlined"
                      sx={{ height: '100%', bgcolor: alpha(group.color, 0.08) }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Typography variant="body1" component="span" sx={{ mr: 1 }}>
                            {group.icon}
                          </Typography>
                          <Typography variant="body1" fontWeight="bold">
                            {group.name}
                          </Typography>
                        </Box>
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                          共 {groupTotal} 条 ({groupPercentage}%)
                        </Typography>

                        {group.fields.map(field => {
                          const value = summaryData[field.key] || 0
                          const percentage =
                            value > 0
                              ? ((value / summaryData['总对话数']) * 100).toFixed(2)
                              : '0.00'

                          return (
                            <Box
                              key={`${group.name}-${field.key}`}
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                mb: 1,
                                '&:last-child': { mb: 0 },
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{ color: value > 0 ? 'text.primary' : 'text.secondary' }}
                              >
                                {field.displayName}
                              </Typography>
                              <Box>
                                <Typography
                                  variant="body2"
                                  component="span"
                                  sx={{
                                    color: value > 0 ? 'text.primary' : 'text.secondary',
                                    mr: 1,
                                  }}
                                >
                                  {value}
                                </Typography>
                                <Typography variant="body2" component="span" color="text.secondary">
                                  ({percentage}%)
                                </Typography>
                              </Box>
                            </Box>
                          )
                        })}
                      </CardContent>
                    </Card>
                  </Grid>
                )
              })}
            </Grid>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}
