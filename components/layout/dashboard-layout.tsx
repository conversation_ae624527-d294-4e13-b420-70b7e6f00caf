'use client'

import React, { useState } from 'react'
import { Box, useTheme, useMediaQuery } from '@mui/material'
import { Sidebar } from '../ui/sidebar'
import { AuthButtons } from '../ui/auth-buttons'

export interface DashboardLayoutProps {
  children: React.ReactNode
  months: string[] // 保留以保持后向兼容性，但实际上不再使用
  groups: string[]
  areas: string[]
  companies: string[]
  projects: string[]
  grids?: { grid_name: string; grid_code: string }[]
  selectedMonth: string | null // 保留以保持后向兼容性，但实际上不再显示在UI中
  selectedGroup: string | null
  selectedArea: string | null
  selectedCompany: string | null
  selectedProject: string | null
  selectedGrid?: string | null
  showGrids?: boolean
  startDate?: Date | null
  endDate?: Date | null
  onMonthChange: (month: string | null) => void // 保留以保持后向兼容性
  onGroupChange: (group: string | null) => void
  onAreaChange: (area: string | null) => void
  onCompanyChange: (company: string | null) => void
  onProjectChange: (projects: string | null) => void
  onGridChange?: (grid: string | null) => void
  onShowGridsChange?: (showGrids: boolean) => void
  onDateRangeChange?: (start: Date | null, end: Date | null) => void
}

// Sidebar width from sidebar component
const drawerWidth = 0

export function DashboardLayout({
  children,
  months,
  groups,
  areas,
  companies,
  projects,
  grids = [],
  selectedMonth,
  selectedGroup,
  selectedArea,
  selectedCompany,
  selectedProject,
  selectedGrid = null,
  showGrids = false,
  startDate,
  endDate,
  onMonthChange,
  onGroupChange,
  onAreaChange,
  onCompanyChange,
  onProjectChange,
  onGridChange = () => {},
  onShowGridsChange = () => {},
  onDateRangeChange,
}: DashboardLayoutProps) {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const [sidebarOpen, setSidebarOpen] = useState(true)

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Sidebar component with filters */}
      <Sidebar
        months={months}
        groups={groups}
        areas={areas}
        companies={companies}
        projects={projects}
        grids={grids}
        selectedMonth={selectedMonth}
        selectedGroup={selectedGroup}
        selectedArea={selectedArea}
        selectedCompany={selectedCompany}
        selectedProject={selectedProject}
        selectedGrid={selectedGrid}
        showGrids={showGrids}
        startDate={startDate}
        endDate={endDate}
        onMonthChange={onMonthChange}
        onGroupChange={onGroupChange}
        onAreaChange={onAreaChange}
        onCompanyChange={onCompanyChange}
        onProjectChange={onProjectChange}
        onGridChange={onGridChange}
        onShowGridsChange={onShowGridsChange}
        onDateRangeChange={onDateRangeChange}
        open={sidebarOpen}
        onToggle={(isOpen: boolean) => setSidebarOpen(isOpen)}
      />

      {/* Main content area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          minHeight: '100vh',
          width: '100%',
          overflow: 'auto',
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          marginLeft: !isMobile ? (sidebarOpen ? `${drawerWidth}px` : '-280px') : '0px',
        }}
      >
        {children}
      </Box>
    </Box>
  )
}
