'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material'

interface CacheStatusData {
  isInitialized: boolean
  isLoading: boolean
  lastUpdated: string | null
  dataCount: {
    conversations: number
    projects: number
    companies: number
  }
  healthStatus: 'healthy' | 'initializing' | 'error'
  uptime: number
}

export default function CacheStatus() {
  const [status, setStatus] = useState<CacheStatusData | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  const theme = useTheme()

  // 获取缓存状态
  const fetchCacheStatus = async () => {
    try {
      const response = await fetch('/api/cache/status')
      const result = await response.json()
      
      if (result.success) {
        setStatus(result.data)
        setError(null)
      } else {
        setError(result.error || '获取缓存状态失败')
      }
    } catch (err) {
      setError('网络错误')
      console.error('获取缓存状态失败:', err)
    }
  }

  // 手动刷新缓存
  const handleRefreshCache = async () => {
    setIsRefreshing(true)
    try {
      const response = await fetch('/api/cache/refresh', {
        method: 'POST',
      })
      const result = await response.json()
      
      if (result.success) {
        setLastRefresh(new Date())
        setError(null)
        // 刷新状态
        await fetchCacheStatus()
      } else {
        setError(result.error || '刷新缓存失败')
      }
    } catch (err) {
      setError('网络错误')
      console.error('刷新缓存失败:', err)
    } finally {
      setIsRefreshing(false)
    }
  }

  // 格式化时间
  const formatTime = (dateString: string | null) => {
    if (!dateString) return '未知'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  }

  // 格式化运行时间
  const formatUptime = (uptime: number) => {
    const minutes = Math.floor(uptime / (1000 * 60))
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    }
    return `${minutes}分钟`
  }

  // 获取状态颜色
  const getStatusColor = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return theme.palette.success.main
      case 'initializing':
        return theme.palette.warning.main
      case 'error':
        return theme.palette.error.main
      default:
        return theme.palette.grey[500]
    }
  }

  // 获取状态图标
  const getStatusIcon = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return <CheckCircleIcon color="success" />
      case 'initializing':
        return <CircularProgress size={20} />
      case 'error':
        return <ErrorIcon color="error" />
      default:
        return <ScheduleIcon />
    }
  }

  // 获取状态文本
  const getStatusText = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return '健康'
      case 'initializing':
        return '初始化中'
      case 'error':
        return '错误'
      default:
        return '未知'
    }
  }

  // 定期更新状态
  useEffect(() => {
    fetchCacheStatus()
    
    const interval = setInterval(fetchCacheStatus, 30000) // 每30秒更新一次
    return () => clearInterval(interval)
  }, [])

  if (!status && !error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={24} />
      </Box>
    )
  }

  return (
    <Paper
      elevation={1}
      sx={{
        p: 3,
        borderRadius: 2,
        backgroundColor: alpha(theme.palette.background.paper, 0.9),
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <StorageIcon color="primary" />
          <Typography variant="h6" fontWeight="bold">
            缓存系统状态
          </Typography>
        </Box>
        
        <Tooltip title="刷新缓存">
          <IconButton
            onClick={handleRefreshCache}
            disabled={isRefreshing}
            color="primary"
            sx={{
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
              },
            }}
          >
            {isRefreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {status && (
        <>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                {getStatusIcon(status.healthStatus)}
                <Typography variant="body2" color="text.secondary">
                  状态:
                </Typography>
                <Chip
                  label={getStatusText(status.healthStatus)}
                  size="small"
                  sx={{
                    backgroundColor: alpha(getStatusColor(status.healthStatus), 0.1),
                    color: getStatusColor(status.healthStatus),
                  }}
                />
              </Box>
            </Box>
            
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <ScheduleIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  最后更新:
                </Typography>
                <Typography variant="body2">
                  {formatTime(status.lastUpdated)}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            缓存数据统计
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Box sx={{ flex: 1, textAlign: 'center', p: 1 }}>
              <Typography variant="h6" color="primary">
                {status.dataCount.conversations.toLocaleString()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                对话记录
              </Typography>
            </Box>
            
            <Box sx={{ flex: 1, textAlign: 'center', p: 1 }}>
              <Typography variant="h6" color="primary">
                {status.dataCount.projects}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                项目数量
              </Typography>
            </Box>
            
            <Box sx={{ flex: 1, textAlign: 'center', p: 1 }}>
              <Typography variant="h6" color="primary">
                {status.dataCount.companies}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                公司数量
              </Typography>
            </Box>
          </Box>

          {status.uptime > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SpeedIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  运行时间: {formatUptime(status.uptime)}
                </Typography>
              </Box>
            </>
          )}

          {lastRefresh && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" color="text.secondary">
                上次手动刷新: {lastRefresh.toLocaleString('zh-CN')}
              </Typography>
            </Box>
          )}
        </>
      )}
    </Paper>
  )
} 