'use client'

import { 
  <PERSON>, 
  CardContent, 
  Typography, 
  Box, 
  Chip, 
  Skeleton,
  Alert,
  Grid as MuiGrid
} from '@mui/material'
import { 
  Description as FileText, 
  CheckCircle, 
  Schedule as Clock, 
  Warning as Alert<PERSON>riangle, 
  People as Users, 
  Business as Building,
  Person as User<PERSON><PERSON><PERSON>,
  <PERSON>
} from '@mui/icons-material'
import { WorkOrderOverview } from '@/types/work-order'

// Create wrapper component to be compatible with MUI v8.3.1
function Grid(props: {
  size?: Record<string, number>
  children: React.ReactNode
  [key: string]: any
}) {
  const { size, ...rest } = props

  // For MUI v8, convert size props to sx prop with width
  const sxWidth: Record<string, string> = {}

  if (size) {
    if (size.xs) sxWidth.xs = `${(size.xs / 12) * 100}%`
    if (size.sm) sxWidth.sm = `${(size.sm / 12) * 100}%`
    if (size.md) sxWidth.md = `${(size.md / 12) * 100}%`
    if (size.lg) sxWidth.lg = `${(size.lg / 12) * 100}%`
  }

  return (
    <MuiGrid
      {...rest}
      sx={{
        ...rest.sx,
        width: sxWidth,
        flexGrow: 1,
      }}
    >
      {props.children}
    </MuiGrid>
  )
}

interface WorkOrderOverviewCardsProps {
  overview: WorkOrderOverview | null
  isLoading: boolean
}

export function WorkOrderOverviewCards({ overview, isLoading }: WorkOrderOverviewCardsProps) {
  if (isLoading) {
    return (
      <MuiGrid container spacing={3}>
        {Array.from({ length: 8 }).map((_, i) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={i}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Skeleton variant="text" width={80} height={20} />
                  <Skeleton variant="circular" width={40} height={40} />
                </Box>
                <Skeleton variant="text" width={60} height={32} />
                <Skeleton variant="text" width={100} height={16} />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </MuiGrid>
    )
  }

  if (!overview) {
    return (
      <Alert severity="warning" sx={{ mb: 3 }}>
        暂无工单数据
      </Alert>
    )
  }

  const completionRate = overview.total_orders > 0 
    ? (overview.completed_orders / overview.total_orders * 100).toFixed(1)
    : '0'

  const urgentRate = overview.total_orders > 0
    ? (overview.urgent_orders / overview.total_orders * 100).toFixed(1)
    : '0'

  const cards = [
    {
      title: '总工单数',
      value: overview.total_orders.toLocaleString(),
      icon: FileText,
      description: '全部工单',
      color: '#1976d2',
      bgColor: '#e3f2fd',
    },
    {
      title: '已完成',
      value: overview.completed_orders.toLocaleString(),
      icon: CheckCircle,
      description: `完成率 ${completionRate}%`,
      color: '#2e7d32',
      bgColor: '#e8f5e8',
      badge: `${completionRate}%`,
      badgeColor: parseFloat(completionRate) > 80 ? 'success' : 'default' as 'success' | 'default',
    },
    {
      title: '未分配',
      value: overview.unassigned_orders.toLocaleString(),
      icon: Clock,
      description: '待分配处理',
      color: '#ed6c02',
      bgColor: '#fff3e0',
      badge: overview.unassigned_orders > 0 ? '需关注' : '正常',
      badgeColor: overview.unassigned_orders > 0 ? 'error' : 'success' as 'error' | 'success',
    },
    {
      title: '紧急工单',
      value: overview.urgent_orders.toLocaleString(),
      icon: AlertTriangle,
      description: `占比 ${urgentRate}%`,
      color: '#d32f2f',
      bgColor: '#ffebee',
      badge: `${urgentRate}%`,
      badgeColor: parseFloat(urgentRate) > 20 ? 'error' : 'default' as 'error' | 'default',
    },
    {
      title: '涉及项目',
      value: overview.unique_projects.toLocaleString(),
      icon: Building,
      description: '不同项目',
      color: '#7b1fa2',
      bgColor: '#f3e5f5',
    },
    {
      title: '服务客户',
      value: overview.unique_customers.toLocaleString(),
      icon: Users,
      description: '不同客户',
      color: '#303f9f',
      bgColor: '#e8eaf6',
    },
    {
      title: '服务代理',
      value: overview.unique_agents.toLocaleString(),
      icon: UserCheck,
      description: '不同代理人',
      color: '#0288d1',
      bgColor: '#e1f5fe',
    },
    {
      title: '满意度调查',
      value: overview.surveyed_orders.toLocaleString(),
      icon: Star,
      description: '已调查工单',
      color: '#f57c00',
      bgColor: '#fff8e1',
    },
  ]

  return (
    <MuiGrid container spacing={3}>
      {cards.map((card, index) => (
        <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
          <Card 
            sx={{ 
              height: '100%',
              transition: 'box-shadow 0.3s ease-in-out',
              '&:hover': {
                boxShadow: 4,
              }
            }}
          >
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="body2" color="text.secondary">
                  {card.title}
                </Typography>
                <Box 
                  sx={{ 
                    p: 1, 
                    borderRadius: 2, 
                    backgroundColor: card.bgColor,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <card.icon sx={{ color: card.color, fontSize: 20 }} />
                </Box>
              </Box>
              
              <Box display="flex" justifyContent="space-between" alignItems="flex-end">
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" mb={0.5}>
                    {card.value}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {card.description}
                  </Typography>
                </Box>
                {card.badge && (
                  <Chip 
                    label={card.badge} 
                    color={card.badgeColor}
                    size="small"
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </MuiGrid>
  )
} 