# PLAN模式 - 数据质量问题修复详细计划

## 📋 项目概述

**项目类型**: 数据质量紧急修复  
**复杂度等级**: Level 3 (中等复杂度)  
**预计工期**: 2-3天 (立即修复) + 2周 (中期优化)  
**紧急程度**: 🚨 高优先级  

### 问题规模评估
基于VAN模式分析，识别出4个关键数据质量问题：
1. **满意度趋势图为空** - 79.9%数据被忽略
2. **对话量数据异常膨胀** - 数量夸大5-20倍
3. **满意度覆盖率计算错误** - 低估10倍
4. **问题分类数据全部为空** - 0%分类准确率

## 🎯 项目目标

### 立即目标 (1-2天)
- 修复满意度趋势图显示问题
- 纠正对话量统计逻辑
- 实现基础问题分类功能
- 恢复关键业务指标准确性

### 中期目标 (1-2周)
- 建立智能问题分类系统
- 完善数据质量监控
- 实现数据验证自动化

### 长期目标 (1个月)
- 引入NLP问题分类服务
- 建立分类标签管理系统
- 实现持续数据质量优化

## 📊 影响组件分析

### 主要影响文件
1. **lib/mysql-conversation-stats.ts** - 核心数据查询逻辑
2. **hooks/use-analytics-data.ts** - 前端数据接口
3. **components/ui/detailed-topic-table.tsx** - 问题分类显示组件
4. **lib/satisfaction-mapper.ts** - 满意度映射逻辑

### 影响的API端点
- `/api/conversation-stats` - 对话统计API
- `/api/dashboard-stats` - 仪表板统计API
- `/api/lead-analytics` - 线索分析API (轻微影响)

### 影响的前端组件
- 满意度趋势图表
- 问题分类详细表格
- 对话量统计卡片
- 覆盖率显示组件

## 🛠️ 详细实施计划

### 阶段1: 立即修复 (Day 1-2)

#### 1.1 修复满意度趋势图 (4小时)
**文件**: `lib/mysql-conversation-stats.ts`  
**位置**: 第142行

**当前问题**:
```typescript
0 AS very_unsatisfied  // 硬编码忽略S0数据
```

**修复方案**:
```typescript
COUNT(CASE WHEN cl.satisfaction = 'S0' THEN 1 END) AS very_unsatisfied
```

**实施步骤**:
1. 备份当前文件
2. 修改第142行代码
3. 测试满意度查询功能
4. 验证前端图表显示
5. 部署到生产环境

**验证标准**:
- 满意度趋势图显示数据
- S0代码统计正确 (79.9%比例)
- NPS计算准确

#### 1.2 修复对话量统计逻辑 (6小时)
**文件**: `lib/mysql-conversation-stats.ts` (多处)

**当前问题**:
```sql
COUNT(*) AS conversation_count  -- 统计消息数
```

**修复方案**:
```sql
COUNT(DISTINCT cm.chat_id) AS conversation_count  -- 统计会话数
```

**影响位置**:
- getConversationCount函数 (第74行)
- getProjectRanking函数 (第361行)
- getHourlyAnalysis函数 (第426行)
- getCategoryProjectRanking函数 (第477行)

**实施步骤**:
1. 识别所有COUNT(*)使用位置
2. 逐一修改为COUNT(DISTINCT cm.chat_id)
3. 更新相关的计算逻辑
4. 批量测试所有统计功能
5. 比较修复前后数据差异

**验证标准**:
- 对话量减少到合理范围 (约1/5-1/20)
- 平均消息数计算正确
- 项目排名逻辑合理

#### 1.3 实现简化问题分类 (8小时)
**目标**: 基于现有biz_type实现三分类系统

**新增函数**:
```typescript
function getSimplifiedTopicDistribution(filters: ConversationStatsFilters): Promise<any[]> {
  const query = `
    SELECT 
      DATE(FROM_UNIXTIME(cm.timestamp/1000)) AS date,
      COUNT(DISTINCT cm.chat_id) AS total_conversations,
      COUNT(DISTINCT CASE WHEN cme.biz_type = 'IOC_TASK' THEN cm.chat_id END) AS work_order_problems,
      COUNT(DISTINCT CASE WHEN cme.biz_type = 'CHANCE' THEN cm.chat_id END) AS lead_inquiries,
      COUNT(DISTINCT CASE WHEN cme.biz_type IS NULL THEN cm.chat_id END) AS general_conversations
    FROM chat_msg cm
    LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
    WHERE ${whereClause}
    GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
    ORDER BY date DESC
  `
}
```

**实施步骤**:
1. 创建简化分类查询函数
2. 修改DetailedTopicDistribution接口
3. 更新前端显示组件
4. 测试分类统计功能
5. 验证数据准确性

**验证标准**:
- 三类分类数据正常显示
- 分类准确率达到60%+
- 前端图表正常渲染

### 阶段2: 中期优化 (Week 2-3)

#### 2.1 智能内容分类系统 (3天)
**目标**: 基于消息内容实现30+详细分类

**核心组件设计**:
```typescript
class MessageClassifier {
  private classificationRules = {
    '供水问题': [/供水|停水|水压|自来水|水管/i],
    '电费缴纳': [/电费|缴费|电费查询|电费单/i],
    '物业费': [/物业费|管理费|物业缴费/i],
    '电梯问题': [/电梯|停梯|电梯故障|电梯维修/i],
    '门窗维修': [/门窗|窗户|门锁|维修/i],
    '噪音投诉': [/噪音|吵闹|噪声|扰民/i],
    // ... 更多分类规则
  }

  classify(content: string): string {
    for (const [category, patterns] of Object.entries(this.classificationRules)) {
      if (patterns.some(pattern => pattern.test(content))) {
        return category
      }
    }
    return '其他'
  }
}
```

**实施计划**:
- Day 1: 设计分类规则和测试样本
- Day 2: 实现分类器和批量处理逻辑
- Day 3: 集成到查询系统和前端界面

#### 2.2 数据质量监控面板 (2天)
**目标**: 实时监控数据质量指标

**监控指标**:
- 满意度数据覆盖率
- 问题分类准确率
- 事件关联完整性
- 查询性能指标
- 数据一致性检查

**实施组件**:
- 数据质量检查脚本
- 监控面板组件
- 告警机制
- 质量报告生成

### 阶段3: 长期改进 (Week 4)

#### 3.1 NLP问题分类服务 (5天)
- 集成第三方NLP API
- 训练自定义分类模型
- 实现分类置信度评估
- 建立人工标注系统

#### 3.2 分类标签管理系统 (3天)
- 分类标签CRUD管理
- 分类规则配置界面
- 分类效果评估工具
- 分类历史数据迁移

## 📋 实施检查清单

### 立即修复任务
- [ ] **满意度S0代码修复**
  - [ ] 备份lib/mysql-conversation-stats.ts
  - [ ] 修改第142行很不满意字段逻辑
  - [ ] 测试getSurveyResults函数
  - [ ] 验证满意度趋势图显示
  - [ ] 部署生产环境

- [ ] **对话量统计修复**
  - [ ] 识别所有COUNT(*)使用位置
  - [ ] 修改为COUNT(DISTINCT cm.chat_id)
  - [ ] 更新计算逻辑
  - [ ] 测试所有统计功能
  - [ ] 验证数据合理性

- [ ] **覆盖率计算修复**
  - [ ] 修正覆盖率计算公式
  - [ ] 更新分母为对话总数
  - [ ] 测试覆盖率显示
  - [ ] 验证20.1%的正确覆盖率

- [ ] **简化问题分类实现**
  - [ ] 创建三分类查询函数
  - [ ] 修改接口定义
  - [ ] 更新前端组件
  - [ ] 测试分类功能
  - [ ] 验证60%+准确率

### 中期优化任务
- [ ] **智能分类系统**
  - [ ] 设计30+分类规则
  - [ ] 实现MessageClassifier类
  - [ ] 批量处理历史数据
  - [ ] 集成到查询系统
  - [ ] 前端界面更新

- [ ] **数据质量监控**
  - [ ] 建立质量检查脚本
  - [ ] 创建监控面板
  - [ ] 实现告警机制
  - [ ] 配置定时检查
  - [ ] 生成质量报告

### 长期改进任务
- [ ] **NLP分类服务**
  - [ ] 调研NLP服务选型
  - [ ] 实现API集成
  - [ ] 训练自定义模型
  - [ ] 评估分类效果
  - [ ] 优化分类规则

## 🚨 风险评估与缓解策略

### 高风险项
1. **数据一致性风险**
   - 风险: 修改统计逻辑可能影响历史数据对比
   - 缓解: 保留旧逻辑作为对比，提供数据迁移说明

2. **性能影响风险**
   - 风险: DISTINCT查询可能影响性能
   - 缓解: 添加适当索引，进行性能测试

3. **分类准确性风险**
   - 风险: 基于关键词的分类可能不够准确
   - 缓解: 建立样本测试集，持续优化规则

### 中风险项
1. **前端组件兼容性**
   - 风险: 接口变更可能影响前端显示
   - 缓解: 保持接口格式兼容，渐进式更新

2. **用户接受度风险**
   - 风险: 数据变化可能引起用户疑问
   - 缓解: 提供详细说明文档，逐步推进

## 📈 成功指标

### 立即修复成功标准
- 满意度趋势图正常显示，包含S0数据
- 对话量统计减少到合理范围 (预计减少80-95%)
- 问题分类显示三类基础分类，准确率≥60%
- 满意度覆盖率显示20.1%

### 中期优化成功标准
- 问题分类增加到30+类别
- 分类准确率提升到80%+
- 数据质量监控面板正常运行
- 告警机制有效工作

### 长期改进成功标准
- NLP分类准确率达到90%+
- 分类标签管理系统稳定运行
- 数据质量持续提升
- 用户满意度明显改善

## 🔄 下一阶段建议

### 如果选择CREATIVE模式
**适用场景**: 需要设计复杂的NLP分类算法或创新的数据质量监控架构

**创意需求**:
- 高级NLP分类算法设计
- 创新的数据质量可视化方案
- 智能分类规则学习系统

### 如果选择IMPLEMENT模式
**适用场景**: 立即开始修复关键问题，快速恢复业务功能

**实施重点**:
- 优先修复满意度和对话量问题
- 快速实现简化分类功能
- 保证系统稳定运行

---
**PLAN模式状态**: ✅ **已完成**  
**建议下一模式**: **IMPLEMENT** (立即修复) 或 **CREATIVE** (设计创新方案)  
**计划创建时间**: 2025-01-29  
**预计开始时间**: 立即 