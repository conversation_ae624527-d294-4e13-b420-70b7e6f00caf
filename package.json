{"name": "chat-analytics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "ANALYZE=true npm run build", "prepare": "husky", "precommit": "lint-staged"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "eslint --fix"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"@clerk/localizations": "^3.16.0", "@clerk/nextjs": "^6.19.4", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.4", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/material-nextjs": "^7.1.0", "@mui/system": "^7.1.0", "@mui/x-charts": "^8.3.1", "@mui/x-data-grid": "^8.3.1", "@mui/x-date-pickers": "^8.3.1", "@mui/x-license": "^8.3.1", "@types/jsonwebtoken": "^9.0.9", "@types/react-window": "^1.8.8", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.15.0", "html2canvas": "^1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "next": "15.3.2", "next-intl": "^4.1.0", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "redis": "^5.5.6", "xlsx": "^0.18.5", "zod": "^3.25.32"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5"}}