-- Create a function to execute raw SQL queries
-- Note: This requires appropriate permissions and should be used carefully
CREATE OR REPLACE FUNCTION public.execute_raw_sql(sql text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE 'SELECT array_to_json(array_agg(row_to_json(t)))::jsonb FROM (' || sql || ') t' INTO result;
  RETURN COALESCE(result, '[]'::jsonb);
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$;

-- Grant permissions to use this function
ALTER FUNCTION public.execute_raw_sql(text) OWNER TO postgres;
GRANT EXECUTE ON FUNCTION public.execute_raw_sql(text) TO service_role;
REVOKE EXECUTE ON FUNCTION public.execute_raw_sql(text) FROM public;

-- Comment on the function
COMMENT ON FUNCTION public.execute_raw_sql(text) IS 'Executes a raw SQL query and returns the results as JSONB array'; 