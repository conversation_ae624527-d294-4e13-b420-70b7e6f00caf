-- 创建网格映射表 (vankeservice_grid_map)
CREATE TABLE IF NOT EXISTS public.vankeservice_grid_map (
  id SERIAL PRIMARY KEY,
  grid_code VARCHAR(50) NOT NULL UNIQUE,
  grid_name VARCHAR(100) NOT NULL,
  project_id VARCHAR(100),
  area_name VARCHAR(100),
  company_name VA<PERSON>HAR(100),
  region VARCHAR(50),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建管家信息表
CREATE TABLE IF NOT EXISTS public.steward_info (
  id SERIAL PRIMARY KEY,
  steward_id VARCHAR(50) NOT NULL UNIQUE,
  steward_name VARCHAR(100) NOT NULL,
  grid_code VARCHAR(50),
  project_id VARCHAR(100),
  phone VARCHAR(20),
  email VARCHAR(100),
  status VARCHAR(20) DEFAULT 'active',
  hire_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (grid_code) REFERENCES vankeservice_grid_map(grid_code) ON DELETE SET NULL
);

-- 创建对话-管家关联表
CREATE TABLE IF NOT EXISTS public.conversation_steward (
  id SERIAL PRIMARY KEY,
  conversation_id INTEGER,
  steward_id VARCHAR(50),
  interaction_type VARCHAR(50), -- 'assigned', 'handled', 'transferred'
  interaction_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (steward_id) REFERENCES steward_info(steward_id) ON DELETE SET NULL
);

-- 创建网格活跃度统计表
CREATE TABLE IF NOT EXISTS public.grid_activity_stats (
  id SERIAL PRIMARY KEY,
  grid_code VARCHAR(50) NOT NULL,
  date DATE NOT NULL,
  conversation_count INTEGER DEFAULT 0,
  work_order_count INTEGER DEFAULT 0,
  lead_count INTEGER DEFAULT 0,
  satisfaction_score DECIMAL(3,2) DEFAULT 0,
  response_time_avg INTEGER DEFAULT 0, -- 平均响应时间（分钟）
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(grid_code, date),
  FOREIGN KEY (grid_code) REFERENCES vankeservice_grid_map(grid_code) ON DELETE CASCADE
);

-- 创建管家活跃度统计表
CREATE TABLE IF NOT EXISTS public.steward_activity_stats (
  id SERIAL PRIMARY KEY,
  steward_id VARCHAR(50) NOT NULL,
  date DATE NOT NULL,
  conversations_handled INTEGER DEFAULT 0,
  work_orders_created INTEGER DEFAULT 0,
  leads_generated INTEGER DEFAULT 0,
  avg_satisfaction_score DECIMAL(3,2) DEFAULT 0,
  avg_response_time INTEGER DEFAULT 0, -- 平均响应时间（分钟）
  active_hours DECIMAL(4,2) DEFAULT 0, -- 当日活跃小时数
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(steward_id, date),
  FOREIGN KEY (steward_id) REFERENCES steward_info(steward_id) ON DELETE CASCADE
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_vankeservice_grid_map_project ON vankeservice_grid_map(project_id);
CREATE INDEX IF NOT EXISTS idx_vankeservice_grid_map_area ON vankeservice_grid_map(area_name);
CREATE INDEX IF NOT EXISTS idx_vankeservice_grid_map_status ON vankeservice_grid_map(status);

CREATE INDEX IF NOT EXISTS idx_steward_info_grid ON steward_info(grid_code);
CREATE INDEX IF NOT EXISTS idx_steward_info_project ON steward_info(project_id);
CREATE INDEX IF NOT EXISTS idx_steward_info_status ON steward_info(status);

CREATE INDEX IF NOT EXISTS idx_conversation_steward_conv ON conversation_steward(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_steward_steward ON conversation_steward(steward_id);
CREATE INDEX IF NOT EXISTS idx_conversation_steward_time ON conversation_steward(interaction_time);

CREATE INDEX IF NOT EXISTS idx_grid_activity_date ON grid_activity_stats(date);
CREATE INDEX IF NOT EXISTS idx_grid_activity_grid_date ON grid_activity_stats(grid_code, date);

CREATE INDEX IF NOT EXISTS idx_steward_activity_date ON steward_activity_stats(date);
CREATE INDEX IF NOT EXISTS idx_steward_activity_steward_date ON steward_activity_stats(steward_id, date);

-- 添加更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vankeservice_grid_map_updated_at BEFORE UPDATE ON vankeservice_grid_map FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_steward_info_updated_at BEFORE UPDATE ON steward_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE vankeservice_grid_map IS '万科服务网格映射表';
COMMENT ON TABLE steward_info IS '管家信息表';
COMMENT ON TABLE conversation_steward IS '对话-管家关联表';
COMMENT ON TABLE grid_activity_stats IS '网格活跃度统计表';
COMMENT ON TABLE steward_activity_stats IS '管家活跃度统计表';

-- 插入一些示例数据
INSERT INTO vankeservice_grid_map (grid_code, grid_name, project_id, area_name, company_name, region) VALUES
('G001', '深圳湾网格', 'VK_ShenZhenBay', '深圳', '万科物业深圳公司', '华南'),
('G002', '前海网格', 'VK_QianHai', '深圳', '万科物业深圳公司', '华南'),
('G003', '宝安网格', 'VK_BaoAn', '深圳', '万科物业深圳公司', '华南'),
('G004', '上海浦东网格', 'VK_PuDong', '上海', '万科物业上海公司', '华东'),
('G005', '北京朝阳网格', 'VK_ChaoYang', '北京', '万科物业北京公司', '华北')
ON CONFLICT (grid_code) DO NOTHING;

INSERT INTO steward_info (steward_id, steward_name, grid_code, project_id, phone, email) VALUES
('ST001', '张三', 'G001', 'VK_ShenZhenBay', '13800138001', '<EMAIL>'),
('ST002', '李四', 'G002', 'VK_QianHai', '13800138002', '<EMAIL>'),
('ST003', '王五', 'G003', 'VK_BaoAn', '13800138003', '<EMAIL>'),
('ST004', '赵六', 'G004', 'VK_PuDong', '13800138004', '<EMAIL>'),
('ST005', '孙七', 'G005', 'VK_ChaoYang', '13800138005', '<EMAIL>')
ON CONFLICT (steward_id) DO NOTHING; 