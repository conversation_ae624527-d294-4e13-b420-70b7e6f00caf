import { getRedisClient, isRedisAvailable } from './redis-client'
import { createHash } from 'crypto'

/**
 * 缓存策略配置接口
 */
export interface CacheConfig {
  ttl: number           // 缓存存活时间（秒）
  keyPrefix: string     // 缓存键前缀
  enableCompression?: boolean  // 是否启用压缩
  fallbackToDb?: boolean      // Redis失效时是否回退到数据库
}

/**
 * 缓存性能监控接口
 */
export interface CacheMetrics {
  hits: number
  misses: number
  errors: number
  totalRequests: number
  avgResponseTime: number
}

/**
 * 智能缓存策略管理器
 */
export class CacheStrategyManager {
  private static instance: CacheStrategyManager
  private metrics: Map<string, CacheMetrics> = new Map()

  // 预定义的缓存策略配置
  private readonly CACHE_CONFIGS: Record<string, CacheConfig> = {
    // 慢查询 - 长时间缓存
    'conversion_rates': {
      ttl: 900,           // 15分钟
      keyPrefix: 'analytics:conversion',
      enableCompression: true,
      fallbackToDb: true
    },
    'project_ranking': {
      ttl: 600,           // 10分钟
      keyPrefix: 'analytics:ranking',
      enableCompression: true,
      fallbackToDb: true
    },
    'hourly_analysis': {
      ttl: 1800,          // 30分钟
      keyPrefix: 'analytics:hourly',
      enableCompression: true,
      fallbackToDb: true
    },
    
    // 中等查询 - 中等时间缓存
    'survey_results': {
      ttl: 300,           // 5分钟
      keyPrefix: 'analytics:survey',
      enableCompression: false,
      fallbackToDb: true
    },
    'intelligent_topic_distribution': {
      ttl: 600,           // 10分钟
      keyPrefix: 'analytics:topic',
      enableCompression: true,
      fallbackToDb: true
    },
    
    // 快查询 - 短时间缓存
    'conversation_count': {
      ttl: 180,           // 3分钟
      keyPrefix: 'analytics:count',
      enableCompression: false,
      fallbackToDb: true
    },
    'active_projects_trend': {
      ttl: 300,           // 5分钟
      keyPrefix: 'analytics:projects',
      enableCompression: false,
      fallbackToDb: true
    }
  }

  private constructor() {}

  public static getInstance(): CacheStrategyManager {
    if (!CacheStrategyManager.instance) {
      CacheStrategyManager.instance = new CacheStrategyManager()
    }
    return CacheStrategyManager.instance
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(type: string, filters: any): string {
    const config = this.CACHE_CONFIGS[type]
    if (!config) {
      return `analytics:default:${type}:${this.hashObject(filters)}`
    }

    // 生成过滤器的哈希值，确保键的一致性
    const filterHash = this.hashObject(filters)
    return `${config.keyPrefix}:${type}:${filterHash}`
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig(type: string): CacheConfig {
    return this.CACHE_CONFIGS[type] || {
      ttl: 300,
      keyPrefix: 'analytics:default',
      enableCompression: false,
      fallbackToDb: true
    }
  }

  /**
   * 缓存查询结果
   */
  async cacheResult(key: string, data: any, config: CacheConfig): Promise<void> {
    try {
      const redis = await getRedisClient()
      let serializedData = JSON.stringify(data)

      // 压缩处理（如果数据量大且启用压缩）
      if (config.enableCompression && serializedData.length > 1024) {
        // 这里可以添加压缩逻辑（如gzip）
        console.log(`📦 缓存数据压缩: ${key} (${serializedData.length} bytes)`)
      }

      await redis.setEx(key, config.ttl, serializedData)
      console.log(`✅ 缓存设置成功: ${key} (TTL: ${config.ttl}s)`)
    } catch (error) {
      console.error('❌ 缓存设置失败:', error)
      this.recordMetric(key, 'error')
    }
  }

  /**
   * 获取缓存结果
   */
  async getCachedResult(key: string): Promise<any | null> {
    try {
      const redis = await getRedisClient()
      const cached = await redis.get(key)
      
      if (cached) {
        this.recordMetric(key, 'hit')
        console.log(`🎯 缓存命中: ${key}`)
        return JSON.parse(cached)
      } else {
        this.recordMetric(key, 'miss')
        console.log(`🔍 缓存未命中: ${key}`)
        return null
      }
    } catch (error) {
      console.error('❌ 缓存获取失败:', error)
      this.recordMetric(key, 'error')
      return null
    }
  }

  /**
   * 批量失效缓存
   */
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const redis = await getRedisClient()
      const keys = await redis.keys(pattern)
      
      if (keys.length > 0) {
        await redis.del(keys)
        console.log(`🗑️ 批量删除缓存: ${keys.length}个键 (模式: ${pattern})`)
      }
    } catch (error) {
      console.error('❌ 缓存失效失败:', error)
    }
  }

  /**
   * 清除特定类型的所有缓存
   */
  async clearCacheByType(type: string): Promise<void> {
    const config = this.getCacheConfig(type)
    const pattern = `${config.keyPrefix}:${type}:*`
    await this.invalidatePattern(pattern)
  }

  /**
   * 获取缓存性能指标
   */
  getCacheMetrics(key?: string): CacheMetrics | Map<string, CacheMetrics> {
    if (key) {
      return this.metrics.get(key) || {
        hits: 0,
        misses: 0,
        errors: 0,
        totalRequests: 0,
        avgResponseTime: 0
      }
    }
    return this.metrics
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics.clear()
    console.log('📊 缓存性能指标已重置')
  }

  /**
   * 生成对象哈希值
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    return createHash('md5').update(str).digest('hex').substring(0, 12)
  }

  /**
   * 记录性能指标
   */
  private recordMetric(key: string, type: 'hit' | 'miss' | 'error'): void {
    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        hits: 0,
        misses: 0,
        errors: 0,
        totalRequests: 0,
        avgResponseTime: 0
      })
    }

    const metric = this.metrics.get(key)!
    metric.totalRequests++
    
    switch (type) {
      case 'hit':
        metric.hits++
        break
      case 'miss':
        metric.misses++
        break
      case 'error':
        metric.errors++
        break
    }

    this.metrics.set(key, metric)
  }
}

// 导出单例实例
export const cacheStrategy = CacheStrategyManager.getInstance()

/**
 * 缓存装饰器函数 - 用于包装慢查询函数
 */
export function withCache(type: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now()
      const filters = args[0] || {}
      const cacheKey = cacheStrategy.generateCacheKey(type, filters)
      const config = cacheStrategy.getCacheConfig(type)

      // 尝试从缓存获取
      const cachedResult = await cacheStrategy.getCachedResult(cacheKey)
      if (cachedResult !== null) {
        const responseTime = Date.now() - startTime
        console.log(`⚡ 缓存返回: ${type} (${responseTime}ms)`)
        return cachedResult
      }

      // 缓存未命中，执行原始查询
      console.log(`🔍 执行查询: ${type} (缓存未命中)`)
      try {
        const result = await originalMethod.apply(this, args)
        const responseTime = Date.now() - startTime

        // 缓存结果
        await cacheStrategy.cacheResult(cacheKey, result, config)
        
        console.log(`✅ 查询完成: ${type} (${responseTime}ms)`)
        return result
      } catch (error) {
        const responseTime = Date.now() - startTime
        console.error(`❌ 查询失败: ${type} (${responseTime}ms)`, error)
        throw error
      }
    }

    return descriptor
  }
} 