export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      conversation: {
        Row: {
          id: number
          project: string | null
          group: string | null
          area: string | null
          company: string | null
          conversation_created: string | null
          sys_created: string | null
          language: string | null
          survey: string | null
          work_order: boolean | null
          lead: boolean | null
          message_count: number | null
          topic: string | null
          emotion: string | null
        }
        Insert: {
          id?: number
          project?: string | null
          group?: string | null
          area?: string | null
          company?: string | null
          conversation_created?: string | null
          sys_created?: string | null
          language?: string | null
          survey?: string | null
          work_order?: boolean | null
          lead?: boolean | null
          message_count?: number | null
          topic?: string | null
          emotion?: string | null
        }
        Update: {
          id?: number
          project?: string | null
          group?: string | null
          area?: string | null
          company?: string | null
          conversation_created?: string | null
          sys_created?: string | null
          language?: string | null
          survey?: string | null
          work_order?: boolean | null
          lead?: boolean | null
          message_count?: number | null
          topic?: string | null
          emotion?: string | null
        }
      }
      project: {
        Row: {
          project: string
          group: string | null
          area: string | null
          company: string | null
        }
        Insert: {
          project: string
          group?: string | null
          area?: string | null
          company?: string | null
        }
        Update: {
          project?: string
          group?: string | null
          area?: string | null
          company?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      execute_raw_sql: {
        Args: {
          sql: string
        }
        Returns: unknown
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
