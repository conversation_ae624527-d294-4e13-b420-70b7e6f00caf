/**
 * 满意度代码映射工具
 * 将数据库中的满意度代码转换为用户友好的标签和分数
 */
/**
 * 满意度代码映射表
 * 基于实际数据分析结果定制
 */
export const SATISFACTION_MAPPING = {
    // 推荐者 (Promoters) - 非常满意
    'S10': {
        score: 5,
        label: '非常满意',
        category: 'promoter',
        color: '#10B981' // green-500
    },
    // 被动者 (Passives) - 满意/一般
    'S08': {
        score: 4,
        label: '满意',
        category: 'passive',
        color: '#3B82F6' // blue-500
    },
    'S05': {
        score: 4,
        label: '满意',
        category: 'passive',
        color: '#3B82F6' // blue-500
    },
    'S02': {
        score: 3,
        label: '一般',
        category: 'passive',
        color: '#F59E0B' // amber-500
    },
    // 批评者 (Detractors) - 不满意
    'S00': {
        score: 2,
        label: '不满意',
        category: 'detractor',
        color: '#F97316' // orange-500
    },
    'S0': {
        score: 1,
        label: '非常不满意',
        category: 'detractor',
        color: '#EF4444' // red-500
    }
};
/**
 * 获取满意度映射信息
 */
export function mapSatisfactionCode(code) {
    return SATISFACTION_MAPPING[code] || {
        score: 0,
        label: '未评价',
        category: 'passive',
        color: '#6B7280' // gray-500
    };
}
/**
 * 获取满意度统计摘要
 */
export function getSatisfactionSummary(satisfactionCodes) {
    const distribution = {};
    let promoters = 0;
    let passives = 0;
    let detractors = 0;
    satisfactionCodes.forEach(code => {
        const mapping = mapSatisfactionCode(code);
        distribution[mapping.label] = (distribution[mapping.label] || 0) + 1;
        switch (mapping.category) {
            case 'promoter':
                promoters++;
                break;
            case 'passive':
                passives++;
                break;
            case 'detractor':
                detractors++;
                break;
        }
    });
    const total = satisfactionCodes.length;
    const nps = total > 0 ? Math.round(((promoters - detractors) / total) * 100) : 0;
    return {
        total,
        promoters,
        passives,
        detractors,
        nps,
        distribution
    };
}
/**
 * 格式化满意度百分比
 */
export function formatSatisfactionRate(satisfied, total) {
    if (total === 0)
        return '0.00%';
    return ((satisfied / total) * 100).toFixed(2) + '%';
}
/**
 * 获取满意度趋势分析
 */
export function analyzeSatisfactionTrend(data) {
    return data.map(item => {
        const summary = getSatisfactionSummary(item.satisfaction_codes);
        return {
            date: item.date,
            nps: summary.nps,
            promoter_rate: summary.total > 0 ? (summary.promoters / summary.total) * 100 : 0,
            detractor_rate: summary.total > 0 ? (summary.detractors / summary.total) * 100 : 0,
            total_responses: summary.total
        };
    });
}
/**
 * 满意度数据验证
 */
export function validateSatisfactionData(codes) {
    const issues = [];
    let valid = 0;
    let invalid = 0;
    codes.forEach(code => {
        if (code && SATISFACTION_MAPPING[code]) {
            valid++;
        }
        else {
            invalid++;
            if (code && !SATISFACTION_MAPPING[code]) {
                issues.push(`未知满意度代码: ${code}`);
            }
        }
    });
    const total = valid + invalid;
    const coverage_rate = total > 0 ? (valid / total) * 100 : 0;
    return {
        valid,
        invalid,
        coverage_rate,
        issues: [...new Set(issues)] // 去重
    };
}
