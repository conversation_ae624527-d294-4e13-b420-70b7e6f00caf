import { getConnection } from './mysql-db'
import { getRedisClient } from './redis-client'
import { cacheStrategy } from './cache-strategy'

/**
 * 预计算汇总缓存管理器
 * 第二阶段：实现智能预计算和汇总数据缓存
 */
export class PrecomputedCacheManager {
  private static instance: PrecomputedCacheManager

  // 预计算任务配置
  private readonly PRECOMPUTE_CONFIGS = {
    // 每日汇总数据 - 每小时预计算
    'daily_summary': {
      schedule: '0 * * * *', // 每小时执行
      ttl: 7200,            // 2小时缓存
      priority: 'high'
    },
    // 项目排名汇总 - 每30分钟预计算
    'project_ranking_summary': {
      schedule: '*/30 * * * *', // 每30分钟执行
      ttl: 3600,              // 1小时缓存
      priority: 'high'
    },
    // 时段分析汇总 - 每15分钟预计算
    'hourly_summary': {
      schedule: '*/15 * * * *', // 每15分钟执行
      ttl: 1800,              // 30分钟缓存
      priority: 'medium'
    },
    // 转化率趋势汇总 - 每小时预计算
    'conversion_trends': {
      schedule: '5 * * * *',  // 每小时第5分钟执行
      ttl: 3600,            // 1小时缓存
      priority: 'high'
    }
  }

  private constructor() {}

  public static getInstance(): PrecomputedCacheManager {
    if (!PrecomputedCacheManager.instance) {
      PrecomputedCacheManager.instance = new PrecomputedCacheManager()
    }
    return PrecomputedCacheManager.instance
  }

  /**
   * 预计算每日汇总数据
   */
  async precomputeDailySummary(): Promise<void> {
    console.log('🔄 开始预计算每日汇总数据...')
    
    try {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000) // 30天前

      // 预计算基础指标
      const summaryData = await this.computeDailySummaryData(startDate, endDate)
      
      // 缓存预计算结果
      const cacheKey = `precomputed:daily_summary:${this.getDateHash(startDate, endDate)}`
      await this.cachePrecomputedData(cacheKey, summaryData, 'daily_summary')
      
      console.log(`✅ 每日汇总数据预计算完成: ${summaryData.length} 条记录`)
    } catch (error) {
      console.error('❌ 每日汇总数据预计算失败:', error)
    }
  }

  /**
   * 预计算项目排名汇总
   */
  async precomputeProjectRankingSummary(): Promise<void> {
    console.log('🔄 开始预计算项目排名汇总...')
    
    try {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000) // 7天前

      // 预计算项目排名数据
      const rankingData = await this.computeProjectRankingData(startDate, endDate)
      
      // 缓存预计算结果
      const cacheKey = `precomputed:project_ranking:${this.getDateHash(startDate, endDate)}`
      await this.cachePrecomputedData(cacheKey, rankingData, 'project_ranking_summary')
      
      console.log(`✅ 项目排名汇总预计算完成: ${rankingData.length} 条记录`)
    } catch (error) {
      console.error('❌ 项目排名汇总预计算失败:', error)
    }
  }

  /**
   * 预计算时段分析汇总
   */
  async precomputeHourlyAnalysisSummary(): Promise<void> {
    console.log('🔄 开始预计算时段分析汇总...')
    
    try {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000) // 7天前

      // 预计算时段分析数据
      const hourlyData = await this.computeHourlyAnalysisData(startDate, endDate)
      
      // 缓存预计算结果
      const cacheKey = `precomputed:hourly_analysis:${this.getDateHash(startDate, endDate)}`
      await this.cachePrecomputedData(cacheKey, hourlyData, 'hourly_summary')
      
      console.log(`✅ 时段分析汇总预计算完成: ${hourlyData.length} 条记录`)
    } catch (error) {
      console.error('❌ 时段分析汇总预计算失败:', error)
    }
  }

  /**
   * 预计算转化率趋势汇总
   */
  async precomputeConversionTrends(): Promise<void> {
    console.log('🔄 开始预计算转化率趋势汇总...')
    
    try {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000) // 30天前

      // 预计算转化率趋势数据
      const conversionData = await this.computeConversionTrendsData(startDate, endDate)
      
      // 缓存预计算结果
      const cacheKey = `precomputed:conversion_trends:${this.getDateHash(startDate, endDate)}`
      await this.cachePrecomputedData(cacheKey, conversionData, 'conversion_trends')
      
      console.log(`✅ 转化率趋势汇总预计算完成: ${conversionData.length} 条记录`)
    } catch (error) {
      console.error('❌ 转化率趋势汇总预计算失败:', error)
    }
  }

  /**
   * 计算每日汇总数据 - 优化的SQL查询
   */
  private async computeDailySummaryData(startDate: Date, endDate: Date): Promise<any[]> {
    const connection = await getConnection()
    try {
      // 优化的单一查询，减少JOIN复杂度
      const query = `
        SELECT 
          DATE(FROM_UNIXTIME(cm.timestamp/1000)) AS date,
          COUNT(DISTINCT cm.chat_id) AS total_conversations,
          COUNT(DISTINCT cm.message_id) AS total_messages,
          AVG(LENGTH(cm.payload)) AS avg_message_length,
          COUNT(DISTINCT cm.bot_id) AS active_bots,
          MIN(FROM_UNIXTIME(cm.timestamp/1000)) AS first_message_time,
          MAX(FROM_UNIXTIME(cm.timestamp/1000)) AS last_message_time
        FROM chat_msg cm
        WHERE cm.timestamp IS NOT NULL 
          AND FROM_UNIXTIME(cm.timestamp/1000) >= ? 
          AND FROM_UNIXTIME(cm.timestamp/1000) <= ?
        GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
        ORDER BY date DESC
      `
      
      const [rows] = await connection.execute(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0] + ' 23:59:59'
      ])
      
      return rows as any[]
    } finally {
      connection.release()
    }
  }

  /**
   * 计算项目排名数据 - 优化的聚合查询
   */
  private async computeProjectRankingData(startDate: Date, endDate: Date): Promise<any[]> {
    const connection = await getConnection()
    try {
      // 优化的项目聚合查询
      const query = `
        SELECT 
          cl.project_code,
          cl.project_name,
          COUNT(DISTINCT cm.chat_id) AS conversation_count,
          COUNT(DISTINCT cm.message_id) AS message_count,
          AVG(LENGTH(cm.payload)) AS avg_message_length,
          ROUND(COUNT(DISTINCT cm.chat_id) * 100.0 / 
            (SELECT COUNT(DISTINCT chat_id) FROM chat_msg 
             WHERE timestamp IS NOT NULL 
               AND FROM_UNIXTIME(timestamp/1000) >= ? 
               AND FROM_UNIXTIME(timestamp/1000) <= ?), 2) AS conversation_percentage
        FROM chat_msg cm
        LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
        WHERE cm.timestamp IS NOT NULL 
          AND FROM_UNIXTIME(cm.timestamp/1000) >= ? 
          AND FROM_UNIXTIME(cm.timestamp/1000) <= ?
          AND cl.project_code IS NOT NULL
        GROUP BY cl.project_code, cl.project_name
        ORDER BY conversation_count DESC
        LIMIT 50
      `
      
      const params = [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0] + ' 23:59:59',
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0] + ' 23:59:59'
      ]
      
      const [rows] = await connection.execute(query, params)
      return rows as any[]
    } finally {
      connection.release()
    }
  }

  /**
   * 计算时段分析数据 - 按小时汇总
   */
  private async computeHourlyAnalysisData(startDate: Date, endDate: Date): Promise<any[]> {
    const connection = await getConnection()
    try {
      // 按小时汇总的查询
      const query = `
        SELECT 
          HOUR(FROM_UNIXTIME(cm.timestamp/1000)) AS hour_num,
          COUNT(DISTINCT cm.chat_id) AS conversation_count,
          COUNT(DISTINCT cm.message_id) AS message_count,
          AVG(LENGTH(cm.payload)) AS avg_message_length,
          COUNT(DISTINCT cm.bot_id) AS active_bots
        FROM chat_msg cm
        WHERE cm.timestamp IS NOT NULL 
          AND FROM_UNIXTIME(cm.timestamp/1000) >= ? 
          AND FROM_UNIXTIME(cm.timestamp/1000) <= ?
        GROUP BY HOUR(FROM_UNIXTIME(cm.timestamp/1000))
        ORDER BY hour_num
      `
      
      const [rows] = await connection.execute(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0] + ' 23:59:59'
      ])
      
      return rows as any[]
    } finally {
      connection.release()
    }
  }

  /**
   * 计算转化率趋势数据
   */
  private async computeConversionTrendsData(startDate: Date, endDate: Date): Promise<any[]> {
    const connection = await getConnection()
    try {
      // 转化率趋势的简化查询
      const query = `
        SELECT 
          DATE(FROM_UNIXTIME(cm.timestamp/1000)) AS date,
          COUNT(DISTINCT cm.chat_id) AS total_conversations,
          COUNT(DISTINCT CASE WHEN cme.biz_type = 'CHANCE' THEN cm.chat_id END) AS lead_events,
          ROUND(COUNT(DISTINCT CASE WHEN cme.biz_type = 'CHANCE' THEN cm.chat_id END) * 100.0 / 
                COUNT(DISTINCT cm.chat_id), 2) AS conversion_rate
        FROM chat_msg cm
        LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
        WHERE cm.timestamp IS NOT NULL 
          AND FROM_UNIXTIME(cm.timestamp/1000) >= ? 
          AND FROM_UNIXTIME(cm.timestamp/1000) <= ?
        GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
        ORDER BY date DESC
      `
      
      const [rows] = await connection.execute(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0] + ' 23:59:59'
      ])
      
      return rows as any[]
    } finally {
      connection.release()
    }
  }

  /**
   * 缓存预计算数据
   */
  private async cachePrecomputedData(key: string, data: any, configType: string): Promise<void> {
    try {
      const redis = await getRedisClient()
      const config = this.PRECOMPUTE_CONFIGS[configType]
      
      const serializedData = JSON.stringify(data)
      await redis.setEx(key, config.ttl, serializedData)
      
      console.log(`💾 预计算数据已缓存: ${key} (TTL: ${config.ttl}s, 大小: ${serializedData.length} bytes)`)
    } catch (error) {
      console.error('❌ 预计算数据缓存失败:', error)
    }
  }

  /**
   * 获取预计算数据
   */
  async getPrecomputedData(type: string, startDate: Date, endDate: Date): Promise<any[] | null> {
    try {
      const redis = await getRedisClient()
      const cacheKey = `precomputed:${type}:${this.getDateHash(startDate, endDate)}`
      
      const cached = await redis.get(cacheKey)
      if (cached) {
        console.log(`⚡ 预计算数据命中: ${cacheKey}`)
        return JSON.parse(cached)
      }
      
      console.log(`🔍 预计算数据未命中: ${cacheKey}`)
      return null
    } catch (error) {
      console.error('❌ 获取预计算数据失败:', error)
      return null
    }
  }

  /**
   * 生成日期范围哈希
   */
  private getDateHash(startDate: Date, endDate: Date): string {
    const start = startDate.toISOString().split('T')[0]
    const end = endDate.toISOString().split('T')[0]
    return `${start}_${end}`.replace(/-/g, '')
  }

  /**
   * 执行所有预计算任务
   */
  async runAllPrecomputeTasks(): Promise<void> {
    console.log('🚀 开始执行所有预计算任务...')
    
    const tasks = [
      this.precomputeDailySummary(),
      this.precomputeProjectRankingSummary(),
      this.precomputeHourlyAnalysisSummary(),
      this.precomputeConversionTrends()
    ]

    try {
      await Promise.all(tasks)
      console.log('✅ 所有预计算任务执行完成')
    } catch (error) {
      console.error('❌ 预计算任务执行失败:', error)
    }
  }
}

// 导出单例实例
export const precomputedCache = PrecomputedCacheManager.getInstance()