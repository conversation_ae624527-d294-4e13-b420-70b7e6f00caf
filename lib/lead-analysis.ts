/**
 * 线索分析功能模块
 * 提供线索识别、转化率分析和客户画像功能
 */

import { getConnection } from './mysql-db';

// 执行数据库查询的通用函数
async function executeQuery(query: string, params: any[] = []): Promise<any[]> {
  const connection = await getConnection();
  try {
    const [rows] = await connection.execute(query, params);
    return rows as any[];
  } finally {
    connection.release();
  }
}

// 查询过滤器接口
export interface LeadAnalysisFilters {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  grid?: string;
  leadType?: string;
}

// 线索转化数据接口
export interface LeadConversionData {
  date: string;
  total_conversations: number;
  lead_events: number;
  lead_conversion_rate: number;
  avg_response_time: number;
  lead_sources: string[];
}

// 线索档案数据接口
export interface LeadProfileData {
  lead_id: string;
  customer_mobile: string;
  customer_name?: string;
  requirement_type: string;
  project_code: string;
  project_name: string;
  created_time: string;
  status: number;
  follow_up_count: number;
}

// 线索来源分析接口
export interface LeadSourceAnalysis {
  source: string;
  lead_count: number;
  conversion_rate: number;
  avg_value: number;
  trend: 'up' | 'down' | 'stable';
}

/**
 * 获取线索转化分析数据
 */
export async function getLeadConversionAnalysis(
  filters: LeadAnalysisFilters = {}
): Promise<LeadConversionData[]> {
  let whereConditions = ['cm.timestamp IS NOT NULL'];
  const params: any[] = [];

  // 构建时间条件
  if (filters.startDate && filters.endDate) {
    whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ? AND FROM_UNIXTIME(cm.timestamp/1000) <= ?');
    params.push(filters.startDate, `${filters.endDate} 23:59:59`);
  }

  // 项目过滤
  if (filters.projectId) {
    whereConditions.push('cl.project_code = ?');
    params.push(filters.projectId);
  }

  const whereClause = whereConditions.join(' AND ');

  // 修复后的查询 - 去除不存在的字段引用
  const query = `
    SELECT 
      DATE(FROM_UNIXTIME(cm.timestamp/1000)) AS date,
      COUNT(DISTINCT cm.chat_id) AS total_conversations,
      COUNT(DISTINCT CASE WHEN cme.biz_type = 'CHANCE' THEN cm.chat_id END) AS lead_events,
      COUNT(DISTINCT CASE WHEN cme.biz_type = 'CHANCE' THEN cm.chat_id END) AS actual_leads,
      0 AS avg_response_time
    FROM chat_msg cm
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
    LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
    LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
    WHERE ${whereClause}
    GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
    ORDER BY date DESC
    LIMIT 30
  `;

  const rawData = await executeQuery(query, params);

  return rawData.map((row: any) => ({
    date: row.date,
    total_conversations: row.total_conversations || 0,
    lead_events: row.lead_events || 0,
    lead_conversion_rate: row.total_conversations > 0 ? 
      Number(((row.actual_leads || 0) * 100 / row.total_conversations).toFixed(2)) : 0,
    avg_response_time: row.avg_response_time || 0,
    lead_sources: ['在线客服', 'APP咨询'] // 基于实际数据可以扩展
  }));
}

/**
 * 获取线索详细档案 - 修复版本
 */
export async function getLeadProfiles(
  filters: LeadAnalysisFilters = {}
): Promise<LeadProfileData[]> {
  let whereConditions = ['cme.biz_type = "CHANCE"'];
  const params: any[] = [];

  // 构建时间条件
  if (filters.startDate && filters.endDate) {
    whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ? AND FROM_UNIXTIME(cm.timestamp/1000) <= ?');
    params.push(filters.startDate, `${filters.endDate} 23:59:59`);
  }

  // 项目过滤
  if (filters.projectId) {
    whereConditions.push('cl.project_code = ?');
    params.push(filters.projectId);
  }

  const whereClause = whereConditions.join(' AND ');

  // 修复后的查询 - 通过chat_clue表获取客户信息
  const query = `
    SELECT 
      cm.chat_id AS lead_id,
      COALESCE(cc.customer_mobile, '') AS customer_mobile,
      COALESCE(cc.customer_name, '') AS customer_name,
      cme.biz_type AS requirement_type,
      cl.project_code,
      cl.project_name,
      FROM_UNIXTIME(cm.timestamp/1000) AS created_time,
      1 AS status,
      COUNT(DISTINCT cm.message_id) AS follow_up_count
    FROM chat_msg_event cme
    LEFT JOIN chat_msg cm ON cme.message_id = cm.message_id
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
    LEFT JOIN chat_clue cc ON cme.message_id = cc.message_id
    WHERE ${whereClause}
    GROUP BY cm.chat_id, cc.customer_mobile, cc.customer_name, 
             cme.biz_type, cl.project_code, cl.project_name, 
             cm.timestamp
    ORDER BY cm.timestamp DESC
    LIMIT 100
  `;

  const rawData = await executeQuery(query, params);

  return rawData.map((row: any) => ({
    lead_id: String(row.lead_id || ''),
    customer_mobile: row.customer_mobile || '',
    customer_name: row.customer_name || '',
    requirement_type: row.requirement_type || '线索',
    project_code: row.project_code || '',
    project_name: row.project_name || '',
    created_time: row.created_time || '',
    status: row.status || 0,
    follow_up_count: row.follow_up_count || 0
  }));
}

/**
 * 获取线索来源分析
 */
export async function getLeadSourceAnalysis(
  filters: LeadAnalysisFilters = {}
): Promise<LeadSourceAnalysis[]> {
  let whereConditions = ['cme.biz_type = "CHANCE"'];
  const params: any[] = [];

  // 构建时间条件
  if (filters.startDate && filters.endDate) {
    whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ? AND FROM_UNIXTIME(cm.timestamp/1000) <= ?');
    params.push(filters.startDate, `${filters.endDate} 23:59:59`);
  }

  const whereClause = whereConditions.join(' AND ');

  const query = `
    SELECT 
      cl.project_name AS source,
      COUNT(*) AS lead_count,
      COUNT(CASE WHEN cme.biz_type = 'CHANCE' THEN 1 END) AS converted_leads,
      500 AS avg_value
    FROM chat_msg_event cme
    LEFT JOIN chat_msg cm ON cme.message_id = cm.message_id
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
    WHERE ${whereClause}
    GROUP BY cl.project_name
    ORDER BY lead_count DESC
    LIMIT 10
  `;

  const rawData = await executeQuery(query, params);

  return rawData.map((row: any) => ({
    source: row.source || '未知来源',
    lead_count: row.lead_count || 0,
    conversion_rate: row.lead_count > 0 ? 
      Number((100).toFixed(2)) : 0, // 简化：所有线索事件都算100%转化
    avg_value: row.avg_value || 0,
    trend: 'stable' as const
  }));
}

/**
 * 获取线索质量评分
 */
export async function getLeadQualityScore(
  filters: LeadAnalysisFilters = {}
): Promise<{
  overall_score: number;
  quality_factors: Array<{
    factor: string;
    score: number;
    description: string;
  }>;
  recommendations: string[];
}> {
  const profiles = await getLeadProfiles(filters);
  const sources = await getLeadSourceAnalysis(filters);

  const qualityFactors = [
    {
      factor: '数据完整性',
      score: calculateDataCompleteness(profiles),
      description: '客户信息的完整度'
    },
    {
      factor: '响应时效',
      score: 85, // 模拟数据，实际需要计算响应时间
      description: '线索响应的及时性'
    },
    {
      factor: '转化率',
      score: calculateAverageConversionRate(sources),
      description: '线索到成交的转化效率'
    },
    {
      factor: '跟进频率',
      score: calculateFollowUpScore(profiles),
      description: '线索跟进的活跃度'
    }
  ];

  const overallScore = Math.round(
    qualityFactors.reduce((sum, factor) => sum + factor.score, 0) / qualityFactors.length
  );

  const recommendations = generateLeadRecommendations(qualityFactors, overallScore);

  return {
    overall_score: overallScore,
    quality_factors: qualityFactors,
    recommendations
  };
}

/**
 * 计算数据完整性评分
 */
function calculateDataCompleteness(profiles: LeadProfileData[]): number {
  if (profiles.length === 0) return 0;

  const completenessScores = profiles.map(profile => {
    let score = 0;
    if (profile.requirement_type && profile.requirement_type !== '未知') score += 40;
    if (profile.project_code) score += 30;
    if (profile.project_name) score += 20;
    if (profile.follow_up_count > 0) score += 10;
    return score;
  });

  return Math.round(
    completenessScores.reduce((sum, score) => sum + score, 0) / completenessScores.length
  );
}

/**
 * 计算平均转化率评分
 */
function calculateAverageConversionRate(sources: LeadSourceAnalysis[]): number {
  if (sources.length === 0) return 0;

  const avgConversionRate = sources.reduce((sum, source) => sum + source.conversion_rate, 0) / sources.length;
  return Math.min(100, Math.round(avgConversionRate * 2)); // 转化率乘以2作为评分
}

/**
 * 计算跟进频率评分
 */
function calculateFollowUpScore(profiles: LeadProfileData[]): number {
  if (profiles.length === 0) return 0;

  const avgFollowUps = profiles.reduce((sum, profile) => sum + profile.follow_up_count, 0) / profiles.length;
  return Math.min(100, Math.round(avgFollowUps * 10)); // 平均跟进次数乘以10作为评分
}

/**
 * 生成线索优化建议
 */
function generateLeadRecommendations(
  qualityFactors: Array<{ factor: string; score: number; description: string }>,
  overallScore: number
): string[] {
  const recommendations: string[] = [];

  if (overallScore < 70) {
    recommendations.push('线索质量总体偏低，建议全面优化线索管理流程');
  }

  qualityFactors.forEach(factor => {
    if (factor.score < 60) {
      switch (factor.factor) {
        case '数据完整性':
          recommendations.push('建议完善客户信息收集表单，提高数据完整性');
          break;
        case '响应时效':
          recommendations.push('建议优化客服响应机制，提升响应速度');
          break;
        case '转化率':
          recommendations.push('建议分析高转化率渠道特点，优化线索筛选策略');
          break;
        case '跟进频率':
          recommendations.push('建议增加线索跟进频次，建立系统化跟进流程');
          break;
      }
    }
  });

  return recommendations;
}

/**
 * 获取线索趋势分析
 */
export async function getLeadTrendAnalysis(
  filters: LeadAnalysisFilters = {}
): Promise<{
  daily_trends: Array<{
    date: string;
    lead_count: number;
    conversion_rate: number;
    quality_score: number;
  }>;
  growth_rate: number;
  peak_hours: number[];
  best_performing_projects: string[];
}> {
  const conversionData = await getLeadConversionAnalysis(filters);
  const sources = await getLeadSourceAnalysis(filters);

  const dailyTrends = conversionData.map(day => ({
    date: day.date,
    lead_count: day.lead_events,
    conversion_rate: day.lead_conversion_rate,
    quality_score: Math.round(Math.random() * 30 + 70) // 模拟质量评分
  }));

  // 计算增长率
  const growthRate = dailyTrends.length >= 2 ?
    ((dailyTrends[0].lead_count - dailyTrends[dailyTrends.length - 1].lead_count) / 
     dailyTrends[dailyTrends.length - 1].lead_count * 100) : 0;

  // 模拟高峰时段
  const peakHours = [9, 10, 14, 15, 16];

  // 最佳表现项目
  const bestPerformingProjects = sources
    .sort((a, b) => b.lead_count - a.lead_count)
    .slice(0, 3)
    .map(source => source.source);

  return {
    daily_trends: dailyTrends,
    growth_rate: Math.round(growthRate),
    peak_hours: peakHours,
    best_performing_projects: bestPerformingProjects
  };
} 