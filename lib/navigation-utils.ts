// Navigation utilities for preserving filter state across pages

interface FilterState {
  selectedMonth?: string | null
  selectedGroup?: string | null
  selectedArea?: string | null
  selectedCompany?: string | null
  selectedProject?: string | null
  selectedGrid?: string | null
  showGrids?: boolean
  startDate?: Date | null
  endDate?: Date | null
}

export function buildNavigationUrl(basePath: string, filterState: FilterState): string {
  const params = new URLSearchParams()
  
  // Add all non-empty filter parameters
  if (filterState.selectedMonth) {
    params.set('month', filterState.selectedMonth)
  }
  
  if (filterState.selectedGroup) {
    params.set('group', filterState.selectedGroup)
  }
  
  if (filterState.selectedArea) {
    params.set('area', filterState.selectedArea)
  }
  
  if (filterState.selectedCompany) {
    params.set('company', filterState.selectedCompany)
  }
  
  if (filterState.selectedProject) {
    params.set('project', filterState.selectedProject)
  }
  
  if (filterState.selectedGrid) {
    params.set('grid', filterState.selectedGrid)
  }
  
  if (filterState.showGrids !== undefined) {
    params.set('showGrids', String(filterState.showGrids))
  }
  
  if (filterState.startDate) {
    params.set('startDate', filterState.startDate.toISOString().split('T')[0])
  }
  
  if (filterState.endDate) {
    params.set('endDate', filterState.endDate.toISOString().split('T')[0])
  }
  
  const queryString = params.toString()
  return queryString ? `${basePath}?${queryString}` : basePath
}

export function parseUrlParams(searchParams: URLSearchParams): Partial<FilterState> {
  const filterState: Partial<FilterState> = {}
  
  if (searchParams.get('month')) {
    filterState.selectedMonth = searchParams.get('month')
  }
  
  if (searchParams.get('group')) {
    filterState.selectedGroup = searchParams.get('group')
  }
  
  if (searchParams.get('area')) {
    filterState.selectedArea = searchParams.get('area')
  }
  
  if (searchParams.get('company')) {
    filterState.selectedCompany = searchParams.get('company')
  }
  
  if (searchParams.get('project')) {
    filterState.selectedProject = searchParams.get('project')
  }
  
  if (searchParams.get('grid')) {
    filterState.selectedGrid = searchParams.get('grid')
  }
  
  if (searchParams.get('showGrids')) {
    filterState.showGrids = searchParams.get('showGrids') === 'true'
  }
  
  if (searchParams.get('startDate')) {
    filterState.startDate = new Date(searchParams.get('startDate')!)
  }
  
  if (searchParams.get('endDate')) {
    filterState.endDate = new Date(searchParams.get('endDate')!)
  }
  
  return filterState
} 