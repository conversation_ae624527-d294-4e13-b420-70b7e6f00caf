import mysql from 'mysql2/promise';

// MySQL数据库连接配置
const DB_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'root',
  password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
  database: 'walrus',
  timezone: '+08:00',
  charset: 'utf8mb4',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  multipleStatements: true
};

// 创建数据库连接
export async function createMySQLConnection() {
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ MySQL数据库连接成功');
    return connection;
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error);
    throw error;
  }
}

// 获取所有表名
export async function getAllTables(connection: mysql.Connection) {
  try {
    const [rows] = await connection.execute('SHOW TABLES');
    return rows as Array<{[key: string]: string}>;
  } catch (error) {
    console.error('获取表列表失败:', error);
    throw error;
  }
}

// 获取表结构
export async function getTableStructure(connection: mysql.Connection, tableName: string) {
  try {
    const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
    const [indexes] = await connection.execute(`SHOW INDEX FROM ${tableName}`);
    const [createTable] = await connection.execute(`SHOW CREATE TABLE ${tableName}`);
    
    return {
      tableName,
      columns,
      indexes,
      createTable
    };
  } catch (error) {
    console.error(`获取表 ${tableName} 结构失败:`, error);
    throw error;
  }
}

// 获取表的样本数据
export async function getTableSample(connection: mysql.Connection, tableName: string, limit = 5) {
  try {
    const [rows] = await connection.execute(`SELECT * FROM ${tableName} LIMIT ${limit}`);
    return rows;
  } catch (error) {
    console.error(`获取表 ${tableName} 样本数据失败:`, error);
    throw error;
  }
}

// 获取表的记录数
export async function getTableCount(connection: mysql.Connection, tableName: string) {
  try {
    const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
    return (rows as Array<{count: number}>)[0].count;
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error);
    return 0;
  }
}

// 数据库分析工具
export async function analyzeMySQLDatabase() {
  let connection: mysql.Connection | null = null;
  
  try {
    connection = await createMySQLConnection();
    
    // 获取所有表
    const tables = await getAllTables(connection);
    console.log(`\n📊 数据库 'walrus' 包含 ${tables.length} 个表:`);
    
    const analysisResult = {
      database: 'walrus',
      tableCount: tables.length,
      tables: [] as Array<any>
    };
    
    // 分析每个表
    for (const tableRow of tables) {
      const tableName = Object.values(tableRow)[0];
      console.log(`\n🔍 分析表: ${tableName}`);
      
      try {
        const structure = await getTableStructure(connection, tableName);
        const sampleData = await getTableSample(connection, tableName, 3);
        const recordCount = await getTableCount(connection, tableName);
        
        const tableInfo = {
          name: tableName,
          recordCount,
          columnCount: (structure.columns as Array<any>).length,
          columns: structure.columns,
          indexes: structure.indexes,
          sampleData,
          createTableSQL: structure.createTable
        };
        
        analysisResult.tables.push(tableInfo);
        
        console.log(`  📝 字段数: ${tableInfo.columnCount}`);
        console.log(`  📊 记录数: ${recordCount}`);
        
        // 显示主要字段
        const columns = structure.columns as Array<any>;
        console.log(`  🗂️ 主要字段:`, columns.slice(0, 5).map((col: any) => `${col.Field}(${col.Type})`).join(', '));
        
      } catch (error) {
        console.error(`  ❌ 分析表 ${tableName} 失败:`, error);
      }
    }
    
    return analysisResult;
    
  } catch (error) {
    console.error('❌ 数据库分析失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
} 