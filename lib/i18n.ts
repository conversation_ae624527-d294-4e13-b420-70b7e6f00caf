// 简单的国际化工具
export type Locale = 'zh' | 'en'

export const defaultLocale: Locale = 'zh'

export const messages = {
  zh: {
    nav: {
      title: 'Chat Analytics',
      login: '登录系统',
      dashboard: '进入仪表盘',
      waitlist: '加入等待名单',
    },
    hero: {
      title: 'AI 对话分析系统',
      subtitle: '全方位解析客户对话，驱动业务增长',
      description:
        '我们的 AI 对话分析平台可以深入挖掘客户对话数据，提供可行的洞察，帮助企业更好地理解客户需求，提升客户满意度和业务表现。',
      cta: {
        login: '立即登录',
        dashboard: '进入仪表盘',
        apply: '申请使用',
      },
      features: {
        realtime: {
          title: '实时分析',
          desc: '对对话内容进行实时分析，获取即时洞察',
        },
        reports: {
          title: '全面报告',
          desc: '生成详细的分析报告，支持多维度筛选',
        },
        prediction: {
          title: '智能预测',
          desc: '预测客户需求和行为，提前做好准备',
        },
        ai: {
          title: 'AI 驱动',
          desc: '采用先进的 AI 技术，不断优化分析模型',
        },
      },
    },
    features: {
      title: '强大的 AI 驱动分析能力',
      subtitle: '我们的平台提供全方位的对话分析功能，帮助您深入理解客户互动',
      semantic: {
        title: '语义理解分析',
        description: '利用先进的自然语言处理技术，深入理解对话内容，捕捉客户真实意图和情感倾向。',
      },
      visualization: {
        title: '多维度数据可视化',
        description:
          '通过直观的图表和仪表盘，展示对话数据的多个维度，包括情绪分布、主题聚类和问题分类。',
      },
      insights: {
        title: '客户画像洞察',
        description:
          '自动生成详细的客户画像，包括兴趣偏好、行为模式和需求痛点，帮助您更好地理解目标客户。',
      },
      prediction: {
        title: '趋势预测和警报',
        description: '识别对话中的趋势变化，预测可能的问题和机会，并在关键指标异常时发出实时警报。',
      },
    },
  },
  en: {
    nav: {
      title: 'Chat Analytics',
      login: 'Sign In',
      dashboard: 'Dashboard',
      waitlist: 'Join Waitlist',
    },
    hero: {
      title: 'AI Chat Analysis System',
      subtitle: 'Comprehensive conversation analysis to drive business growth',
      description:
        'Our AI conversation analysis platform deeply analyzes customer conversation data, providing actionable insights to help businesses better understand customer needs and improve satisfaction and performance.',
      cta: {
        login: 'Sign In Now',
        dashboard: 'Go to Dashboard',
        apply: 'Apply for Access',
      },
      features: {
        realtime: {
          title: 'Real-time Analysis',
          desc: 'Analyze conversation content in real-time for instant insights',
        },
        reports: {
          title: 'Comprehensive Reports',
          desc: 'Generate detailed analysis reports with multi-dimensional filtering',
        },
        prediction: {
          title: 'Smart Prediction',
          desc: 'Predict customer needs and behaviors to prepare ahead',
        },
        ai: {
          title: 'AI-Powered',
          desc: 'Leveraging advanced AI technology with continuously optimized models',
        },
      },
    },
    features: {
      title: 'Powerful AI-Driven Analysis Capabilities',
      subtitle:
        'Our platform provides comprehensive conversation analysis features to help you deeply understand customer interactions',
      semantic: {
        title: 'Semantic Understanding Analysis',
        description:
          "Utilizing advanced natural language processing technology to deeply understand conversation content and capture customers' true intentions and emotional tendencies.",
      },
      visualization: {
        title: 'Multi-dimensional Data Visualization',
        description:
          'Display multiple dimensions of conversation data through intuitive charts and dashboards, including emotion distribution, topic clustering, and issue classification.',
      },
      insights: {
        title: 'Customer Profile Insights',
        description:
          'Automatically generate detailed customer profiles, including interests, behavioral patterns, and pain points to help you better understand target customers.',
      },
      prediction: {
        title: 'Trend Prediction and Alerts',
        description:
          'Identify trend changes in conversations, predict potential issues and opportunities, and send real-time alerts when key metrics are abnormal.',
      },
    },
  },
} as const

export function useTranslations(locale: Locale = defaultLocale) {
  return {
    t: (key: string) => {
      const keys = key.split('.')
      let value: any = messages[locale]

      for (const k of keys) {
        value = value?.[k]
      }

      return value || key
    },
    locale,
  }
}
