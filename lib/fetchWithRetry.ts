// Utility function to handle fetch requests with retry logic
export const fetchWithRetry = async (url: string, retries = 3, delay = 2000) => {
  let lastError: any
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url)

      // 检查HTTP状态码
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText || `HTTP error ${response.status}`)
      }

      return response
    } catch (err) {
      lastError = err
      console.warn(`Fetch attempt ${i + 1} failed, retrying in ${delay}ms...`, err)
      // 超时错误多给一些时间重试
      if (err instanceof Error && err.message.includes('timeout')) {
        delay = delay * 2
      }
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  // 所有重试都失败，抛出最后一个错误
  throw lastError
}
