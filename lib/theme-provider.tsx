'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { zhCN } from '@mui/material/locale'

// 定义明亮主题的调色板
const brightPalette = {
  primary: {
    main: '#1976d2',
    light: '#42a5f5',
    dark: '#1565c0',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#9c27b0',
    light: '#ba68c8',
    dark: '#7b1fa2',
    contrastText: '#ffffff',
  },
  success: {
    main: '#00A389',
    light: '#4cc9b0',
    dark: '#007F60',
  },
  error: {
    main: '#E55246',
    light: '#ff6f65',
    dark: '#c41c10',
  },
  warning: {
    main: '#ED954B',
    light: '#ffb66e',
    dark: '#c27400',
  },
  info: {
    main: '#6B5AED',
    light: '#9c8cff',
    dark: '#4936c1',
  },
  background: {
    default: '#f8f9fa',
    paper: '#ffffff',
  },
  text: {
    primary: 'rgba(0, 0, 0, 0.87)',
    secondary: 'rgba(0, 0, 0, 0.6)',
    disabled: 'rgba(0, 0, 0, 0.38)',
  },
  // 添加自定义卡片背景色
  customCard: {
    green: '#e6f7eb',
    purple: '#f5ebfa',
    yellow: '#fdf6e3',
    red: '#fbece9',
    blue: '#e3f2fd',
  },
  // 添加自定义图表颜色
  chart: {
    green: '#00A389',
    purple: '#6B5AED',
    orange: '#ED954B',
    red: '#E55246',
    blue: '#3498db',
    yellow: '#f1c40f',
  },
}

// Create a context for theme mode (though we'll only use light mode)
interface ThemeContextType {
  setMode: (mode: 'light') => void
}

const ThemeContext = createContext<ThemeContextType>({
  setMode: () => {},
})

export function useThemeContext() {
  return useContext(ThemeContext)
}

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  // Always use light mode
  const [mode] = useState<'light'>('light')

  // Create theme based on light mode
  const theme = React.useMemo(
    () =>
      createTheme(
        {
          palette: {
            mode: 'light',
            ...brightPalette,
          },
          typography: {
            fontFamily: [
              'var(--font-geist-sans)',
              '-apple-system',
              'BlinkMacSystemFont',
              '"Segoe UI"',
              'Roboto',
              '"Helvetica Neue"',
              'Arial',
              'sans-serif',
              '"Apple Color Emoji"',
              '"Segoe UI Emoji"',
              '"Segoe UI Symbol"',
            ].join(','),
          },
          components: {
            MuiPaper: {
              styleOverrides: {
                root: {
                  borderRadius: '12px',
                  boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
                },
              },
              defaultProps: {
                elevation: 0,
              },
            },
            MuiButton: {
              styleOverrides: {
                root: {
                  textTransform: 'none',
                  borderRadius: '8px',
                },
              },
              defaultProps: {
                disableElevation: true,
              },
            },
            MuiTextField: {
              defaultProps: {
                variant: 'outlined',
                size: 'small',
              },
            },
            MuiSelect: {
              defaultProps: {
                variant: 'outlined',
                size: 'small',
              },
            },
            MuiCard: {
              styleOverrides: {
                root: {
                  borderRadius: '16px',
                },
              },
            },
          },
          shape: {
            borderRadius: 8,
          },
        },
        zhCN
      ),
    []
  )

  const setMode = () => {} // 空函数，因为我们只使用明亮模式

  // Provide the theme context and MUI theme
  return (
    <ThemeContext.Provider value={{ setMode }}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  )
}
