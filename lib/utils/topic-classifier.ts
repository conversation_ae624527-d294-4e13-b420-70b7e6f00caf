/**
 * 主题分类工具
 * 统一的主题分类逻辑，避免重复代码
 */

export interface TopicClassificationRule {
  keywords: string[]
  category: string
  priority?: number // 优先级，数字越小优先级越高
}

// 主题分类规则配置
export const TOPIC_CLASSIFICATION_RULES: TopicClassificationRule[] = [
  // 基础设施类问题 (优先级 1)
  { keywords: ['停水', '来水', '水压', '出水少'], category: '供水问题', priority: 1 },
  { keywords: ['水费', '水卡充值'], category: '水费缴纳', priority: 1 },
  { keywords: ['马桶', '漏水', '下水管', '管道堵'], category: '卫浴/漏水问题', priority: 1 },
  { keywords: ['送水'], category: '送水服务', priority: 1 },
  { keywords: ['停电'], category: '停电咨询', priority: 1 },
  { keywords: ['电费'], category: '电费缴纳', priority: 1 },
  { keywords: ['电器', '插座'], category: '电器维修', priority: 1 },
  { keywords: ['灯'], category: '照明问题', priority: 1 },
  { keywords: ['燃气', '煤气', '天然气'], category: '燃气问题', priority: 1 },
  
  // 物业服务类 (优先级 2)
  { keywords: ['物业费'], category: '物业费', priority: 2 },
  { keywords: ['噪音', '吵', '扰民'], category: '噪音投诉', priority: 2 },
  { keywords: ['电梯'], category: '电梯问题', priority: 2 },
  { keywords: ['卫生', '清洁', '垃圾'], category: '卫生清洁', priority: 2 },
  
  // 维修类服务 (优先级 3)
  { keywords: ['热水器'], category: '热水器问题', priority: 3 },
  { keywords: ['安装', '预约师傅', '预约电工'], category: '安装/预约服务', priority: 3 },
  { keywords: ['抽屉', '家具'], category: '家具维修', priority: 3 },
  
  // 车辆与出入类 (优先级 4)
  { keywords: ['录入车辆', '更换车牌', '绑定车牌'], category: '车辆登记/变更', priority: 4 },
  { keywords: ['放行', '货车', '搬', '临时停车'], category: '车辆通行/搬家', priority: 4 },
  { keywords: ['门禁', '密码', '出入'], category: '门禁/出入管理', priority: 4 },
  { keywords: ['钥匙'], category: '钥匙问题', priority: 4 },
  { keywords: ['车位被占', '占车位'], category: '车位被占', priority: 4 },
  { keywords: ['充电桩'], category: '充电桩', priority: 4 },
  
  // 社区服务类 (优先级 5)
  { keywords: ['快递', '包裹'], category: '快递服务', priority: 5 },
  { keywords: ['暖气', '采暖'], category: '暖气问题', priority: 5 },
  { keywords: ['家政', '保姆'], category: '家政服务', priority: 5 },
  { keywords: ['搬家'], category: '搬家服务', priority: 5 },
  { keywords: ['打印'], category: '打印服务', priority: 5 },
  { keywords: ['外借', '借用'], category: '物品借用', priority: 5 },
  
  // 一般交互类 (优先级 6)
  { keywords: ['转人工', '人工客服', '人工服务'], category: '转人工服务', priority: 6 },
  { keywords: ['满意', '感谢', '谢谢'], category: '满意/感谢反馈', priority: 6 },
  { keywords: ['你好'], category: '问候/简单交流', priority: 6 },
  { keywords: ['没有明确表达诉求', '没有具体诉求', '暂时没有问题', '测试', '试试'], category: '无明确诉求/测试', priority: 6 },
  
  // 异常情况 (优先级 0 - 最高优先级)
  { keywords: ['紧急', '火', '烧焦', '隐患'], category: '紧急情况/安全问题', priority: 0 },
]

// 复合条件规则 (需要特殊处理的规则)
export const COMPLEX_CLASSIFICATION_RULES = [
  {
    condition: (topic: string) => (topic.includes('电') && topic.includes('维修')),
    category: '电器维修'
  },
  {
    condition: (topic: string) => (topic.includes('声音') && topic.includes('大')),
    category: '噪音投诉'
  },
  {
    condition: (topic: string) => (topic.includes('投诉') && !(topic.includes('噪音') || topic.includes('吵'))),
    category: '一般投诉'
  },
  {
    condition: (topic: string) => ((topic.includes('门') || topic.includes('窗')) && (topic.includes('坏') || topic.includes('维修') || topic.includes('修理'))),
    category: '门窗维修'
  },
  {
    condition: (topic: string) => (topic.includes('坏') && !(topic.includes('门') || topic.includes('电') || topic.includes('水'))),
    category: '家具维修'
  },
  {
    condition: (topic: string) => ((topic.includes('车') && !(topic.includes('车位') || topic.includes('充电桩')))),
    category: '车辆登记/变更'
  },
  {
    condition: (topic: string) => (topic.includes('锁') && !topic.includes('门禁')),
    category: '锁具问题'
  },
  {
    condition: (topic: string) => ((topic.includes('车位') && (topic.includes('费') || topic.includes('缴') || topic.includes('租'))) || topic.includes('车位使用')),
    category: '车位费用/管理'
  },
  {
    condition: (topic: string) => ((topic.includes('装修') || topic.includes('施工')) && !(topic.includes('噪音') || topic.includes('吵'))),
    category: '装修/施工'
  },
  {
    condition: (topic: string) => (topic.includes('租房') || topic.includes('房源') || (topic.includes('租') && !topic.includes('车位'))),
    category: '租房咨询'
  },
  {
    condition: (topic: string) => ((topic.includes('咨询') || topic.includes('询问')) && !(
      topic.includes('水') || topic.includes('电') || topic.includes('费') ||
      topic.includes('车') || topic.includes('门禁') || topic.includes('燃气') ||
      topic.includes('天然气') || topic.includes('煤气') || topic.includes('维修')
    )),
    category: '一般咨询'
  },
  {
    condition: (topic: string) => (topic.includes('您好') && topic.length < 50),
    category: '问候/简单交流'
  }
]

/**
 * 分类单个主题
 */
export function classifyTopic(topic: string): string {
  if (!topic) return '其他问题'
  
  // 首先检查复合条件规则
  for (const rule of COMPLEX_CLASSIFICATION_RULES) {
    if (rule.condition(topic)) {
      return rule.category
    }
  }
  
  // 然后按优先级检查简单关键词规则
  const sortedRules = TOPIC_CLASSIFICATION_RULES.sort((a, b) => (a.priority || 999) - (b.priority || 999))
  
  for (const rule of sortedRules) {
    if (rule.keywords.some(keyword => topic.includes(keyword))) {
      return rule.category
    }
  }
  
  return '其他问题'
}

/**
 * 获取所有可能的主题类别
 */
export function getAllTopicCategories(): string[] {
  const categories = new Set<string>()
  
  TOPIC_CLASSIFICATION_RULES.forEach(rule => categories.add(rule.category))
  COMPLEX_CLASSIFICATION_RULES.forEach(rule => categories.add(rule.category))
  categories.add('其他问题')
  
  return Array.from(categories).sort()
}

/**
 * 获取详细主题分布的所有字段
 */
export function getDetailedTopicFields(): string[] {
  return [
    '总对话数',
    '供水问题', '水费缴纳', '卫浴/漏水问题', '送水服务',
    '停电咨询', '电费缴纳', '电器维修', '照明问题', '燃气问题',
    '物业费', '车位费用/管理', '噪音投诉', '一般投诉', '车位被占', '充电桩', '电梯问题', '卫生清洁',
    '门窗维修', '热水器问题', '安装/预约服务', '家具维修',
    '车辆登记/变更', '车辆通行/搬家', '门禁/出入管理', '锁具问题', '钥匙问题',
    '快递服务', '装修/施工', '暖气问题', '家政服务', '搬家服务',
    '打印服务', '物品借用', '租房咨询',
    '一般咨询', '转人工服务', '满意/感谢反馈', '问候/简单交流', '无明确诉求/测试',
    '紧急情况/安全问题'
  ]
} 