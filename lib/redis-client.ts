import { createClient, RedisClientType } from 'redis'

class RedisManager {
  private static instance: RedisManager
  private client: RedisClientType | null = null
  private isConnected: boolean = false

  private constructor() {}

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager()
    }
    return RedisManager.instance
  }

  async connect(): Promise<RedisClientType> {
    if (this.client && this.isConnected) {
      return this.client
    }

    try {
      // Redis连接配置 - 开发环境使用本地Redis
      this.client = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          connectTimeout: 5000,
          lazyConnect: true,
          reconnectStrategy: (retries) => {
            console.log(`🔄 Redis重连尝试: ${retries}`)
            if (retries > 3) {
              return new Error('Redis连接失败，超过最大重试次数')
            }
            return Math.min(retries * 50, 500)
          }
        }
      })

      // 错误处理
      this.client.on('error', (err) => {
        console.error('❌ Redis连接错误:', err)
        this.isConnected = false
      })

      this.client.on('connect', () => {
        console.log('✅ Redis连接成功')
        this.isConnected = true
      })

      this.client.on('disconnect', () => {
        console.log('⚠️ Redis连接断开')
        this.isConnected = false
      })

      await this.client.connect()
      return this.client
    } catch (error) {
      console.error('❌ Redis连接初始化失败:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.quit()
      this.isConnected = false
      console.log('✅ Redis连接已关闭')
    }
  }

  getClient(): RedisClientType | null {
    return this.client
  }

  isRedisConnected(): boolean {
    return this.isConnected
  }
}

// 导出单例实例
const redisManager = RedisManager.getInstance()

export default redisManager

// 辅助函数：获取Redis客户端
export async function getRedisClient(): Promise<RedisClientType> {
  return await redisManager.connect()
}

// 辅助函数：检查Redis连接状态
export function isRedisAvailable(): boolean {
  return redisManager.isRedisConnected()
} 