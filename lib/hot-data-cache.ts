import { getRedisClient } from './redis-client'
import { precomputedCache } from './precomputed-cache'

/**
 * 智能热数据缓存管理器
 * 第三阶段：实现动态热数据识别和智能缓存策略
 */
export class HotDataCacheManager {
  private static instance: HotDataCacheManager
  
  // 热数据配置
  private readonly HOT_DATA_CONFIGS = {
    // 热数据访问阈值
    access_threshold: 3,   // 降低到3次访问即认为是热数据
    // 热数据缓存时间
    hot_data_ttl: 300,     // 5分钟
    // 监控窗口期（小时）
    monitor_window: 24,
    // 热数据预加载阈值
    preload_threshold: 3   // 降低预加载阈值
  }

  private constructor() {}

  public static getInstance(): HotDataCacheManager {
    if (!HotDataCacheManager.instance) {
      HotDataCacheManager.instance = new HotDataCacheManager()
    }
    return HotDataCacheManager.instance
  }

  /**
   * 记录查询访问日志
   */
  async recordQueryAccess(queryType: string, filters: any): Promise<void> {
    try {
      const redis = await getRedisClient()
      const accessKey = this.generateAccessKey(queryType, filters)
      const hourKey = `hot_access:${Math.floor(Date.now() / (1000 * 60 * 60))}`
      
      // 记录访问计数
      await redis.hIncrBy(hourKey, accessKey, 1)
      await redis.expire(hourKey, this.HOT_DATA_CONFIGS.monitor_window * 3600)
      
      // 记录最后访问时间
      await redis.set(`last_access:${accessKey}`, Date.now(), {
        EX: this.HOT_DATA_CONFIGS.monitor_window * 3600
      })
      
    } catch (error) {
      console.error('❌ 记录查询访问失败:', error)
    }
  }

  /**
   * 检查是否为热数据
   */
  async isHotData(queryType: string, filters: any): Promise<boolean> {
    try {
      const redis = await getRedisClient()
      const accessKey = this.generateAccessKey(queryType, filters)
      const currentHour = Math.floor(Date.now() / (1000 * 60 * 60))
      
      let totalAccesses = 0
      
      // 检查过去24小时的访问记录
      for (let i = 0; i < this.HOT_DATA_CONFIGS.monitor_window; i++) {
        const hourKey = `hot_access:${currentHour - i}`
        const accesses = await redis.hGet(hourKey, accessKey)
        totalAccesses += parseInt(accesses || '0')
      }
      
      return totalAccesses >= this.HOT_DATA_CONFIGS.access_threshold
    } catch (error) {
      console.error('❌ 检查热数据状态失败:', error)
      return false
    }
  }

  /**
   * 获取热数据查询列表
   */
  async getHotQueries(): Promise<Array<{ query: string, accesses: number }>> {
    try {
      const redis = await getRedisClient()
      const currentHour = Math.floor(Date.now() / (1000 * 60 * 60))
      const hotQueries = new Map<string, number>()
      
      // 汇总过去24小时的所有访问记录
      for (let i = 0; i < this.HOT_DATA_CONFIGS.monitor_window; i++) {
        const hourKey = `hot_access:${currentHour - i}`
        const hourData = await redis.hGetAll(hourKey)
        
        for (const [query, accesses] of Object.entries(hourData)) {
          const currentCount = hotQueries.get(query) || 0
          hotQueries.set(query, currentCount + parseInt(accesses))
        }
      }
      
      // 过滤并排序热数据
      return Array.from(hotQueries.entries())
        .filter(([_, accesses]) => accesses >= this.HOT_DATA_CONFIGS.access_threshold)
        .map(([query, accesses]) => ({ query, accesses }))
        .sort((a, b) => b.accesses - a.accesses)
        
    } catch (error) {
      console.error('❌ 获取热数据查询失败:', error)
      return []
    }
  }

  /**
   * 预加载热数据
   */
  async preloadHotData(): Promise<void> {
    console.log('🔥 开始预加载热数据...')
    
    try {
      const hotQueries = await this.getHotQueries()
      const preloadTasks = []
      
      for (const { query, accesses } of hotQueries) {
        if (accesses >= this.HOT_DATA_CONFIGS.preload_threshold) {
          const [queryType, filtersStr] = query.split(':')
          const filters = JSON.parse(filtersStr || '{}')
          
          // 为高频查询预加载数据
          preloadTasks.push(this.preloadQueryData(queryType, filters))
        }
      }
      
      await Promise.all(preloadTasks)
      console.log(`✅ 热数据预加载完成，处理了 ${preloadTasks.length} 个查询`)
      
    } catch (error) {
      console.error('❌ 预加载热数据失败:', error)
    }
  }

  /**
   * 预加载单个查询数据
   */
  private async preloadQueryData(queryType: string, filters: any): Promise<void> {
    try {
      const redis = await getRedisClient()
      const cacheKey = `hot_cache:${this.generateAccessKey(queryType, filters)}`
      
      // 检查是否已有热缓存
      const existing = await redis.get(cacheKey)
      if (existing) {
        return
      }
      
      // 尝试从预计算缓存获取
      let data = null
      if (queryType.includes('conversion') || queryType.includes('project_ranking') || queryType.includes('hourly')) {
        const endDate = new Date()
        const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000)
        data = await precomputedCache.getPrecomputedData(queryType, startDate, endDate)
      }
      
      if (data) {
        // 缓存热数据
        await redis.setEx(cacheKey, this.HOT_DATA_CONFIGS.hot_data_ttl, JSON.stringify(data))
        console.log(`🔥 热数据已缓存: ${queryType}`)
      }
      
    } catch (error) {
      console.error(`❌ 预加载查询数据失败 [${queryType}]:`, error)
    }
  }

  /**
   * 获取热缓存数据
   */
  async getHotCachedData(queryType: string, filters: any): Promise<any[] | null> {
    try {
      const redis = await getRedisClient()
      const cacheKey = `hot_cache:${this.generateAccessKey(queryType, filters)}`
      
      const cached = await redis.get(cacheKey)
      if (cached) {
        console.log(`🔥 热缓存命中: ${queryType}`)
        return JSON.parse(cached)
      }
      
      return null
    } catch (error) {
      console.error('❌ 获取热缓存数据失败:', error)
      return null
    }
  }

  /**
   * 智能缓存数据
   */
  async smartCacheData(queryType: string, filters: any, data: any[]): Promise<void> {
    const isHot = await this.isHotData(queryType, filters)
    
    if (isHot) {
      try {
        const redis = await getRedisClient()
        const cacheKey = `hot_cache:${this.generateAccessKey(queryType, filters)}`
        
        await redis.setEx(cacheKey, this.HOT_DATA_CONFIGS.hot_data_ttl, JSON.stringify(data))
        console.log(`🔥 智能热缓存设置: ${queryType}`)
      } catch (error) {
        console.error('❌ 智能缓存设置失败:', error)
      }
    }
  }

  /**
   * 生成访问键
   */
  private generateAccessKey(queryType: string, filters: any): string {
    const normalizedFilters = this.normalizeFilters(filters)
    return `${queryType}:${JSON.stringify(normalizedFilters)}`
  }

  /**
   * 标准化过滤器
   */
  private normalizeFilters(filters: any): any {
    // 移除不影响查询结果的参数
    const { timestamp, cached, source, ...normalizedFilters } = filters
    
    // 对键进行排序以确保一致性
    const sortedKeys = Object.keys(normalizedFilters).sort()
    const sorted: any = {}
    sortedKeys.forEach(key => {
      sorted[key] = normalizedFilters[key]
    })
    
    return sorted
  }

  /**
   * 获取热数据统计
   */
  async getHotDataStats(): Promise<any> {
    try {
      const hotQueries = await this.getHotQueries()
      const redis = await getRedisClient()
      
      // 统计热缓存数量
      const hotCacheKeys = await redis.keys('hot_cache:*')
      
      return {
        total_hot_queries: hotQueries.length,
        total_hot_cache_entries: hotCacheKeys.length,
        top_queries: hotQueries.slice(0, 10),
        cache_hit_potential: hotQueries.reduce((sum, q) => sum + q.accesses, 0)
      }
    } catch (error) {
      console.error('❌ 获取热数据统计失败:', error)
      return {}
    }
  }

  /**
   * 清理过期的热数据缓存
   */
  async cleanupExpiredHotCache(): Promise<void> {
    try {
      const redis = await getRedisClient()
      const hotCacheKeys = await redis.keys('hot_cache:*')
      
      let cleanedCount = 0
      for (const key of hotCacheKeys) {
        const ttl = await redis.ttl(key)
        if (ttl === -1 || ttl === -2) {
          await redis.del(key)
          cleanedCount++
        }
      }
      
      console.log(`🧹 清理了 ${cleanedCount} 个过期热缓存项`)
    } catch (error) {
      console.error('❌ 清理过期热缓存失败:', error)
    }
  }
}

// 导出单例实例
export const hotDataCache = HotDataCacheManager.getInstance() 