import mysql from 'mysql2/promise';

// MySQL Walrus数据库连接配置
const MYSQL_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'root',
  password: '372b2974-a009-4784-8cfb-c8627f9c48ac',
  database: 'walrus',
  timezone: '+08:00',
  charset: 'utf8mb4',
  multipleStatements: true,
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 60000,
    timeout: 60000,
    idleTimeoutMillis: 30000,
  }
};

// 创建连接池
const pool = mysql.createPool(MYSQL_CONFIG);

// 数据库查询接口
export interface ConversationData {
  id: number;
  chat_id: string;
  created_at: Date;
  user_id: string;
  user_name: string;
  content: string;
  project_id?: string;
  project_name?: string;
  grid_name?: string;
  emotion_type?: string;
  survey?: string;
  work_order: number;
  company_name?: string;
  steward_name?: string;
}

// 项目信息接口
export interface ProjectInfo {
  project_code: string;
  project_name: string;
  grid_names?: string[];
}

// 获取数据库连接
export async function getConnection() {
  try {
    const connection = await pool.getConnection();
    return connection;
  } catch (error) {
    console.error('MySQL连接失败:', error);
    throw error;
  }
}

// 获取对话记录数据 (替代原有的conversations查询)
export async function getConversations(params: {
  limit?: number;
  offset?: number;
  project_id?: string;
  start_date?: string;
  end_date?: string;
  grid_name?: string;
  survey?: string;
}): Promise<ConversationData[]> {
  const connection = await getConnection();
  
  try {
    const { limit = 1000, offset = 0, project_id, start_date, end_date, grid_name, survey } = params;
    
    let whereClause = 'WHERE cm.deleted = 0';
    const queryParams: any[] = [];
    
    if (project_id) {
      whereClause += ' AND cl.project_code = ?';
      queryParams.push(project_id);
    }
    
    if (start_date) {
      whereClause += ' AND FROM_UNIXTIME(cm.timestamp/1000) >= ?';
      queryParams.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND FROM_UNIXTIME(cm.timestamp/1000) <= ?';
      queryParams.push(end_date);
    }
    
    if (grid_name) {
      whereClause += ' AND sb.grid_name = ?';
      queryParams.push(grid_name);
    }
    
    if (survey) {
      const surveyCondition = survey === '满意' 
        ? "cl.satisfaction IN ('很满意', '满意')"
        : survey === '一般' 
        ? "cl.satisfaction = '一般'"
        : survey === '不满意'
        ? "cl.satisfaction IN ('不满意', '很不满意')"
        : "cl.satisfaction IS NOT NULL";
      whereClause += ` AND ${surveyCondition}`;
    }
    
    const query = `
      SELECT 
        cm.id,
        cm.chat_id,
        FROM_UNIXTIME(cm.timestamp/1000) AS created_at,
        cm.external_user_id AS user_id,
        cm.contact_name AS user_name,
        JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.text')) AS content,
        cl.project_code AS project_id,
        cl.project_name AS project_name,
        sb.grid_name AS grid_name,
        COALESCE(cm.emotion_type, cl.emotion_type) AS emotion_type,
        CASE 
          WHEN cl.satisfaction IN ('很满意', '满意') THEN '满意'
          WHEN cl.satisfaction = '一般' THEN '一般'
          WHEN cl.satisfaction IN ('不满意', '很不满意') THEN '不满意'
          ELSE NULL
        END AS survey,
        CASE 
          WHEN EXISTS(
            SELECT 1 FROM chat_msg_event cme 
            WHERE cme.message_id = cm.message_id 
            AND cme.biz_type LIKE '%WORK%'
          ) THEN 1 
          ELSE 0 
        END AS work_order,
        '' AS company_name,
        sb.grid_name AS steward_name
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      AND cm.timestamp IS NOT NULL
      AND cm.payload IS NOT NULL
      ORDER BY cm.timestamp DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(limit, offset);
    
    const [rows] = await connection.execute(query, queryParams);
    return rows as ConversationData[];
    
  } finally {
    connection.release();
  }
}

// 获取项目列表
export async function getProjects(): Promise<ProjectInfo[]> {
  const connection = await getConnection();
  
  try {
    const query = `
      SELECT DISTINCT
        spc.project_code,
        spc.project_name,
        GROUP_CONCAT(DISTINCT sb.grid_name) AS grid_names
      FROM sys_project_config spc
      LEFT JOIN sys_bot sb ON spc.project_code = sb.project_code
      WHERE spc.project_name IS NOT NULL
      GROUP BY spc.project_code, spc.project_name
      ORDER BY spc.project_name
    `;
    
    const [rows] = await connection.execute(query);
    return (rows as any[]).map(row => ({
      project_code: row.project_code,
      project_name: row.project_name,
      grid_names: row.grid_names ? row.grid_names.split(',') : []
    }));
    
  } finally {
    connection.release();
  }
}

// 获取网格列表
export async function getGrids(project_id?: string): Promise<string[]> {
  const connection = await getConnection();
  
  try {
    let query = `
      SELECT DISTINCT grid_name 
      FROM sys_bot 
      WHERE grid_name IS NOT NULL AND grid_name != ''
    `;
    const queryParams: any[] = [];
    
    if (project_id) {
      query += ' AND project_code = ?';
      queryParams.push(project_id);
    }
    
    query += ' ORDER BY grid_name';
    
    const [rows] = await connection.execute(query, queryParams);
    return (rows as any[]).map(row => row.grid_name);
    
  } finally {
    connection.release();
  }
}

// 获取统计数据
export async function getStats(params: {
  project_id?: string;
  start_date?: string;
  end_date?: string;
}) {
  const connection = await getConnection();
  
  try {
    const { project_id, start_date, end_date } = params;
    
    let whereClause = 'WHERE cm.deleted = 0';
    const queryParams: any[] = [];
    
    if (project_id) {
      whereClause += ' AND cl.project_code = ?';
      queryParams.push(project_id);
    }
    
    if (start_date) {
      whereClause += ' AND FROM_UNIXTIME(cm.timestamp/1000) >= ?';
      queryParams.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND FROM_UNIXTIME(cm.timestamp/1000) <= ?';
      queryParams.push(end_date);
    }
    
    const query = `
      SELECT 
        COUNT(*) AS total_conversations,
        COUNT(DISTINCT cm.chat_id) AS unique_chats,
        COUNT(DISTINCT cm.external_user_id) AS unique_users,
        COUNT(DISTINCT cl.project_code) AS unique_projects,
        COUNT(DISTINCT sb.grid_name) AS unique_grids,
        SUM(CASE WHEN cl.satisfaction IN ('很满意', '满意') THEN 1 ELSE 0 END) AS satisfied_count,
        SUM(CASE WHEN cl.satisfaction = '一般' THEN 1 ELSE 0 END) AS neutral_count,
        SUM(CASE WHEN cl.satisfaction IN ('不满意', '很不满意') THEN 1 ELSE 0 END) AS unsatisfied_count,
        COUNT(CASE WHEN EXISTS(
          SELECT 1 FROM chat_msg_event cme 
          WHERE cme.message_id = cm.message_id 
          AND cme.biz_type LIKE '%WORK%'
        ) THEN 1 END) AS work_orders
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      AND cm.timestamp IS NOT NULL
    `;
    
    const [rows] = await connection.execute(query, queryParams);
    return (rows as any[])[0];
    
  } finally {
    connection.release();
  }
}

// 关闭连接池
export async function closePool() {
  await pool.end();
}

// 测试连接
export async function testConnection() {
  try {
    const connection = await getConnection();
    await connection.execute('SELECT 1');
    connection.release();
    console.log('✅ MySQL数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ MySQL数据库连接测试失败:', error);
    return false;
  }
} 