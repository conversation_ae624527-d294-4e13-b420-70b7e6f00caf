/**
 * 数据完整性验证系统
 * 检查各表之间的关联质量和数据一致性
 */

import { getConnection } from './mysql-db';
import { validateSatisfactionData } from './satisfaction-mapper';

// 执行查询的辅助函数
async function executeQuery(query: string, params: any[] = []): Promise<any[]> {
  const connection = await getConnection()
  try {
    const [rows] = await connection.execute(query, params)
    return rows as any[]
  } finally {
    connection.release()
  }
}

export interface DataIntegrityReport {
  timestamp: string;
  overall_score: number;
  checks: IntegrityCheck[];
  recommendations: string[];
}

export interface IntegrityCheck {
  name: string;
  status: 'pass' | 'warning' | 'fail';
  score: number;
  details: {
    expected?: number;
    actual?: number;
    percentage?: number;
    issues?: string[];
  };
  description: string;
}

/**
 * 执行完整的数据完整性检查
 */
export async function validateDataIntegrity(): Promise<DataIntegrityReport> {
  const checks: IntegrityCheck[] = [];
  const recommendations: string[] = [];

  try {
    // 1. 表关联质量检查
    const relationshipChecks = await validateTableRelationships();
    checks.push(...relationshipChecks);

    // 2. 满意度数据质量检查
    const satisfactionCheck = await validateSatisfactionQuality();
    checks.push(satisfactionCheck);

    // 3. 时间戳一致性检查
    const timestampCheck = await validateTimestampConsistency();
    checks.push(timestampCheck);

    // 4. 业务事件数据检查
    const eventCheck = await validateBusinessEvents();
    checks.push(eventCheck);

    // 5. 数据覆盖范围检查
    const coverageCheck = await validateDataCoverage();
    checks.push(coverageCheck);

    // 计算总体评分
    const overall_score = calculateOverallScore(checks);

    // 生成建议
    recommendations.push(...generateRecommendations(checks));

    return {
      timestamp: new Date().toISOString(),
      overall_score,
      checks,
      recommendations
    };
  } catch (error) {
    console.error('数据完整性验证失败:', error);
    throw error;
  }
}

/**
 * 验证表关联质量
 */
async function validateTableRelationships(): Promise<IntegrityCheck[]> {
  const checks: IntegrityCheck[] = [];

  // 检查 chat_msg -> chat_list 关联质量
  const chatMsgToListQuery = `
    SELECT 
      COUNT(DISTINCT cm.chat_id) as chat_msg_chats,
      COUNT(DISTINCT cl.chat_id) as chat_list_chats,
      COUNT(DISTINCT CASE WHEN cl.chat_id IS NOT NULL THEN cm.chat_id END) as matched_chats
    FROM chat_msg cm
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
  `;
  
  const [chatRelation] = await executeQuery(chatMsgToListQuery);
  const chatMatchRate = (chatRelation.matched_chats / chatRelation.chat_msg_chats) * 100;
  
  checks.push({
    name: 'chat_msg_to_chat_list_relation',
    status: chatMatchRate >= 90 ? 'pass' : chatMatchRate >= 70 ? 'warning' : 'fail',
    score: Math.min(100, chatMatchRate),
    details: {
      expected: chatRelation.chat_msg_chats,
      actual: chatRelation.matched_chats,
      percentage: Number(chatMatchRate.toFixed(2))
    },
    description: `chat_msg表与chat_list表的关联匹配率`
  });

  // 检查 chat_msg -> chat_msg_event 关联质量
  const eventRelationQuery = `
    SELECT 
      COUNT(DISTINCT cm.message_id) as chat_msg_messages,
      COUNT(DISTINCT cme.message_id) as event_messages,
      COUNT(DISTINCT CASE WHEN cme.message_id IS NOT NULL THEN cm.message_id END) as matched_messages
    FROM chat_msg cm
    LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
  `;
  
  const [eventRelation] = await executeQuery(eventRelationQuery);
  const eventMatchRate = (eventRelation.matched_messages / eventRelation.chat_msg_messages) * 100;
  
  checks.push({
    name: 'chat_msg_to_event_relation',
    status: eventMatchRate >= 50 ? 'pass' : eventMatchRate >= 20 ? 'warning' : 'fail',
    score: Math.min(100, eventMatchRate * 2), // 放宽评分标准
    details: {
      expected: eventRelation.chat_msg_messages,
      actual: eventRelation.matched_messages,
      percentage: Number(eventMatchRate.toFixed(2))
    },
    description: `chat_msg表与chat_msg_event表的关联匹配率`
  });

  return checks;
}

/**
 * 验证满意度数据质量
 */
async function validateSatisfactionQuality(): Promise<IntegrityCheck> {
  const satisfactionQuery = `
    SELECT 
      COUNT(*) as total_chats,
      COUNT(CASE WHEN satisfaction IS NOT NULL AND satisfaction != '' THEN 1 END) as satisfaction_count,
      satisfaction
    FROM chat_list
    GROUP BY satisfaction
  `;
  
  const satisfactionData = await executeQuery(satisfactionQuery);
  const totalChats = satisfactionData.reduce((sum: number, row: any) => sum + row.total_chats, 0);
  const satisfactionChats = satisfactionData.reduce((sum: number, row: any) => 
    row.satisfaction ? sum + row.total_chats : sum, 0
  );
  
  const coverageRate = totalChats > 0 ? (satisfactionChats / totalChats) * 100 : 0;
  
  // 验证满意度代码
  const codes = satisfactionData
    .filter((row: any) => row.satisfaction)
    .map((row: any) => row.satisfaction);
  
  const validation = validateSatisfactionData(codes);
  
  return {
    name: 'satisfaction_data_quality',
    status: coverageRate >= 30 ? 'pass' : coverageRate >= 20 ? 'warning' : 'fail',
    score: Math.min(100, coverageRate * 3), // 调整评分标准
    details: {
      expected: totalChats,
      actual: satisfactionChats,
      percentage: Number(coverageRate.toFixed(2)),
      issues: validation.issues
    },
    description: `满意度数据覆盖率和代码有效性`
  };
}

/**
 * 验证时间戳一致性
 */
async function validateTimestampConsistency(): Promise<IntegrityCheck> {
  const timestampQuery = `
    SELECT 
      'chat_msg' as table_name,
      MIN(FROM_UNIXTIME(timestamp/1000)) as min_date,
      MAX(FROM_UNIXTIME(timestamp/1000)) as max_date,
      COUNT(*) as record_count
    FROM chat_msg
    WHERE timestamp IS NOT NULL
    
    UNION ALL
    
    SELECT 
      'chat_list' as table_name,
      MIN(FROM_UNIXTIME(created_time/1000)) as min_date,
      MAX(FROM_UNIXTIME(created_time/1000)) as max_date,
      COUNT(*) as record_count
    FROM chat_list
    WHERE created_time IS NOT NULL
    
    UNION ALL
    
    SELECT 
      'chat_msg_event' as table_name,
      MIN(FROM_UNIXTIME(timestamp/1000)) as min_date,
      MAX(FROM_UNIXTIME(timestamp/1000)) as max_date,
      COUNT(*) as record_count
    FROM chat_msg_event
    WHERE timestamp IS NOT NULL
  `;
  
  const timestampData = await executeQuery(timestampQuery);
  
  // 检查时间范围重叠度
  const dateRanges = timestampData.map((row: any) => ({
    table: row.table_name,
    start: new Date(row.min_date).getTime(),
    end: new Date(row.max_date).getTime(),
    count: row.record_count
  }));
  
  // 计算时间范围一致性评分
  const score = calculateTimestampConsistencyScore(dateRanges);
  
  return {
    name: 'timestamp_consistency',
    status: score >= 80 ? 'pass' : score >= 60 ? 'warning' : 'fail',
    score,
    details: {
      actual: dateRanges.length,
      percentage: score
    },
    description: `各表时间戳一致性和数据时间范围重叠度`
  };
}

/**
 * 验证业务事件数据
 */
async function validateBusinessEvents(): Promise<IntegrityCheck> {
  const eventQuery = `
    SELECT 
      biz_type,
      COUNT(*) as event_count,
      COUNT(DISTINCT message_id) as unique_messages,
      MIN(FROM_UNIXTIME(timestamp/1000)) as earliest_event,
      MAX(FROM_UNIXTIME(timestamp/1000)) as latest_event
    FROM chat_msg_event
    GROUP BY biz_type
  `;
  
  const eventData = await executeQuery(eventQuery);
  const totalEvents = eventData.reduce((sum: number, row: any) => sum + row.event_count, 0);
  
  // 检查业务事件类型分布
  const iocTaskEvents = eventData.find(row => row.biz_type === 'IOC_TASK')?.event_count || 0;
  const chanceEvents = eventData.find(row => row.biz_type === 'CHANCE')?.event_count || 0;
  
  const businessEventRatio = totalEvents > 0 ? ((iocTaskEvents + chanceEvents) / totalEvents) * 100 : 0;
  
  return {
    name: 'business_events_quality',
    status: businessEventRatio >= 95 ? 'pass' : businessEventRatio >= 80 ? 'warning' : 'fail',
    score: Math.min(100, businessEventRatio),
    details: {
      expected: totalEvents,
      actual: iocTaskEvents + chanceEvents,
      percentage: Number(businessEventRatio.toFixed(2))
    },
    description: `业务事件数据质量和类型分布`
  };
}

/**
 * 验证数据覆盖范围
 */
async function validateDataCoverage(): Promise<IntegrityCheck> {
  const coverageQuery = `
    SELECT 
      DATE(FROM_UNIXTIME(cm.timestamp/1000)) as date,
      COUNT(*) as message_count,
      COUNT(DISTINCT cm.chat_id) as chat_count,
      COUNT(CASE WHEN cl.satisfaction IS NOT NULL THEN 1 END) as satisfaction_count,
      COUNT(CASE WHEN cme.biz_type IS NOT NULL THEN 1 END) as event_count
    FROM chat_msg cm
    LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
    LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
    WHERE cm.timestamp IS NOT NULL
    GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
    ORDER BY date DESC
    LIMIT 30
  `;
  
  const coverageData = await executeQuery(coverageQuery);
  
  // 计算最近30天的数据完整性
  const avgCompleteness = coverageData.reduce((sum, row) => {
    const completeness = (
      (row.satisfaction_count / row.message_count) * 0.3 + // 满意度覆盖30%权重
      (row.event_count / row.message_count) * 0.7 // 事件覆盖70%权重
    ) * 100;
    return sum + completeness;
  }, 0) / coverageData.length;
  
  return {
    name: 'data_coverage',
    status: avgCompleteness >= 20 ? 'pass' : avgCompleteness >= 10 ? 'warning' : 'fail',
    score: Math.min(100, avgCompleteness * 5), // 调整评分标准
    details: {
      actual: coverageData.length,
      percentage: Number(avgCompleteness.toFixed(2))
    },
    description: `最近30天数据覆盖完整性`
  };
}

/**
 * 计算时间戳一致性评分
 */
function calculateTimestampConsistencyScore(dateRanges: Array<{
  table: string;
  start: number;
  end: number;
  count: number;
}>): number {
  if (dateRanges.length < 2) return 100;
  
  // 找到最大时间范围
  const overallStart = Math.min(...dateRanges.map(r => r.start));
  const overallEnd = Math.max(...dateRanges.map(r => r.end));
  const overallRange = overallEnd - overallStart;
  
  // 计算每个表的时间范围覆盖度
  let totalCoverage = 0;
  dateRanges.forEach(range => {
    const coverage = (range.end - range.start) / overallRange;
    totalCoverage += coverage;
  });
  
  // 返回平均覆盖度作为评分
  return Math.min(100, (totalCoverage / dateRanges.length) * 100);
}

/**
 * 计算总体评分
 */
function calculateOverallScore(checks: IntegrityCheck[]): number {
  if (checks.length === 0) return 0;
  
  const totalScore = checks.reduce((sum, check) => sum + check.score, 0);
  return Math.round(totalScore / checks.length);
}

/**
 * 生成优化建议
 */
function generateRecommendations(checks: IntegrityCheck[]): string[] {
  const recommendations: string[] = [];
  
  checks.forEach(check => {
    switch (check.name) {
      case 'chat_msg_to_event_relation':
        if (check.status !== 'pass') {
          recommendations.push('建议优化chat_msg_event表的message_id关联逻辑，提升事件数据关联率');
        }
        break;
      case 'satisfaction_data_quality':
        if (check.status !== 'pass') {
          recommendations.push('建议增加满意度评价的收集渠道，提升满意度数据覆盖率');
        }
        break;
      case 'timestamp_consistency':
        if (check.status !== 'pass') {
          recommendations.push('建议检查各表时间戳字段的一致性，确保时间范围对齐');
        }
        break;
      case 'business_events_quality':
        if (check.status !== 'pass') {
          recommendations.push('建议检查业务事件数据收集逻辑，确保工单和线索事件的完整记录');
        }
        break;
      case 'data_coverage':
        if (check.status !== 'pass') {
          recommendations.push('建议优化数据收集流程，提升各维度数据的覆盖完整性');
        }
        break;
    }
  });
  
  return recommendations;
}

/**
 * 生成数据质量报告
 */
export async function generateDataQualityReport(): Promise<string> {
  const report = await validateDataIntegrity();
  
  let output = '# 数据完整性验证报告\n\n';
  output += `**生成时间**: ${new Date(report.timestamp).toLocaleString('zh-CN')}\n`;
  output += `**总体评分**: ${report.overall_score}/100\n\n`;
  
  output += '## 检查结果\n\n';
  report.checks.forEach(check => {
    const statusIcon = check.status === 'pass' ? '✅' : check.status === 'warning' ? '⚠️' : '❌';
    output += `### ${statusIcon} ${check.description}\n`;
    output += `- **状态**: ${check.status.toUpperCase()}\n`;
    output += `- **评分**: ${check.score}/100\n`;
    if (check.details.percentage) {
      output += `- **覆盖率**: ${check.details.percentage}%\n`;
    }
    if (check.details.issues?.length) {
      output += `- **问题**: ${check.details.issues.join(', ')}\n`;
    }
    output += '\n';
  });
  
  if (report.recommendations.length > 0) {
    output += '## 优化建议\n\n';
    report.recommendations.forEach((rec, index) => {
      output += `${index + 1}. ${rec}\n`;
    });
  }
  
  return output;
} 