import { getConnection } from './mysql-db'

// 简单的查询封装函数
async function executeQuery(query: string, params: any[] = []): Promise<any[]> {
  const connection = await getConnection()
  try {
    const [rows] = await connection.execute(query, params)
    return rows as any[]
  } finally {
    connection.release()
  }
}

// 数据类型定义，保持与原有系统兼容
export interface ConversationStats {
  id: string
  chat_id: string
  created_at: string
  user_id: string
  content: string
  project: {
    id: string
    name: string
    group?: string
    area?: string
    company?: string
  } | null
  grid_name: string | null
  work_order: number
  lead: number
  survey: string | null
  user_emotion: string | null
  company: {
    id: string
    name: string
  } | null
}

export interface DashboardStats {
  active_projects: number
  total_conversations: number
  total_work_orders: number
  total_leads: number
  overall_satisfaction_rate: number
  nps_score: number
  nps_promoters: number
  nps_passives: number
  nps_detractors: number
  user_emotion_stats: Array<{
    emotion: string
    count: number
    percentage: number
  }>
  active_grids: number
  active_grids_data: Array<{
    grid_name: string
    conversation_count: number
  }>
  inactive_projects: any[]
  inactive_projects_by_area: any[]
  inactive_projects_by_company: any[]
  inactive_grids: any[]
}

export interface QueryFilters {
  startDate?: string
  endDate?: string
  projectId?: string
  companyId?: string
  group?: string
  area?: string
  grid?: string
  month?: string
  survey?: string
}

/**
 * 获取对话统计数据
 */
export async function getConversationStats(filters: QueryFilters = {}): Promise<ConversationStats[]> {
  console.log('🔍 MySQL查询对话统计数据', filters)
  
  try {
    let whereConditions = ['cm.payload IS NOT NULL']
    const params: any[] = []
    
    // 时间范围过滤
    if (filters.startDate) {
      whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ?')
      params.push(filters.startDate)
    }
    
    if (filters.endDate) {
      whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) <= ?')
      params.push(`${filters.endDate} 23:59:59`)
    }
    
    // 月份过滤
    if (filters.month) {
      whereConditions.push('DATE_FORMAT(FROM_UNIXTIME(cm.timestamp/1000), "%Y-%m") = ?')
      params.push(filters.month)
    }
    
    // 项目过滤
    if (filters.projectId) {
      whereConditions.push('cl.project_code = ?')
      params.push(filters.projectId)
    }
    
    // 网格过滤
    if (filters.grid) {
      whereConditions.push('sb.grid_name = ?')
      params.push(filters.grid)
    }
    
    // 满意度过滤
    if (filters.survey) {
      whereConditions.push('JSON_UNQUOTE(JSON_EXTRACT(cm.payload, "$.survey")) = ?')
      params.push(filters.survey)
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    const query = `
      SELECT 
        cm.id,
        cm.chat_id,
        FROM_UNIXTIME(cm.timestamp/1000) AS created_at,
        cm.external_user_id AS user_id,
        CASE 
          WHEN JSON_VALID(cm.payload) THEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.text'))
          ELSE NULL
        END AS content,
        cl.project_code AS project_id,
        cl.project_name AS project_name,
        sb.grid_name AS grid_name,
        CASE 
          WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.work_order')) = 'true' THEN 1
          ELSE 0
        END AS work_order,
        CASE 
          WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.lead')) = 'true' THEN 1
          ELSE 0
        END AS lead,
        CASE 
          WHEN JSON_VALID(cm.payload) THEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey'))
          ELSE NULL
        END AS survey,
        CASE 
          WHEN JSON_VALID(cm.payload) THEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.user_emotion'))
          ELSE NULL
        END AS user_emotion
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      ORDER BY cm.timestamp DESC
      LIMIT 50000
    `
    
    console.log('🔍 执行查询:', query.substring(0, 200) + '...')
    const rows = await executeQuery(query, params)
    
    const conversations: ConversationStats[] = rows.map((row: any) => ({
      id: String(row.id),
      chat_id: String(row.chat_id),
      created_at: row.created_at,
      user_id: String(row.user_id || ''),
      content: row.content || '',
      project: row.project_id ? {
        id: row.project_id,
        name: row.project_name || row.project_id
      } : null,
      grid_name: row.grid_name,
      work_order: Number(row.work_order || 0),
      lead: Number(row.lead || 0),
      survey: row.survey,
      user_emotion: row.user_emotion,
      company: null // TODO: 如需要可从项目信息中获取
    }))
    
    console.log(`✅ 查询完成，返回 ${conversations.length} 条对话记录`)
    return conversations
    
  } catch (error) {
    console.error('❌ MySQL查询对话统计失败:', error)
    throw error
  }
}

/**
 * 获取仪表板统计数据
 */
export async function getDashboardStats(filters: QueryFilters = {}): Promise<DashboardStats> {
  console.log('📊 MySQL查询仪表板统计', filters)
  
  try {
    const conversations = await getConversationStats(filters)
    
    // 计算基础统计
    const activeProjects = new Set(
      conversations
        .filter(c => c.project?.id)
        .map(c => c.project!.id)
    ).size
    
    const totalWorkOrders = conversations.filter(c => c.work_order > 0).length
    const totalLeads = conversations.filter(c => c.lead > 0).length
    
    // 计算满意度
    const surveyResponses = conversations.filter(c => c.survey && ['很满意', '满意', '一般', '不满', '很不满'].includes(c.survey))
    const positiveSurveys = surveyResponses.filter(c => ['很满意', '满意'].includes(c.survey!)).length
    const satisfactionRate = surveyResponses.length > 0 ? positiveSurveys / surveyResponses.length : 0
    
    // 计算NPS
    const npsResponses = conversations.filter(c => c.survey && ['很满意', '满意', '一般', '不满', '很不满'].includes(c.survey))
    const promoters = npsResponses.filter(c => c.survey === '很满意').length
    const passives = npsResponses.filter(c => ['满意', '一般'].includes(c.survey!)).length
    const detractors = npsResponses.filter(c => ['不满', '很不满'].includes(c.survey!)).length
    const npsScore = npsResponses.length > 0 ? ((promoters - detractors) / npsResponses.length) * 100 : 0
    
    // 计算情感统计
    const emotionStats = conversations
      .filter(c => c.user_emotion)
      .reduce((acc: { [key: string]: number }, c) => {
        const emotion = c.user_emotion!
        acc[emotion] = (acc[emotion] || 0) + 1
        return acc
      }, {})
    
    const totalEmotions = Object.values(emotionStats).reduce((sum, count) => sum + count, 0)
    const userEmotionStats = Object.entries(emotionStats).map(([emotion, count]) => ({
      emotion,
      count,
      percentage: totalEmotions > 0 ? (count / totalEmotions) * 100 : 0
    }))
    
    // 计算网格统计
    const gridStats = conversations
      .filter(c => c.grid_name)
      .reduce((acc: { [key: string]: number }, c) => {
        const grid = c.grid_name!
        acc[grid] = (acc[grid] || 0) + 1
        return acc
      }, {})
    
    const activeGridsData = Object.entries(gridStats).map(([grid_name, conversation_count]) => ({
      grid_name,
      conversation_count
    }))
    
    const stats: DashboardStats = {
      active_projects: activeProjects,
      total_conversations: conversations.length,
      total_work_orders: totalWorkOrders,
      total_leads: totalLeads,
      overall_satisfaction_rate: satisfactionRate,
      nps_score: npsScore,
      nps_promoters: promoters,
      nps_passives: passives,
      nps_detractors: detractors,
      user_emotion_stats: userEmotionStats,
      active_grids: Object.keys(gridStats).length,
      active_grids_data: activeGridsData,
      inactive_projects: [],
      inactive_projects_by_area: [],
      inactive_projects_by_company: [],
      inactive_grids: []
    }
    
    console.log('✅ 仪表板统计计算完成:', {
      activeProjects: stats.active_projects,
      totalConversations: stats.total_conversations,
      satisfactionRate: stats.overall_satisfaction_rate.toFixed(2)
    })
    
    return stats
    
  } catch (error) {
    console.error('❌ MySQL仪表板统计失败:', error)
    throw error
  }
}

/**
 * 获取情感趋势
 */
export async function getSentimentTrend(days: number = 30): Promise<Array<{ date: string; sentiment: number }>> {
  console.log(`📈 MySQL查询情感趋势 (${days}天)`)
  
  try {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
    
    const query = `
      SELECT 
        DATE(FROM_UNIXTIME(cm.timestamp/1000)) AS date,
        AVG(
          CASE 
            WHEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) = '很满意' THEN 1.0
            WHEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) = '满意' THEN 0.8
            WHEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) = '一般' THEN 0.6
            WHEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) = '不满' THEN 0.4
            WHEN JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) = '很不满' THEN 0.2
            ELSE 0.6
          END
        ) AS avg_sentiment
      FROM chat_msg cm
      WHERE FROM_UNIXTIME(cm.timestamp/1000) >= ?
        AND FROM_UNIXTIME(cm.timestamp/1000) <= ?
        AND cm.payload IS NOT NULL
      GROUP BY DATE(FROM_UNIXTIME(cm.timestamp/1000))
      ORDER BY date
    `
    
    const rows = await executeQuery(query, [
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    ])
    
    const trend = rows.map((row: any) => ({
      date: row.date,
      sentiment: Number(row.avg_sentiment || 0.6)
    }))
    
    console.log(`✅ 情感趋势查询完成，返回 ${trend.length} 个数据点`)
    return trend
    
  } catch (error) {
    console.error('❌ MySQL情感趋势查询失败:', error)
    return []
  }
}

/**
 * 获取所有对话数据用于缓存（如果需要的话）
 */
export async function getAllConversationsForCache(): Promise<ConversationStats[]> {
  console.log('📊 MySQL获取所有对话数据用于缓存')
  return await getConversationStats()
}

/**
 * 获取所有项目
 */
export async function getAllProjects(): Promise<Array<{ id: string; name: string; group?: string; area?: string; company?: string }>> {
  console.log('📋 MySQL查询所有项目')
  
  try {
    const query = `
      SELECT DISTINCT
        cl.project_code AS id,
        cl.project_name AS name
      FROM chat_list cl
      WHERE cl.project_code IS NOT NULL
        AND cl.project_code != ''
      ORDER BY cl.project_name
    `
    
    const rows = await executeQuery(query)
    
    const projects = rows.map((row: any) => ({
      id: row.id,
      name: row.name || row.id
    }))
    
    console.log(`✅ 项目查询完成，返回 ${projects.length} 个项目`)
    return projects
    
  } catch (error) {
    console.error('❌ MySQL项目查询失败:', error)
    return []
  }
} 