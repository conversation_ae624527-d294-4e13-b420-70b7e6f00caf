# Chat Analytics Platform

一个基于 AI 的对话分析系统，提供深度客户交互洞察，驱动业务增长。

## ✨ 主要特性

- **🔍 对话分析**: 追踪和分析对话数量随时间的变化
- **😊 情感分析**: 通过调研结果测量客户满意度
- **📊 项目活动跟踪**: 监控不同项目的参与度
- **📈 转化指标**: 跟踪工单和潜在客户转化率
- **🏷️ 主题分布**: 了解客户讨论的话题
- **🏆 排名仪表板**: 比较公司、地区和项目的表现
- **📊 NPS 跟踪**: 监控净推荐值趋势
- **⚡ 智能缓存**: 内存缓存系统，提供毫秒级查询响应

## 🚀 技术栈

### 核心技术

- **Next.js 15** with App Router - 现代化的React框架
- **React 19** - 最新的React版本
- **TypeScript** - 类型安全的JavaScript
- **Material UI v7** - 现代化的UI组件库
- **MUI X v8** - 高级数据可视化组件

### 认证与数据

- **Clerk** - 现代化身份认证解决方案
- **Supabase** - 开源的Firebase替代方案
- **Recharts** - 优雅的数据可视化库
- **内存缓存系统** - 高性能数据缓存

### 开发工具

- **Jest** - 单元测试框架
- **React Testing Library** - React组件测试
- **Prettier** - 代码格式化
- **ESLint** - 代码质量检查
- **Husky** - Git钩子管理

## 🏗️ 项目结构

```
chat-analytics/
├── app/                    # Next.js App Router页面
│   ├── dashboard/         # 仪表板页面
│   ├── api/              # API路由
│   │   ├── cache/        # 缓存管理API
│   │   └── cached-dashboard-stats/ # 缓存优化的数据API
│   └── page.tsx          # 主页面
├── components/            # React组件
│   ├── landing/          # 着陆页组件
│   ├── charts/           # 图表组件
│   ├── ui/               # 通用UI组件
│   ├── layout/           # 布局组件
│   └── CacheStatus.tsx  # 缓存状态监控组件
├── lib/                  # 工具函数和配置
│   ├── cache.ts          # 核心缓存管理器
│   ├── cache-init.ts     # 缓存初始化脚本
│   ├── cached-queries.ts # 缓存查询接口
│   ├── supabase-queries.ts  # 安全的数据库查询
│   └── i18n.ts           # 国际化工具
├── hooks/                # 自定义React钩子
└── migrations/           # 数据库迁移文件
```

## 🛠️ 开始使用

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

```bash
npm install
```

### 环境变量配置

创建 `.env.local` 文件并配置以下变量：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Clerk认证配置
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
```

### 开发环境启动

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

**注意**: 首次启动时，缓存系统会自动初始化并加载数据，这可能需要几秒钟时间。

## 📝 可用脚本

```bash
# 开发
npm run dev              # 启动开发服务器（使用Turbopack）
npm run build            # 构建生产版本
npm run start            # 启动生产服务器

# 代码质量
npm run lint             # 运行ESLint检查
npm run lint:fix         # 自动修复ESLint错误
npm run type-check       # TypeScript类型检查
npm run format           # 格式化代码
npm run format:check     # 检查代码格式

# 测试
npm run test             # 运行测试
npm run test:watch       # 监听模式运行测试
npm run test:coverage    # 生成测试覆盖率报告

# 分析
npm run analyze          # 分析bundle大小
```

## 🔒 安全特性

### ✅ 已实现的安全措施

- **类型安全的数据库查询** - 替代原始SQL执行
- **输入验证** - 对所有用户输入进行验证
- **参数化查询** - 防止SQL注入
- **权限检查** - 基于用户角色的访问控制
- **错误处理** - 安全的错误信息处理
- **缓存隔离** - 内存缓存与数据库访问隔离

### 🚨 安全建议

- 在生产环境中启用Supabase的Row Level Security (RLS)
- 定期更新依赖包以修复安全漏洞
- 实施API速率限制
- 监控异常活动
- 定期清理缓存中的敏感数据

## 🧪 测试

项目包含完整的测试套件：

```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

测试覆盖以下方面：

- 组件渲染测试
- 用户交互测试
- API集成测试
- 数据库查询测试

## 🌍 国际化

项目支持多语言：

- 🇨🇳 中文 (zh)
- 🇺🇸 英文 (en)

语言文件位于 `lib/i18n.ts`，可以轻松添加新语言支持。

## 📈 性能优化

### 已实现的优化

- **智能缓存系统** - 毫秒级数据查询响应
- **代码分割** - 按需加载组件
- **图片优化** - 支持WebP和AVIF格式
- **Bundle分析** - 使用`npm run analyze`分析包大小
- **字体优化** - 自动优化字体加载
- **缓存策略** - 优化静态资源缓存
- **预计算数据** - 项目和公司分组预计算

### Lighthouse评分目标

- Performance: > 95 (缓存系统提升)
- Accessibility: > 95
- Best Practices: > 90
- SEO: > 90

### 缓存性能指标

| 场景 | 无缓存 | 有缓存 | 性能提升 |
|------|--------|--------|----------|
| 仪表板加载 | ~800ms | ~50ms | **16x** |
| 数据过滤 | ~500ms | ~5ms | **100x** |
| 项目列表 | ~300ms | ~2ms | **150x** |
| 趋势查询 | ~600ms | ~8ms | **75x** |

## 🚀 部署

### Vercel部署（推荐）

1. 推送代码到GitHub
2. 在Vercel中导入项目
3. 配置环境变量
4. 部署

**生产环境注意事项**:
- 缓存系统会在应用启动时自动初始化
- 建议配置健康检查端点 `/api/cache/status`
- 监控缓存刷新日志以确保系统正常运行

### 其他平台

项目兼容任何支持Node.js的托管平台：

- Netlify
- Railway
- AWS Amplify
- Digital Ocean App Platform

## 🔧 缓存系统配置

### 配置选项

在 `lib/cache.ts` 中可以调整以下配置：

```typescript
// 缓存刷新间隔（默认1小时）
private readonly CACHE_DURATION = 60 * 60 * 1000

// 情感趋势天数（默认30天）
getSentimentTrend(30)
```

### 监控和维护

1. **状态监控**: 在仪表板中添加 `<CacheStatus />` 组件
2. **手动刷新**: 通过API或界面手动刷新缓存
3. **日志监控**: 关注应用启动和缓存刷新日志
4. **性能监控**: 监控查询响应时间和缓存命中率

## 🤝 贡献指南

1. Fork本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

### 代码规范

- 使用TypeScript编写所有新代码
- 遵循Prettier格式化规则
- 为新功能编写测试
- 更新相关文档
- 考虑对缓存系统的影响

## 📚 学习资源

- [Next.js文档](https://nextjs.org/docs)
- [Material UI组件库](https://mui.com/)
- [Clerk认证文档](https://clerk.com/docs)
- [Supabase文档](https://supabase.com/docs)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题：

1. 查看[常见问题](docs/FAQ.md)
2. 搜索现有的[Issues](https://github.com/your-repo/chat-analytics/issues)
3. 创建新的Issue并提供详细信息

### 缓存相关问题

- **缓存未初始化**: 检查Supabase环境变量配置
- **查询缓慢**: 确认缓存状态是否为"健康"
- **数据不同步**: 手动刷新缓存或检查定时刷新设置

---

<div align="center">
  <p>Built with ❤️ using Next.js, TypeScript and Smart Caching</p>
  <p>⚡ Powered by intelligent in-memory caching for lightning-fast queries</p>
</div>

## ⚡ 缓存系统

### 缓存特性

- **🚀 启动预加载**: 应用启动时自动加载所有数据到内存
- **⏰ 定时刷新**: 每小时自动更新缓存数据
- **🔄 手动刷新**: 支持通过API或界面手动刷新
- **📊 实时监控**: 缓存状态实时监控和健康检查
- **🛡️ 降级机制**: 缓存失败时自动回退到直接数据库查询

### 缓存API

```bash
# 查看缓存状态
GET /api/cache/status

# 手动刷新缓存
POST /api/cache/refresh

# 使用缓存的数据API
GET /api/cached-dashboard-stats
```

### 性能优势

- **查询速度**: 从数据库查询 ~500ms → 缓存查询 ~5ms
- **并发处理**: 支持高并发查询而不影响数据库性能
- **数据分组**: 预计算的项目和公司分组，提升过滤查询效率
