const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 移除了危险的ESLint和TypeScript跳过配置
  // 这些应该在开发过程中修复，而不是在构建时忽略

  // 启用严格模式以获得更好的性能和错误检测
  reactStrictMode: true,

  // 优化图片
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Turbopack 配置 (移出experimental)
  turbopack: {
    resolveAlias: {
      '@': './',
    },
  },

  // 性能优化
  compress: true,

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 重定向规则（如果需要）
  async redirects() {
    return []
  },

  // 重写规则（如果需要）
  async rewrites() {
    return []
  },

  // Webpack配置自定义
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 添加自定义webpack配置
    if (!dev && !isServer) {
      // 生产环境优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
          // 将MUI相关依赖打包到一起
          mui: {
            name: 'mui',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@mui|@emotion)[\\/]/,
            priority: 40,
          },
          // 将React相关依赖打包到一起
          react: {
            name: 'react',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 30,
          },
          // 其他vendor库
          vendor: {
            name: 'vendor',
            chunks: 'all',
            test: /[\\/]node_modules[\\/]/,
            priority: 20,
          },
        },
      }
    }

    return config
  },
}

module.exports = withBundleAnalyzer(nextConfig)
