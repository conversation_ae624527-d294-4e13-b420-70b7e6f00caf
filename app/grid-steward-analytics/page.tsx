'use client'

import React, { useState, useEffect, Suspense } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  <PERSON>ert,
  Skeleton,
  <PERSON>bs,
  Tab,
  Grid as Mui<PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import PeopleIcon from '@mui/icons-material/People'
import GridOnIcon from '@mui/icons-material/GridOn'
import VerifiedIcon from '@mui/icons-material/Verified'
import StarIcon from '@mui/icons-material/Star'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { useAnalyticsData } from '@/hooks/use-analytics-data'
import { LineChart } from '@/components/charts/line-chart'
import { BarChart } from '@/components/charts/bar-chart'
import { DataTable } from '@/components/ui/data-table'
import { GridStewardAnalyticsSection } from '@/components/ui/grid-steward-analytics-section'

import { useAuth, UserButton } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { format } from 'date-fns'
import { useSearchParams } from 'next/navigation'
import { buildNavigationUrl, parseUrlParams } from '@/lib/navigation-utils'

// Create wrapper components to be compatible with MUI v8.3.1
function Grid(props: {
  size?: Record<string, number>
  children: React.ReactNode
  [key: string]: any
}) {
  const { size, ...rest } = props

  // For MUI v8, convert size props to sx prop with width
  const sxWidth: Record<string, string> = {}

  if (size) {
    if (size.xs) sxWidth.xs = `${(size.xs / 12) * 100}%`
    if (size.sm) sxWidth.sm = `${(size.sm / 12) * 100}%`
    if (size.md) sxWidth.md = `${(size.md / 12) * 100}%`
    if (size.lg) sxWidth.lg = `${(size.lg / 12) * 100}%`
  }

  return (
    <MuiGrid
      {...rest}
      sx={{
        ...rest.sx,
        width: sxWidth,
        flexGrow: 1,
      }}
    >
      {props.children}
    </MuiGrid>
  )
}

// 标签页面板组件
interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`grid-tabpanel-${index}`}
      aria-labelledby={`grid-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  )
}

// 内部组件，使用useSearchParams
function GridStewardAnalyticsContent() {
  // 身份验证检查
  const { userId, isLoaded } = useAuth()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (isLoaded && !userId) {
      redirect('/sign-in')
    }
  }, [isLoaded, userId])

  const [tabValue, setTabValue] = useState(0)
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null)
  const [selectedArea, setSelectedArea] = useState<string | null>(null)
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [selectedGrid, setSelectedGrid] = useState<string | null>(null)
  const [showGrids, setShowGrids] = useState<boolean>(true) // 网格管家页面默认显示网格
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化: 从URL参数获取筛选器状态，如果没有则设置默认日期范围
  useEffect(() => {
    if (!isLoaded) return

    const urlFilters = parseUrlParams(searchParams)
    
    // 如果URL中有筛选器参数，使用它们
    if (Object.keys(urlFilters).length > 0) {
      if (urlFilters.selectedMonth) setSelectedMonth(urlFilters.selectedMonth)
      if (urlFilters.selectedGroup) setSelectedGroup(urlFilters.selectedGroup)
      if (urlFilters.selectedArea) setSelectedArea(urlFilters.selectedArea)
      if (urlFilters.selectedCompany) setSelectedCompany(urlFilters.selectedCompany)
      if (urlFilters.selectedProject) setSelectedProject(urlFilters.selectedProject)
      if (urlFilters.selectedGrid) setSelectedGrid(urlFilters.selectedGrid)
      if (urlFilters.showGrids !== undefined) setShowGrids(urlFilters.showGrids)
      if (urlFilters.startDate) setStartDate(urlFilters.startDate)
      if (urlFilters.endDate) setEndDate(urlFilters.endDate)
    } else {
      // 没有URL参数时使用默认值
      const defaultEndDate = new Date('2025-05-27')
      const defaultStartDate = new Date('2025-05-21')
      setStartDate(defaultStartDate)
      setEndDate(defaultEndDate)
      setShowGrids(true) // 网格管家页面默认显示网格

      if (defaultStartDate) {
        setSelectedMonth(format(defaultStartDate, 'yyyy-MM'))
      }
    }
    
    setIsInitialized(true)
  }, [isLoaded, searchParams])

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    setStartDate(start)
    setEndDate(end)
    setSelectedMonth(start ? format(start, 'yyyy-MM') : null)
  }

  // 使用现有的 analytics data hook，现在支持过滤器
  const {
    months,
    groups,
    areas,
    companies,
    projects,
    grids,
    conversationCounts,
    surveyResults,
    projectActivity,
    conversionRates,
    hourlyAnalysis,

    inactiveGrids,
    loadMoreInactiveGrids,
    hasMoreInactiveGrids,
    isLoadingMoreInactiveGrids,
    isLoading,
    error,
  } = useAnalyticsData(
    selectedMonth,
    selectedProject,
    selectedGroup,
    selectedArea,
    selectedCompany,
    startDate,
    endDate,
    selectedGrid,
    showGrids
  )

  // 从现有数据中计算网格管家相关指标
  const calculateGridStewardMetrics = () => {
    // 只使用真实数据，如果没有数据则显示0
    const totalActiveGrids = grids?.length || 0
    const totalActiveStewards = projectActivity?.length || 0
    
    const totalSurveys = surveyResults?.reduce((total, item) => total + item.total_survey, 0) || 0
    const satisfiedSurveys = surveyResults?.reduce((total, item) => 
      total + item.very_satisfied + item.satisfied, 0) || 0
    const avgSatisfactionRate = totalSurveys > 0 ? Math.round((satisfiedSurveys / totalSurveys) * 100) : 0
    const certificationRate = 0 // 没有真实数据时显示0

    console.log('Grid Steward Metrics:', {
      gridsLength: grids?.length,
      projectsLength: projects?.length,
      totalActiveGrids,
      totalActiveStewards,
      avgSatisfactionRate,
      certificationRate,
      totalSurveys,
      satisfiedSurveys,
      showGrids
    })

    return {
      totalActiveGrids,
      totalActiveStewards,
      certificationRate,
      avgSatisfactionRate,
    }
  }

  const metrics = calculateGridStewardMetrics()

  // 加载中状态
  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    )
  }

  return (
    <DashboardLayout
      months={months}
      groups={groups}
      areas={areas}
      companies={companies}
      projects={projects}
      grids={grids}
      selectedMonth={selectedMonth}
      selectedGroup={selectedGroup}
      selectedArea={selectedArea}
      selectedCompany={selectedCompany}
      selectedProject={selectedProject}
      selectedGrid={selectedGrid}
      showGrids={showGrids}
      startDate={startDate}
      endDate={endDate}
      onMonthChange={setSelectedMonth}
      onGroupChange={setSelectedGroup}
      onAreaChange={setSelectedArea}
      onCompanyChange={setSelectedCompany}
      onProjectChange={setSelectedProject}
      onGridChange={setSelectedGrid}
      onShowGridsChange={setShowGrids}
      onDateRangeChange={handleDateRangeChange}
    >
      <Box width="100%" sx={{ px: { xs: 0, md: 2 } }}>
        {/* 导航栏 */}
        <Card sx={{ mb: 3, p: 2 }}>
          <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/dashboard', {
                selectedMonth,
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                selectedGrid,
                showGrids,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              📊 综合分析
            </Button>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/work-order-analytics', {
                selectedMonth,
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                selectedGrid,
                showGrids,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              📋 工单分析
            </Button>
            <Button
              variant="contained"
              color="primary"
              href="/grid-steward-analytics"
              sx={{ textTransform: 'none' }}
            >
              🏠 网格管家分析
            </Button>
            
            {/* 用户信息 - 靠右侧显示 */}
            <Box sx={{ marginLeft: 'auto' }}>
              {userId ? (
                <UserButton 
                  appearance={{
                    elements: {
                      userButtonAvatarBox: {
                        width: 32,
                        height: 32
                      }
                    }
                  }}
                />
              ) : (
                <Button
                  variant="outlined"
                  href="/sign-in"
                  size="small"
                  sx={{ textTransform: 'none' }}
                >
                  登录
                </Button>
              )}
            </Box>
          </Box>
        </Card>

        {/* 标签页导航 */}
        <Card sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={(event, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab
              icon={<GridOnIcon />}
              label="网格概览"
              id="grid-tab-0"
              aria-controls="grid-tabpanel-0"
            />
            <Tab
              icon={<PeopleIcon />}
              label="网格管家分析"
              id="grid-tab-1"
              aria-controls="grid-tabpanel-1"
            />
            <Tab
              icon={<TrendingUpIcon />}
              label="网格深度分析"
              id="grid-tab-2"
              aria-controls="grid-tabpanel-2"
            />
          </Tabs>
        </Card>

        {/* 加载状态 */}
        {isLoading && (
          <Card variant="outlined" sx={{ p: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Skeleton variant="text" width={180} height={40} />
          </Card>
        )}

        {/* 错误状态 */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            加载数据时出错: {error}
          </Alert>
        )}

        {/* 标签页内容 */}
        {!isLoading && !error && (
          <>
            {/* 网格管家概览标签页 */}
            <TabPanel value={tabValue} index={0}>
              {/* 核心指标概览 */}
              <Card variant="outlined" sx={{ boxShadow: 1, mb: 4 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                    网格管家核心指标
                  </Typography>
                  <MuiGrid container spacing={3}>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          p: 3,
                          bgcolor: 'customCard.blue',
                          borderColor: 'chart.blue',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <GridOnIcon sx={{ fontSize: 40, color: 'chart.blue', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" textAlign="center">
                          活跃网格数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.blue">
                          {metrics.totalActiveGrids}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          p: 3,
                          bgcolor: 'customCard.green',
                          borderColor: 'chart.green',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <PeopleIcon sx={{ fontSize: 40, color: 'chart.green', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" textAlign="center">
                          活跃管家数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.green">
                          {metrics.totalActiveStewards}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          p: 3,
                          bgcolor: 'customCard.purple',
                          borderColor: 'chart.purple',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <VerifiedIcon sx={{ fontSize: 40, color: 'chart.purple', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" textAlign="center">
                          认证率
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.purple">
                          {metrics.certificationRate}%
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          p: 3,
                          bgcolor: 'customCard.yellow',
                          borderColor: 'chart.orange',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <StarIcon sx={{ fontSize: 40, color: 'chart.orange', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" textAlign="center">
                          平均满意度
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.orange">
                          {metrics.avgSatisfactionRate}%
                        </Typography>
                      </Card>
                    </Grid>
                  </MuiGrid>
                </CardContent>
              </Card>

              {/* 高级网格管家分析组件 */}
              <GridStewardAnalyticsSection 
                startDate={startDate}
                endDate={endDate}
                selectedGroup={selectedGroup}
                selectedArea={selectedArea}
                selectedCompany={selectedCompany}
                selectedProject={selectedProject}
              />
            </TabPanel>

                        {/* 网格管家分析标签页 */}
            <TabPanel value={tabValue} index={1}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {/* 网格管家详细分析报告 */}
                <GridStewardAnalyticsSection 
                  startDate={startDate}
                  endDate={endDate}
                  selectedGroup={selectedGroup}
                  selectedArea={selectedArea}
                  selectedCompany={selectedCompany}
                  selectedProject={selectedProject}
                />

                {/* 网格管家绩效分析 */}
                <Card variant="outlined" sx={{ boxShadow: 1 }}>
                  <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                    <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                      网格管家绩效分析
                    </Typography>
                    <MuiGrid container spacing={3}>
                      <Grid size={{ xs: 12, md: 6 }}>
                        <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                          <CardContent sx={{ p: 2, height: '100%' }}>
                            <Typography variant="h6" mb={2} textAlign="center">
                              管家服务效率分布
                            </Typography>
                            <BarChart
                              data={hourlyAnalysis || []}
                              xDataKey="hour"
                              yDataKeys={[
                                { key: 'conversation_count', name: '服务次数', color: '#2196f3' }
                              ]}
                              title="24小时服务分布"
                              fileName="steward-hourly-service"
                            />
                          </CardContent>
                        </Card>
                      </Grid>

                      <Grid size={{ xs: 12, md: 6 }}>
                        <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                          <CardContent sx={{ p: 2, height: '100%' }}>
                            <Typography variant="h6" mb={2} textAlign="center">
                              网格管家满意度统计
                            </Typography>
                            <LineChart
                              data={hourlyAnalysis || []}
                              xDataKey="hour"
                              yDataKeys={[
                                { key: 'satisfaction_rate', name: '满意度(%)', color: '#4caf50' }
                              ]}
                              title="时段满意度变化"
                              fileName="steward-satisfaction-hourly"
                            />
                          </CardContent>
                        </Card>
                      </Grid>
                    </MuiGrid>
                  </CardContent>
                </Card>

                {/* 不活跃网格列表 */}
                <MuiGrid container spacing={3}>
                  <Grid size={{ xs: 12 }}>
                    <Card variant="outlined" sx={{ boxShadow: 1 }}>
                      <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                        <Typography variant="h6" mb={2}>
                          需要关注的网格
                        </Typography>
                        <DataTable
                          data={inactiveGrids?.slice(0, 20) || []}
                          columns={[
                            { key: 'grid_name', header: '网格名称' },
                            { key: 'project', header: '所属项目' },
                            { key: 'steward_name', header: '负责管家' },
                            { key: 'last_activity', header: '最后活动时间' },
                            { key: 'inactive_days', header: '非活跃天数' },
                          ]}
                          title="不活跃网格列表"
                          fileName="inactive-grids"
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </MuiGrid>
              </Box>
            </TabPanel>

                        {/* 网格深度分析标签页 */}
            <TabPanel value={tabValue} index={2}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {/* 网格覆盖分析 */}
                <Card variant="outlined" sx={{ boxShadow: 1 }}>
                  <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                    <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                      网格覆盖度分析
                    </Typography>
                    <MuiGrid container spacing={3}>
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Card
                          variant="outlined"
                          sx={{
                            height: '150px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            p: 3,
                            bgcolor: 'customCard.blue',
                            borderColor: 'chart.blue',
                          }}
                        >
                          <Typography variant="h3" fontWeight="bold" color="chart.blue">
                            {metrics.totalActiveGrids}
                          </Typography>
                          <Typography variant="body1" color="text.secondary" textAlign="center">
                            活跃网格总数
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid size={{ xs: 12, md: 4 }}>
                        <Card
                          variant="outlined"
                          sx={{
                            height: '150px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            p: 3,
                            bgcolor: 'customCard.green',
                            borderColor: 'chart.green',
                          }}
                        >
                          <Typography variant="h3" fontWeight="bold" color="chart.green">
                            {(() => {
                              const activeGrids = metrics.totalActiveGrids
                              const inactiveCount = inactiveGrids?.length || 0
                              const totalGrids = activeGrids + inactiveCount
                              return totalGrids > 0 ? Math.round((activeGrids / totalGrids) * 100) : 0
                            })()}%
                          </Typography>
                          <Typography variant="body1" color="text.secondary" textAlign="center">
                            网格覆盖率
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid size={{ xs: 12, md: 4 }}>
                        <Card
                          variant="outlined"
                          sx={{
                            height: '150px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            p: 3,
                            bgcolor: 'customCard.orange',
                            borderColor: 'chart.orange',
                          }}
                        >
                          <Typography variant="h3" fontWeight="bold" color="chart.orange">
                            {inactiveGrids?.length || 0}
                          </Typography>
                          <Typography variant="body1" color="text.secondary" textAlign="center">
                            待激活网格
                          </Typography>
                        </Card>
                      </Grid>
                    </MuiGrid>
                  </CardContent>
                </Card>

                {/* 网格服务质量分析 */}
                <Card variant="outlined" sx={{ boxShadow: 1 }}>
                  <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                    <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                      网格服务质量分析
                    </Typography>
                    <MuiGrid container spacing={3}>
                      <Grid size={{ xs: 12, md: 6 }}>
                        <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                          <CardContent sx={{ p: 2, height: '100%' }}>
                            <Typography variant="h6" mb={2} textAlign="center">
                              网格服务响应时间分析
                            </Typography>
                            <BarChart
                              data={hourlyAnalysis || []}
                              xDataKey="hour"
                              yDataKeys={[
                                { key: 'work_order_rate', name: '响应效率(%)', color: '#ff9800' }
                              ]}
                              title="各时段响应效率"
                              fileName="grid-response-efficiency"
                            />
                          </CardContent>
                        </Card>
                      </Grid>

                      <Grid size={{ xs: 12, md: 6 }}>
                        <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                          <CardContent sx={{ p: 2, height: '100%' }}>
                            <Typography variant="h6" mb={2} textAlign="center">
                              网格服务质量评分
                            </Typography>
                            <LineChart
                              data={hourlyAnalysis || []}
                              xDataKey="hour"
                              yDataKeys={[
                                { key: 'satisfaction_rate', name: '服务质量评分', color: '#9c27b0' }
                              ]}
                              title="24小时服务质量变化"
                              fileName="grid-service-quality"
                            />
                          </CardContent>
                        </Card>
                      </Grid>
                    </MuiGrid>
                  </CardContent>
                </Card>

                {/* 网格管家工作负荷分析 */}
                <Card variant="outlined" sx={{ boxShadow: 1 }}>
                  <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                    <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                      网格管家工作负荷分析
                    </Typography>
                    <DataTable
                      data={grids?.slice(0, 15).map((grid) => ({
                        grid_name: grid.grid_name || '未知网格',
                        steward_name: '暂无数据',
                        daily_conversations: '暂无数据',
                        work_orders: '暂无数据',
                        response_time: '暂无数据',
                        satisfaction_score: '暂无数据',
                        workload_level: '暂无数据'
                      })) || []}
                      columns={[
                        { key: 'grid_name', header: '网格名称' },
                        { key: 'steward_name', header: '负责管家' },
                        { key: 'daily_conversations', header: '日均对话数' },
                        { key: 'work_orders', header: '处理工单数' },
                        { key: 'response_time', header: '平均响应时间(分钟)' },
                        { key: 'satisfaction_score', header: '满意度评分' },
                        { key: 'workload_level', header: '工作负荷' },
                      ]}
                      title="网格管家工作负荷详情"
                      fileName="steward-workload-analysis"
                    />
                  </CardContent>
                </Card>
              </Box>
            </TabPanel>
          </>
        )}
      </Box>
    </DashboardLayout>
  )
}

// 主组件，用Suspense包装GridStewardAnalyticsContent
export default function GridStewardAnalyticsPage() {
  return (
    <Suspense fallback={
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    }>
      <GridStewardAnalyticsContent />
    </Suspense>
  )
} 