'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Skeleton,
  Alert,
  Grid as MuiGrid,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
  Paper,
  Button,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
} from '@mui/material'
import {
  RefreshRounded as RefreshIcon,
  TrendingUpRounded as TrendingUpIcon,
  AssignmentRounded as FileTextIcon,
  CheckCircleRounded as CheckCircleIcon,
  ScheduleRounded as ClockIcon,
  WarningRounded as AlertTriangleIcon,
  PeopleRounded as UsersIcon,
  BusinessRounded as BuildingIcon,
  PersonRounded as UserCheckIcon,
  StarRounded as StarIcon,
} from '@mui/icons-material'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { LineChart } from '@/components/charts/line-chart'
import { Bar<PERSON><PERSON> } from '@/components/charts/bar-chart'
import { Pie<PERSON>hart } from '@/components/charts/pie-chart'
import { DataTable } from '@/components/ui/data-table'
import { useWorkOrderAnalytics } from '@/hooks/use-work-order-analytics'
import { useAnalyticsData } from '@/hooks/use-analytics-data'
import { addDays, format } from 'date-fns'
import { useAuth, UserButton } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { buildNavigationUrl, parseUrlParams } from '@/lib/navigation-utils'

// Create wrapper components to be compatible with MUI v8.3.1
function Grid(props: {
  size?: Record<string, number>
  children: React.ReactNode
  [key: string]: any
}) {
  const { size, ...rest } = props

  // For MUI v8, convert size props to sx prop with width
  const sxWidth: Record<string, string> = {}

  if (size) {
    if (size.xs) sxWidth.xs = `${(size.xs / 12) * 100}%`
    if (size.sm) sxWidth.sm = `${(size.sm / 12) * 100}%`
    if (size.md) sxWidth.md = `${(size.md / 12) * 100}%`
    if (size.lg) sxWidth.lg = `${(size.lg / 12) * 100}%`
  }

  return (
    <MuiGrid
      {...rest}
      sx={{
        ...rest.sx,
        width: sxWidth,
        flexGrow: 1,
      }}
    >
      {props.children}
    </MuiGrid>
  )
}

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`work-order-tabpanel-${index}`}
      aria-labelledby={`work-order-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

// 内部组件，使用useSearchParams
function WorkOrderAnalyticsContent() {
  // 身份验证检查
  const { userId, isLoaded } = useAuth()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (isLoaded && !userId) {
      redirect('/sign-in')
    }
  }, [isLoaded, userId])

  const [tabValue, setTabValue] = useState(0)
  const [selectedProject, setSelectedProject] = useState<string>('')
  const [selectedGroup, setSelectedGroup] = useState<string>('')
  const [selectedArea, setSelectedArea] = useState<string>('')
  const [selectedCompany, setSelectedCompany] = useState<string>('')
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化: 从URL参数获取筛选器状态，如果没有则设置默认日期范围
  useEffect(() => {
    if (!isLoaded) return

    const urlFilters = parseUrlParams(searchParams)
    
    // 如果URL中有筛选器参数，使用它们
    if (Object.keys(urlFilters).length > 0) {
      if (urlFilters.selectedGroup) setSelectedGroup(urlFilters.selectedGroup)
      if (urlFilters.selectedArea) setSelectedArea(urlFilters.selectedArea)
      if (urlFilters.selectedCompany) setSelectedCompany(urlFilters.selectedCompany)
      if (urlFilters.selectedProject) setSelectedProject(urlFilters.selectedProject)
      if (urlFilters.startDate) setStartDate(urlFilters.startDate)
      if (urlFilters.endDate) setEndDate(urlFilters.endDate)
    } else {
      // 没有URL参数时使用默认值：数据可用期间的最近30天
      const defaultEndDate = new Date('2025-05-14')
      const defaultStartDate = addDays(defaultEndDate, -30)
      setStartDate(defaultStartDate)
      setEndDate(defaultEndDate)
    }
    
    setIsInitialized(true)
  }, [isLoaded, searchParams])

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    setStartDate(start)
    setEndDate(end)
  }

  // 状态处理函数
  const handleGroupChange = (group: string | null) => {
    setSelectedGroup(group || '')
  }

  const handleAreaChange = (area: string | null) => {
    setSelectedArea(area || '')
  }

  const handleCompanyChange = (company: string | null) => {
    setSelectedCompany(company || '')
  }

  const handleProjectChange = (project: string | null) => {
    setSelectedProject(project || '')
  }

  // 获取项目和组织数据
  const { 
    months,
    groups, 
    areas,
    companies,
    projects,
    grids,
  } = useAnalyticsData(
    null,
    selectedProject || null,
    selectedGroup || null,
    selectedArea || null,
    selectedCompany || null,
    startDate,
    endDate
  )

  // 获取工单分析数据 - 现在支持日期过滤
  const {
    overview,
    statusDistribution,
    emergencyAnalysis,
    typeAnalysis,
    projectRanking,
    agentPerformance,
    dailyTrend,
    hourlyDistribution,
    customerAnalysis,
    titleKeywords,
    distributionAnalysis,
    isLoading,
    error,
    refreshData,
  } = useWorkOrderAnalytics(
    isInitialized ? startDate : null, // 只在初始化完成后使用日期
    isInitialized ? endDate : null, // 只在初始化完成后使用日期
    selectedProject || null,
    selectedGroup || null
  )

  // 获取对话转化率数据
  const {
    conversionRates,
  } = useAnalyticsData(
    null,
    selectedProject || null,
    selectedGroup || null,
    selectedArea || null,
    selectedCompany || null,
    startDate,
    endDate
  )

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  // 转换数据格式用于饼图
  const statusPieData = statusDistribution.map(item => ({
    name: item.status,
    value: item.count
  }))

  const emergencyPieData = emergencyAnalysis.map(item => ({
    name: item.emergency_level,
    value: item.count
  }))

  // 概览卡片组件
  const OverviewCards = () => {
    if (isLoading) {
      return (
        <MuiGrid container spacing={3}>
          {Array.from({ length: 8 }).map((_, i) => (
            <Grid size={{ xs: 12, sm: 6, md: 3 }} key={i}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={20} />
                  <Skeleton variant="text" width="40%" height={32} />
                  <Skeleton variant="text" width="80%" height={16} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </MuiGrid>
      )
    }

    if (!overview) {
      return (
        <Alert severity="warning">
          暂无工单数据
        </Alert>
      )
    }

    const completionRate = overview.total_orders > 0 
      ? (overview.completed_orders / overview.total_orders * 100).toFixed(1)
      : '0'

    const urgentRate = overview.total_orders > 0
      ? (overview.urgent_orders / overview.total_orders * 100).toFixed(1)
      : '0'

    const cards = [
      {
        title: '总工单数',
        value: overview.total_orders.toLocaleString(),
        icon: FileTextIcon,
        color: '#1976d2',
        bgColor: '#e3f2fd',
      },
      {
        title: '已完成',
        value: overview.completed_orders.toLocaleString(),
        icon: CheckCircleIcon,
        color: '#2e7d32',
        bgColor: '#e8f5e8',
        badge: `${completionRate}%`,
        badgeColor: parseFloat(completionRate) > 80 ? 'success' : 'default',
      },
      {
        title: '未分配',
        value: overview.unassigned_orders.toLocaleString(),
        icon: ClockIcon,
        color: '#ed6c02',
        bgColor: '#fff3e0',
        badge: overview.unassigned_orders > 0 ? '需关注' : '正常',
        badgeColor: overview.unassigned_orders > 0 ? 'error' : 'success',
      },
      {
        title: '紧急工单',
        value: overview.urgent_orders.toLocaleString(),
        icon: AlertTriangleIcon,
        color: '#d32f2f',
        bgColor: '#ffebee',
        badge: `${urgentRate}%`,
        badgeColor: parseFloat(urgentRate) > 20 ? 'error' : 'default',
      },
      {
        title: '涉及项目',
        value: overview.unique_projects.toLocaleString(),
        icon: BuildingIcon,
        color: '#7b1fa2',
        bgColor: '#f3e5f5',
      },
      {
        title: '服务客户',
        value: overview.unique_customers.toLocaleString(),
        icon: UsersIcon,
        color: '#303f9f',
        bgColor: '#e8eaf6',
      },
      {
        title: '服务代理',
        value: overview.unique_agents.toLocaleString(),
        icon: UserCheckIcon,
        color: '#0288d1',
        bgColor: '#e1f5fe',
      },
      {
        title: '满意度调查',
        value: overview.surveyed_orders.toLocaleString(),
        icon: StarIcon,
        color: '#f57c00',
        bgColor: '#fff8e1',
      },
    ]

    return (
      <MuiGrid container spacing={3}>
        {cards.map((card, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card sx={{ height: '100%', '&:hover': { boxShadow: 4 } }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    {card.title}
                  </Typography>
                  <Box 
                    sx={{ 
                      p: 1, 
                      borderRadius: 2, 
                      backgroundColor: card.bgColor,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <card.icon sx={{ color: card.color, fontSize: 20 }} />
                  </Box>
                </Box>
                
                <Box display="flex" justifyContent="space-between" alignItems="flex-end">
                  <Box>
                    <Typography variant="h4" component="div" fontWeight="bold" mb={0.5}>
                      {card.value}
                    </Typography>
                  </Box>
                  {card.badge && (
                    <Chip 
                      label={card.badge} 
                      color={card.badgeColor as any}
                      size="small"
                    />
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </MuiGrid>
    )
  }

  // 加载中状态
  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    )
  }

  return (
    <DashboardLayout
      months={months}
      groups={groups}
      areas={areas}
      companies={companies}
      projects={projects}
      grids={grids}
      selectedMonth={null}
      selectedGroup={selectedGroup}
      selectedArea={selectedArea}
      selectedCompany={selectedCompany}
      selectedProject={selectedProject}
      selectedGrid={null}
      showGrids={false}
      startDate={startDate}
      endDate={endDate}
      onMonthChange={() => {}}
      onGroupChange={handleGroupChange}
      onAreaChange={handleAreaChange}
      onCompanyChange={handleCompanyChange}
      onProjectChange={handleProjectChange}
      onGridChange={() => {}}
      onShowGridsChange={() => {}}
      onDateRangeChange={handleDateRangeChange}
    >
      <Box width="100%" sx={{ px: { xs: 0, md: 2 } }}>
        {/* 导航栏 */}
        <Card sx={{ mb: 3, p: 2 }}>
          <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/dashboard', {
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              📊 综合分析
            </Button>
            <Button
              variant="contained"
              color="primary"
              href="/work-order-analytics"
              sx={{ textTransform: 'none' }}
            >
              📋 工单分析
            </Button>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/grid-steward-analytics', {
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              🏠 网格管家分析
            </Button>
            
            {/* 用户信息 - 靠右侧显示 */}
            <Box sx={{ marginLeft: 'auto' }}>
              {userId ? (
                <UserButton 
                  appearance={{
                    elements: {
                      userButtonAvatarBox: {
                        width: 32,
                        height: 32
                      }
                    }
                  }}
                />
              ) : (
                <Button
                  variant="outlined"
                  href="/sign-in"
                  size="small"
                  sx={{ textTransform: 'none' }}
                >
                  登录
                </Button>
              )}
            </Box>
          </Box>
        </Card>

        {/* 页面功能区域 */}
        <Box display="flex" justifyContent="flex-end" alignItems="center" mb={3}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={refreshData}
            disabled={isLoading}
          >
            刷新数据
          </Button>
        </Box>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* 加载进度条 */}
        {isLoading && (
          <LinearProgress sx={{ mb: 3 }} />
        )}

        {/* 概览卡片 */}
        <OverviewCards />

        {/* 工单趋势分析 */}
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
              工单趋势分析
            </Typography>
            <MuiGrid container spacing={3}>
              {/* 每日工单趋势 */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={dailyTrend || []}
                      xDataKey="date"
                      yDataKeys={[
                        { key: 'total_orders', name: '总工单数', color: '#1976d2' },
                        { key: 'completed_orders', name: '已完成', color: '#2e7d32' },
                        { key: 'urgent_orders', name: '紧急工单', color: '#d32f2f' },
                      ]}
                      title="每日工单趋势"
                      fileName="daily-work-order-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 工单完成率趋势 */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={dailyTrend || []}
                      xDataKey="date"
                      yDataKeys={[
                        { key: 'completion_rate', name: '完成率(%)', color: '#2e7d32' },
                      ]}
                      title="工单完成率趋势"
                      fileName="completion-rate-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>
          </CardContent>
        </Card>

        {/* 转化率分析 */}
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
              转化率分析
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={2}>
              分析对话转化为工单和商机的比率趋势
            </Typography>
            <MuiGrid container spacing={3}>
              {/* 工单转化率趋势 */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={conversionRates || []}
                      xDataKey="day"
                      yDataKeys={[
                        { key: 'work_order_rate', name: '工单转化率(%)', color: '#e67e22' },
                      ]}
                      title="工单转化率趋势"
                      fileName="work-order-conversion-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 商机转化率趋势 */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={conversionRates || []}
                      xDataKey="day"
                      yDataKeys={[{ key: 'lead_rate', name: '商机转化率(%)', color: '#e74c3c' }]}
                      title="商机转化率趋势"
                      fileName="lead-conversion-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>
          </CardContent>
        </Card>

        {/* 工单分布分析 */}
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
              工单分布分析
            </Typography>
            <MuiGrid container spacing={3}>
              {/* 状态分布 */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <PieChart
                      data={statusPieData}
                      dataKey="value"
                      nameKey="name"
                      title="工单状态分布"
                      fileName="status-distribution"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 紧急程度分布 */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <PieChart
                      data={emergencyPieData}
                      dataKey="value"
                      nameKey="name"
                      title="紧急程度分布"
                      fileName="emergency-distribution"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 小时分布 */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Card variant="outlined" sx={{ height: '400px' }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <PieChart
                      data={hourlyDistribution || []}
                      dataKey="value"
                      nameKey="name"
                      title="工单创建时段分布"
                      fileName="time-period-distribution"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>
          </CardContent>
        </Card>

        {/* 详细分析 */}
        <Card sx={{ mt: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
              <Tab label="状态分析" />
              <Tab label="类型分析" />
              <Tab label="项目排名" />
              <Tab label="管家使用" />
              <Tab label="关键词分析" />
              <Tab label="客户统计" />
              <Tab label="分发方式" />
              <Tab label="客户详情" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" mb={2}>工单状态分布</Typography>
            <DataTable
              data={statusDistribution}
              columns={[
                { key: 'status', header: '状态' },
                { key: 'count', header: '数量' },
                { key: 'percentage', header: '占比(%)' },
              ]}
              title=""
              fileName="status-distribution-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" mb={2}>工单类型分析</Typography>
            <DataTable
              data={typeAnalysis}
              columns={[
                { key: 'current_type', header: '类型' },
                { key: 'count', header: '数量' },
                { key: 'percentage', header: '占比(%)' },
                { key: 'completion_rate', header: '完成率(%)' },
              ]}
              title=""
              fileName="type-analysis-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" mb={2}>项目工单排名</Typography>
            <DataTable
              data={projectRanking.slice(0, 20)}
              columns={[
                { key: 'project', header: '项目' },
                { key: 'total_orders', header: '总工单' },
                { key: 'completed_orders', header: '已完成' },
                { key: 'completion_rate', header: '完成率(%)' },
                { key: 'urgent_orders', header: '紧急工单' },
              ]}
              title=""
              fileName="project-ranking-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" mb={2}>管家使用分析</Typography>
            <DataTable
              data={agentPerformance.slice(0, 20)}
              columns={[
                { key: 'agent', header: '管家' },
                { key: 'agent_phone', header: '电话' },
                { key: 'project', header: '项目' },
                { key: 'total_orders', header: '总工单' },
                { key: 'completed_orders', header: '已完成' },
                { key: 'completion_rate', header: '完成率(%)' },
                { key: 'urgent_orders', header: '紧急工单' },
                { key: 'projects_served', header: '服务项目数' },
              ]}
              title=""
              fileName="steward-usage-analysis-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <Typography variant="h6" mb={2}>工单标题关键词分析</Typography>
            <DataTable
              data={titleKeywords}
              columns={[
                { key: 'keyword_category', header: '关键词类别' },
                { key: 'count', header: '数量' },
                { key: 'percentage', header: '占比(%)' },
                { key: 'completion_rate', header: '完成率(%)' },
              ]}
              title=""
              fileName="keywords-analysis-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={5}>
            <Typography variant="h6" mb={2}>客户分析</Typography>
            <DataTable
              data={customerAnalysis}
              columns={[
                { key: 'customer', header: '客户' },
                { key: 'total_orders', header: '总工单' },
                { key: 'completed_orders', header: '已完成' },
                { key: 'completion_rate', header: '完成率(%)' },
              ]}
              title=""
              fileName="customer-analysis-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={6}>
            <Typography variant="h6" mb={2}>分发方式分析</Typography>
            <DataTable
              data={distributionAnalysis}
              columns={[
                { key: 'distribution_method', header: '分发方式' },
                { key: 'count', header: '数量' },
                { key: 'percentage', header: '占比(%)' },
                { key: 'completion_rate', header: '完成率(%)' },
              ]}
              title=""
              fileName="distribution-analysis-table"
            />
          </TabPanel>

          <TabPanel value={tabValue} index={7}>
            <Typography variant="h6" mb={2}>客户详细分析</Typography>
            <DataTable
              data={customerAnalysis}
              columns={[
                { key: 'customer', header: '客户姓名' },
                { key: 'customer_phone', header: '联系电话' },
                { key: 'project', header: '所属项目' },
                { key: 'total_orders', header: '总工单数' },
                { key: 'completed_orders', header: '已完成' },
                { key: 'completion_rate', header: '完成率(%)' },
                { key: 'urgent_orders', header: '紧急工单' },
                { key: 'order_types_count', header: '工单类型数' },
                { key: 'last_order_time', header: '最近工单时间' },
              ]}
              title=""
              fileName="customer-detailed-analysis"
            />
          </TabPanel>
        </Card>
      </Box>
    </DashboardLayout>
  )
}

// 主组件，用Suspense包装WorkOrderAnalyticsContent
export default function WorkOrderAnalyticsPage() {
  return (
    <Suspense fallback={
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    }>
      <WorkOrderAnalyticsContent />
    </Suspense>
  )
} 