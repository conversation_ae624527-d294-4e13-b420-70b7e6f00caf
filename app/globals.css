/* Base styles for the MUI app */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  line-height: 1.6;
}

/* Background gradients for the app */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 15% 25%, rgba(79, 172, 254, 0.05), transparent 25%),
    radial-gradient(circle at 85% 75%, rgba(142, 124, 252, 0.05), transparent 25%);
  z-index: -1;
  pointer-events: none;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(240, 242, 245, 0.8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(25, 118, 210, 0.3); /* MUI primary color */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(25, 118, 210, 0.5); /* MUI primary color */
}
