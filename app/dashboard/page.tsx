'use client'

import React, { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  Box,
  Typography,
  Card,
  Card<PERSON>ontent,
  Skeleton,
  Alert,
  Grid as MuiGrid,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableBody,
  TableCell,
  Paper,
  Button,
  List,
  ListItem,
  ListItemText,

} from '@mui/material'
import {
  ChatRounded as ChatIcon,
  MessageRounded as MessageIcon,
  TrendingUpRounded as TrendingUpIcon,
  AccessTimeRounded as TimeIcon,
  RefreshRounded as RefreshIcon,
  Dashboard as DashboardIcon,
  GridOnRounded as GridIcon,
} from '@mui/icons-material'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { LineChart } from '@/components/charts/line-chart'
import { BarChart } from '@/components/charts/bar-chart'
import { PieChart } from '@/components/charts/pie-chart'
import { DataTable } from '@/components/ui/data-table'
import { DetailedTopicTable } from '@/components/ui/detailed-topic-table'
import { ProjectRankingTable } from '@/components/ui/project-ranking-table'
import { AreaRankingTable } from '@/components/ui/area-ranking-table'
import { CompanyRankingTable } from '@/components/ui/company-ranking-table'
import { useAnalyticsData } from '@/hooks/use-analytics-data'
import { NPSChart } from '@/components/charts/nps-chart'
import { useAuth, UserButton } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { format, addDays } from 'date-fns'
import { InactiveProjectList } from '@/components/ui/inactive-project-list'
import { CategoryProjectRanking } from '@/components/ui/category-project-ranking'
import { GridStewardAnalyticsSection } from '@/components/ui/grid-steward-analytics-section'
import { buildNavigationUrl, parseUrlParams } from '@/lib/navigation-utils'

// Create wrapper components to be compatible with MUI v8.3.1
function Grid(props: {
  size?: Record<string, number>
  children: React.ReactNode
  [key: string]: any
}) {
  const { size, ...rest } = props

  // For MUI v8, convert size props to sx prop with width
  const sxWidth: Record<string, string> = {}

  if (size) {
    if (size.xs) sxWidth.xs = `${(size.xs / 12) * 100}%`
    if (size.sm) sxWidth.sm = `${(size.sm / 12) * 100}%`
    if (size.md) sxWidth.md = `${(size.md / 12) * 100}%`
    if (size.lg) sxWidth.lg = `${(size.lg / 12) * 100}%`
  }

  return (
    <MuiGrid
      {...rest}
      sx={{
        ...rest.sx,
        width: sxWidth,
        flexGrow: 1,
      }}
    >
      {props.children}
    </MuiGrid>
  )
}



// 内部组件，使用useSearchParams
function DashboardContent() {
  // 身份验证检查
  const { userId, isLoaded } = useAuth()
  const searchParams = useSearchParams()

  // 重定向未登录用户
  useEffect(() => {
    if (isLoaded && !userId) {
      redirect('/sign-in')
    }
  }, [isLoaded, userId])

  // 保留selectedMonth状态但默认设置为空，只作为内部数据传递
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null)
  const [selectedArea, setSelectedArea] = useState<string | null>(null)
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [selectedGrid, setSelectedGrid] = useState<string | null>(null)
  const [showGrids, setShowGrids] = useState<boolean>(false)
  const [startDate, setStartDate] = useState<Date | null>(null) // 初始为null，避免竞争条件
  const [endDate, setEndDate] = useState<Date | null>(null) // 初始为null，避免竞争条件
  const [error, setError] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false) // 添加初始化状态

  // 初始化: 从URL参数获取筛选器状态，如果没有则设置默认日期范围
  useEffect(() => {
    if (!isLoaded) return

    const urlFilters = parseUrlParams(searchParams)
    
    // 如果URL中有筛选器参数，使用它们
    if (Object.keys(urlFilters).length > 0) {
      if (urlFilters.selectedMonth) setSelectedMonth(urlFilters.selectedMonth)
      if (urlFilters.selectedGroup) setSelectedGroup(urlFilters.selectedGroup)
      if (urlFilters.selectedArea) setSelectedArea(urlFilters.selectedArea)
      if (urlFilters.selectedCompany) setSelectedCompany(urlFilters.selectedCompany)
      if (urlFilters.selectedProject) setSelectedProject(urlFilters.selectedProject)
      if (urlFilters.selectedGrid) setSelectedGrid(urlFilters.selectedGrid)
      if (urlFilters.showGrids !== undefined) setShowGrids(urlFilters.showGrids)
      if (urlFilters.startDate) setStartDate(urlFilters.startDate)
      if (urlFilters.endDate) setEndDate(urlFilters.endDate)
    } else {
      // 没有URL参数时使用默认值：固定的有数据日期范围：5月21日到5月27日（最新7天）
      const defaultEndDate = new Date('2025-05-27')
      const defaultStartDate = new Date('2025-05-21')
      setStartDate(defaultStartDate)
      setEndDate(defaultEndDate)

      // 如果使用月份筛选，也更新月份（仅用于后端API）
      if (defaultStartDate) {
        setSelectedMonth(format(defaultStartDate, 'yyyy-MM'))
      }
    }
    
    // 标记为已初始化
    setIsInitialized(true)
  }, [isLoaded, searchParams])

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    // 当清除日期范围时，真正清空日期
    setStartDate(start)
    setEndDate(end)
    setSelectedMonth(start ? format(start, 'yyyy-MM') : null)

    // 这里不需要手动触发数据加载，因为 useEffect 会监听 startDate 和 endDate 的变化
  }

  const {
    months,
    groups,
    areas,
    companies,
    projects,
    grids,
    conversationCounts,
    surveyResults,
    projectActivity,
    conversionRates,
    detailedTopicDistribution,
    projectRankings,
    areaRankings,
    companyRankings,
    npsTrend,
    activeProjectsTrend,
    hourlyAnalysis,
    inactiveProjects,
    loadMoreInactiveProjects,
    hasMoreInactiveProjects,
    isLoadingMoreInactive,
    inactiveGrids,
    loadMoreInactiveGrids,
    hasMoreInactiveGrids,
    isLoadingMoreInactiveGrids,
    isLoading,
    error: analyticsError,
    areaRankingsLoading,
    areaRankingsError,
    companyRankingsLoading,
    companyRankingsError,
    topicCategoryTrends,
  } = useAnalyticsData(
    selectedMonth,
    selectedProject,
    selectedGroup,
    selectedArea,
    selectedCompany,
    startDate,
    endDate,
    selectedGrid,
    showGrids
  )

  // 计算对话分析统计数据
  const getConversationStats = () => {
    if (isLoading || analyticsError) return {}

    // 计算对话总数
    const total_conversations = conversationCounts?.reduce((total, item) => total + item.conversation_count, 0) || 0

    // 计算平均消息数
    let avg_messages = 0
    if (conversionRates && conversionRates.length > 0) {
      const totalMessages = conversionRates.reduce((total, item) => total + (item.avg_messages || 0), 0)
      const validDays = conversionRates.filter(item => item.avg_messages > 0).length
      avg_messages = validDays > 0 
        ? Math.round((totalMessages / validDays) * 100) / 100 
        : 0
    }

    // 计算NPS得分
    let nps_score = 0
    let nps_promoters = 0
    let nps_passives = 0 
    let nps_detractors = 0
    if (npsTrend && npsTrend.length > 0) {
      const totalResponses = npsTrend.reduce((total, item) => total + item.total_responses, 0)
      if (totalResponses > 0) {
        const promoters = npsTrend.reduce((total, item) => total + item.promoters, 0)
        const passives = npsTrend.reduce((total, item) => total + item.passives, 0)
        const detractors = npsTrend.reduce((total, item) => total + item.detractors, 0)
        
        nps_promoters = Math.round((promoters / totalResponses) * 100)
        nps_passives = Math.round((passives / totalResponses) * 100)
        nps_detractors = Math.round((detractors / totalResponses) * 100)
        nps_score = nps_promoters - nps_detractors
      }
    }

    // 计算满意度
    let satisfaction_rate = 0
    if (surveyResults && surveyResults.length > 0) {
      const totalSatisfaction = surveyResults.reduce((total, item) => total + (item.satisfaction_rate || 0), 0)
      const validDays = surveyResults.filter(item => item.satisfaction_rate > 0).length
      satisfaction_rate = validDays > 0 
        ? Math.round((totalSatisfaction / validDays) * 100) / 100 
        : 0
    }

    return {
      total_conversations,
      avg_messages,
      nps_score,
      nps_promoters,
      nps_passives,
      nps_detractors,
      satisfaction_rate,
    }
  }

  const conversationStats = getConversationStats()

  // 从已加载的组件数据计算仪表板统计摘要
  const getDashboardSummaryStats = () => {
    // 如果数据正在加载中或出错，返回空对象
    if (isLoading || analyticsError) return {}

    // 计算活跃项目数（累积整个时间段内所有活跃项目并去重）
    let activeProjectsSet = new Set<string>()
    
    // 从项目排名中收集活跃项目
    if (projectRankings && projectRankings.length > 0) {
      projectRankings.forEach(ranking => {
        if (ranking.project) {
          activeProjectsSet.add(ranking.project)
        }
      })
    }
    
    // 从项目活动数据中收集活跃项目
    if (projectActivity && projectActivity.length > 0) {
      projectActivity.forEach(activity => {
        if (activity.project) {
          activeProjectsSet.add(activity.project)
        }
      })
    }
    
    // 从活跃项目趋势中获取累积活跃项目数（最后一天的累积数）
    if (activeProjectsTrend && activeProjectsTrend.length > 0) {
      // 使用最后一天的累积活跃项目数
      const lastDayData = activeProjectsTrend[activeProjectsTrend.length - 1]
      const cumulativeProjects = lastDayData.cumulative_active_projects || lastDayData.active_projects || 0
      
      // 如果之前没有收集到项目，使用累积数据
      if (activeProjectsSet.size === 0 && cumulativeProjects > 0) {
        // 创建虚拟项目集合来匹配累积数量
        activeProjectsSet = new Set(Array(cumulativeProjects).fill(0).map((_, i) => `project_${i}`))
      } else if (cumulativeProjects > activeProjectsSet.size) {
        // 如果累积数量更大，使用累积数量
        activeProjectsSet = new Set(Array(cumulativeProjects).fill(0).map((_, i) => `project_${i}`))
      }
    }
    
    const active_projects = activeProjectsSet.size

    // 计算对话总数（从对话统计中汇总）
    const total_conversations = conversationCounts?.reduce((total, item) => total + item.conversation_count, 0) || 0

    // 计算工单总数（从转化率中获取）
    const total_work_orders = conversionRates?.reduce((total, item) => total + (item.work_order_count || 0), 0) || 0

    // 计算销售线索总数（从转化率中获取）
    const total_leads = conversionRates?.reduce((total, item) => total + (item.lead_count || 0), 0) || 0

    // 计算平均消息数（从转化率中获取）
    let avg_messages = 0
    if (conversionRates && conversionRates.length > 0) {
      const totalMessages = conversionRates.reduce((total, item) => total + (item.avg_messages || 0), 0)
      const validDays = conversionRates.filter(item => item.avg_messages > 0).length
      avg_messages = validDays > 0 
        ? Math.round((totalMessages / validDays) * 100) / 100 
        : 0
    }

    // 计算NPS得分（从NPS趋势中获取平均值）
    let nps_score = 0
    let nps_promoters = 0
    let nps_passives = 0 
    let nps_detractors = 0
    if (npsTrend && npsTrend.length > 0) {
      const totalResponses = npsTrend.reduce((total, item) => total + item.total_responses, 0)
      if (totalResponses > 0) {
        const promoters = npsTrend.reduce((total, item) => total + item.promoters, 0)
        const passives = npsTrend.reduce((total, item) => total + item.passives, 0)
        const detractors = npsTrend.reduce((total, item) => total + item.detractors, 0)
        
        nps_promoters = Math.round((promoters / totalResponses) * 100)
        nps_passives = Math.round((passives / totalResponses) * 100)
        nps_detractors = Math.round((detractors / totalResponses) * 100)
        nps_score = nps_promoters - nps_detractors
      }
    }

    // 网格相关数据
    // 活跃网格数量
    const active_grids = grids?.length || 0
    
    // 只显示网格名称，其他数据暂无
    const active_grids_data = grids?.map(grid => ({
      grid_name: grid.grid_name,
      conversation_count: 0, // 暂无真实数据
      work_order_count: 0,   // 暂无真实数据
      satisfaction_rate: 0    // 暂无真实数据
    })) || []
    
    // 网格趋势暂无真实数据
    const active_grids_trend = activeProjectsTrend?.map(item => ({
      day: item.day || '',
      active_grids: 0 // 暂无真实网格趋势数据
    })) || []

    // 不活跃项目和不活跃网格数据（直接使用已加载的数据）
    const inactive_projects = inactiveProjects || []
    const inactive_grids = inactiveGrids || []
    
    // 用户情绪统计（简化处理）
    const user_emotion_stats: Array<{emotion_category: string, count: number, percentage: number}> = []

    return {
      active_projects,
      total_conversations,
      total_work_orders,
      total_leads,
      avg_messages,
      nps_score,
      nps_promoters,
      nps_passives,
      nps_detractors,
      active_grids,
      active_grids_data,
      active_grids_trend,
      inactive_projects,
      inactive_grids,
      user_emotion_stats,
      inactive_projects_by_area: [],
      inactive_projects_by_company: []
    }
  }

  // 获取仪表板摘要统计数据
  const dashboardStats = getDashboardSummaryStats()

  // 加载中状态
  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    )
  }

  return (
    <DashboardLayout
      months={months}
      groups={groups}
      areas={areas}
      companies={companies}
      projects={projects}
      grids={grids}
      selectedMonth={selectedMonth}
      selectedGroup={selectedGroup}
      selectedArea={selectedArea}
      selectedCompany={selectedCompany}
      selectedProject={selectedProject}
      selectedGrid={selectedGrid}
      showGrids={showGrids}
      startDate={startDate}
      endDate={endDate}
      onMonthChange={setSelectedMonth}
      onGroupChange={setSelectedGroup}
      onAreaChange={setSelectedArea}
      onCompanyChange={setSelectedCompany}
      onProjectChange={setSelectedProject}
      onGridChange={setSelectedGrid}
      onShowGridsChange={setShowGrids}
      onDateRangeChange={handleDateRangeChange}
    >
      <Box width="100%" sx={{ px: { xs: 0, md: 2 } }}>
        {/* 导航栏 */}
        <Card sx={{ mb: 3, p: 2 }}>
          <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
            <Button
              variant="contained"
              color="primary"
              href="/dashboard"
              sx={{ textTransform: 'none' }}
            >
              📊 综合分析
            </Button>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/work-order-analytics', {
                selectedMonth,
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                selectedGrid,
                showGrids,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              📋 工单分析
            </Button>
            <Button
              variant="outlined"
              color="primary"
              href={buildNavigationUrl('/grid-steward-analytics', {
                selectedMonth,
                selectedGroup,
                selectedArea,
                selectedCompany,
                selectedProject,
                selectedGrid,
                showGrids,
                startDate,
                endDate
              })}
              sx={{ textTransform: 'none' }}
            >
              🏠 网格管家分析
            </Button>
            
            {/* 用户信息 - 靠右侧显示 */}
            <Box sx={{ marginLeft: 'auto' }}>
              {userId ? (
                <UserButton 
                  appearance={{
                    elements: {
                      userButtonAvatarBox: {
                        width: 32,
                        height: 32
                      }
                    }
                  }}
                />
              ) : (
                <Button
                  variant="outlined"
                  href="/sign-in"
                  size="small"
                  sx={{ textTransform: 'none' }}
                >
                  登录
                </Button>
              )}
            </Box>
          </Box>
        </Card>

        {/* 主要内容 */}
          {isLoading && (
            <Card
              variant="outlined"
              sx={{ p: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            >
              <Skeleton variant="text" width={180} height={40} sx={{ bgcolor: 'grey.200' }} />
            </Card>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {!isLoading && !error && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {/* Dashboard Stats Summary */}
            {dashboardStats && (
              <Card variant="outlined" sx={{ boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                    仪表板统计摘要
                  </Typography>
                  <MuiGrid container spacing={3}>
                    <Grid size={{ xs: 6, sm: 4, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.green',
                          borderColor: 'chart.green',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          活跃项目
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.green">
                          {dashboardStats.active_projects || 0}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 4, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.purple',
                          borderColor: 'chart.purple',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          对话总数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.purple">
                          {dashboardStats.total_conversations || 0}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 4, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.yellow',
                          borderColor: 'chart.orange',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          工单总数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.orange">
                          {dashboardStats.total_work_orders || 0}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 4, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.red',
                          borderColor: 'chart.red',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          销售线索总数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.red">
                          {dashboardStats.total_leads || 0}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 4, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.purple',
                          borderColor: 'chart.purple',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          平均消息数
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.purple">
                          {dashboardStats.avg_messages || 0}
                        </Typography>
                      </Card>
                    </Grid>
                  </MuiGrid>
                </CardContent>
              </Card>
            )}

            {/* Active Projects Trend Analysis */}
            <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                  活跃项目趋势分析
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={2}>
                  {startDate && endDate
                    ? `${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`
                    : '全部时间范围'}
                </Typography>

                <LineChart
                  data={activeProjectsTrend || []}
                  xDataKey="day"
                  yDataKeys={[
                    { key: 'active_projects', name: '当日活跃项目数', color: '#3498db' },
                    { key: 'cumulative_active_projects', name: '累积活跃项目数', color: '#e74c3c' }
                  ]}
                  title="活跃项目数趋势"
                  fileName="active-projects-trend"
                />
              </CardContent>
            </Card>

            {/* NPS Analysis - Add if NPS data is available */}
            {dashboardStats?.nps_score !== undefined && (
              <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 2 } }}>
                  <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                    NPS分析
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    NPS (净推荐值) = 推荐者百分比 -
                    批评者百分比。推荐者为&apos;很满意&apos;，批评者为&apos;不满&apos;和&apos;很不满&apos;，中立者为&apos;满意&apos;和&apos;一般&apos;。
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    NPS（净推荐值）从-100至100，高于0为良好，高于50为优秀。推荐者为&quot;很满意&quot;客户，批评者为&quot;不满&quot;和&quot;很不满&quot;客户。
                  </Typography>
                  <MuiGrid container spacing={3} mt={1}>
                    <Grid size={{ xs: 6, sm: 3, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.blue',
                          borderColor: 'chart.blue',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          NPS总分
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.blue">
                          {dashboardStats.nps_score || 0}
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.green',
                          borderColor: 'chart.green',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          推荐者占比
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.green">
                          {dashboardStats.nps_promoters || 0}%
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.yellow',
                          borderColor: 'chart.yellow',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          中立者占比
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.yellow">
                          {dashboardStats.nps_passives || 0}%
                        </Typography>
                      </Card>
                    </Grid>

                    <Grid size={{ xs: 6, sm: 3, md: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          p: 2,
                          bgcolor: 'customCard.red',
                          borderColor: 'chart.red',
                          transition: 'transform 0.3s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 3,
                          },
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          批评者占比
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color="chart.red">
                          {dashboardStats.nps_detractors || 0}%
                        </Typography>
                      </Card>
                    </Grid>
                  </MuiGrid>
                </CardContent>
              </Card>
            )}

            {/* NPS Trend Chart */}
            <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                  NPS趋势分析
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={2}>
                  {startDate && endDate
                    ? `${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`
                    : '全部时间范围'}
                </Typography>
                <NPSChart data={npsTrend} title="" xAxisKey="day" fileName="nps-trend" />
              </CardContent>
            </Card>

            {/* 24小时分析 */}
            <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                  24小时对话分析
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={2}>
                  {startDate && endDate
                    ? `${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`
                    : '全部时间范围'} 按小时统计
                </Typography>
                
                <MuiGrid container spacing={3}>
                  {/* 时段分布饼图 */}
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <PieChart
                          data={(() => {
                            const periods = [
                              { name: '早高峰 (7-9点)', hours: [7, 8] },
                              { name: '上午 (9-12点)', hours: [9, 10, 11] },
                              { name: '午休 (12-14点)', hours: [12, 13] },
                              { name: '下午 (14-18点)', hours: [14, 15, 16, 17] },
                              { name: '晚高峰 (18-20点)', hours: [18, 19] },
                              { name: '夜间 (20-7点)', hours: [20, 21, 22, 23, 0, 1, 2, 3, 4, 5, 6] }
                            ]
                            
                            return periods.map(period => ({
                              name: period.name,
                              value: period.hours.reduce((sum, hour) => {
                                const hourData = hourlyAnalysis?.find(h => h.hour_num === hour)
                                return sum + (hourData?.conversation_count || 0)
                              }, 0)
                            })).filter(item => item.value > 0)
                          })()}
                          dataKey="value"
                          nameKey="name"
                          title="时段分布"
                          fileName="time-period-distribution"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 24小时热力图风格的柱状图 */}
                  <Grid size={{ xs: 12, md: 8 }}>
                    <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <BarChart
                          data={hourlyAnalysis || []}
                          xDataKey="hour"
                          yDataKeys={[{ key: 'conversation_count', name: '对话数量', color: '#3498db' }]}
                          title="24小时对话热力分布"
                          fileName="hourly-conversation-heatmap"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 服务质量雷达图（用满意度和转化率） */}
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={hourlyAnalysis || []}
                          xDataKey="hour"
                          yDataKeys={[
                            { key: 'satisfaction_rate', name: '满意度(%)', color: '#2ecc71' },
                            { key: 'work_order_rate', name: '工单转化率(%)', color: '#e67e22' }
                          ]}
                          title="服务质量趋势"
                          fileName="service-quality-trend"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 高峰时段对比 */}
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card variant="outlined" sx={{ height: '400px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <PieChart
                          data={(() => {
                            const peakHours = [8, 9, 10, 18, 19, 20] // 定义高峰时段
                            const peakConversations = hourlyAnalysis?.filter(h => peakHours.includes(h.hour_num))
                              .reduce((sum, h) => sum + h.conversation_count, 0) || 0
                            const totalConversations = hourlyAnalysis?.reduce((sum, h) => sum + h.conversation_count, 0) || 0
                            const offPeakConversations = totalConversations - peakConversations
                            
                            return [
                              { name: '高峰时段', value: peakConversations },
                              { name: '非高峰时段', value: offPeakConversations }
                            ].filter(item => item.value > 0)
                          })()}
                          dataKey="value"
                          nameKey="name"
                          title="高峰时段对比"
                          fileName="peak-hours-comparison"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 24小时详细数据表 */}
                  <Grid size={{ xs: 12 }}>
                    <Card variant="outlined" sx={{ height: 'auto', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <DataTable
                          data={hourlyAnalysis || []}
                          columns={[
                            { key: 'hour', header: '时段' },
                            { key: 'conversation_count', header: '对话数' },
                            { key: 'work_order_count', header: '工单数' },
                            { key: 'lead_count', header: '商机数' },
                            { key: 'work_order_rate', header: '工单转化率(%)' },
                            { key: 'satisfaction_rate', header: '满意度(%)' },
                          ]}
                          title="24小时详细数据"
                          fileName="hourly-detailed-data"
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </MuiGrid>
              </CardContent>
            </Card>

            {/* Project Rankings */}
            {!selectedProject && projectRankings.length > 0 && (
              <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <ProjectRankingTable
                    data={projectRankings}
                    title="项目表现排名"
                    fileName="project-rankings"
                  />
                </CardContent>
              </Card>
            )}

            {/* Area Rankings (only for 万科物业) */}
            {selectedGroup === '万科物业' && !selectedArea && (
              <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <AreaRankingTable
                    data={areaRankings}
                    title="区域表现排名"
                    fileName="area-rankings"
                    isLoading={areaRankingsLoading}
                    error={areaRankingsError}
                  />
                </CardContent>
              </Card>
            )}

            {/* Company Rankings (only for 万科物业) */}
            {selectedGroup === '万科物业' && !selectedCompany && (
              <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <CompanyRankingTable
                    data={companyRankings}
                    title="项目公司表现排名"
                    fileName="company-rankings"
                    isLoading={companyRankingsLoading}
                    error={companyRankingsError}
                  />
                </CardContent>
              </Card>
            )}

            {/* Row 1: 对话数量月度趋势 and 用户满意度趋势 */}
            <MuiGrid container spacing={3} sx={{ mt: 3 }}>
              {/* Conversation Count Trend */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={conversationCounts || []}
                      xDataKey="day"
                      yDataKeys={[{ key: 'conversation_count', name: '对话数', color: '#3498db' }]}
                      title="对话数趋势"
                      fileName="conversation-count-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Satisfaction Rate Trend */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={surveyResults || []}
                      xDataKey="day"
                      yDataKeys={[
                        { key: 'satisfaction_rate', name: '满意度(%)', color: '#2ecc71' },
                      ]}
                      title="满意度趋势"
                      fileName="satisfaction-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>

            {/* Row 2: 转化率分析 and 对话平均消息数 */}
            <MuiGrid container spacing={3} sx={{ mt: 3 }}>
              {/* Conversion Rates - Work Order */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={conversionRates || []}
                      xDataKey="day"
                      yDataKeys={[
                        { key: 'work_order_rate', name: '工单转化率(%)', color: '#e67e22' },
                      ]}
                      title="工单转化率趋势"
                      fileName="work-order-conversion-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Conversion Rates - Lead */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <LineChart
                      data={conversionRates || []}
                      xDataKey="day"
                      yDataKeys={[{ key: 'lead_rate', name: '商机转化率(%)', color: '#e74c3c' }]}
                      title="商机转化率趋势"
                      fileName="lead-conversion-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>

            {/* Average Messages row */}
            <MuiGrid container spacing={3} sx={{ mt: 3 }}>
              {/* Average Messages */}
              <Grid size={{ xs: 12 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <BarChart
                      data={conversionRates || []}
                      xDataKey="day"
                      yDataKeys={[{ key: 'avg_messages', name: '平均对话轮次', color: '#3498db' }]}
                      title="平均对话轮次趋势"
                      fileName="avg-messages-trend"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>

            {/* Row 4: 对话数量详情 and 满意度调查结果详情 */}
            <MuiGrid container spacing={3} sx={{ mt: 3 }}>
              {/* Conversation Data Table */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <DataTable
                      data={conversationCounts}
                      columns={[
                        { key: 'day', header: '日期' },
                        { key: 'conversation_count', header: '对话数量' },
                      ]}
                      title="对话数量详情"
                      fileName="conversation-data"
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Survey Results Table */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Card
                  variant="outlined"
                  sx={{ height: '450px', boxShadow: 1, display: 'flex', flexDirection: 'column' }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <DataTable
                      data={surveyResults}
                      columns={[
                        { key: 'day', header: '日期' },
                        { key: 'total_conversations', header: '总对话数' },
                        { key: 'total_survey', header: '总评价数' },
                        { key: 'very_satisfied', header: '很满意' },
                        { key: 'satisfied', header: '满意' },
                        { key: 'neutral', header: '一般' },
                        { key: 'unsatisfied', header: '不满' },
                        { key: 'very_unsatisfied', header: '很不满' },
                        { key: 'satisfaction_rate', header: '满意率 (%)' },
                      ]}
                      title="满意度调查结果详情"
                      fileName="survey-results"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </MuiGrid>

            {/* Detailed Topic Distribution Table - Full Width */}
            <Card variant="outlined" sx={{ mt: 3, width: '100%', boxShadow: 1 }}>
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <DetailedTopicTable
                  data={detailedTopicDistribution}
                  title="问题分类详细分析"
                  fileName="topic-distribution"
                />
              </CardContent>
            </Card>

            {/* Category Project Rankings - Full Width */}
            <CategoryProjectRanking
              startDate={startDate}
              endDate={endDate}
              selectedGroup={selectedGroup}
              selectedArea={selectedArea}
              selectedCompany={selectedCompany}
            />

            {/* Topic Category Trends - Full Width */}
            <Card variant="outlined" sx={{ mt: 3, width: '100%', boxShadow: 1 }}>
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                  问题类别趋势分析
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={2}>
                  {startDate && endDate
                    ? `${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`
                    : '全部时间范围'}
                </Typography>
                <MuiGrid container spacing={3}>
                  {/* 基础设施类问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.基础设施类 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '基础设施类问题', color: '#3498db' }]}
                          title="基础设施类问题趋势"
                          fileName="infrastructure-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 物业服务类问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.物业服务类 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '物业服务类问题', color: '#27ae60' }]}
                          title="物业服务类问题趋势"
                          fileName="property-service-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 维修类服务问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.维修类服务 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '维修类服务问题', color: '#e74c3c' }]}
                          title="维修类服务问题趋势"
                          fileName="repair-service-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 车辆与出入类问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.车辆与出入类 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '车辆与出入类问题', color: '#9b59b6' }]}
                          title="车辆与出入类问题趋势"
                          fileName="vehicle-access-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 社区服务类问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.社区服务类 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '社区服务类问题', color: '#f39c12' }]}
                          title="社区服务类问题趋势"
                          fileName="community-service-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 一般交互类问题趋势 */}
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Card variant="outlined" sx={{ height: '350px', boxShadow: 1 }}>
                      <CardContent sx={{ p: 2 }}>
                        <LineChart
                          data={topicCategoryTrends?.一般交互类 || []}
                          xDataKey="day"
                          yDataKeys={[{ key: 'total', name: '一般交互类问题', color: '#16a085' }]}
                          title="一般交互类问题趋势"
                          fileName="general-interaction-issues"
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </MuiGrid>
              </CardContent>
            </Card>

            {/* Add InactiveProjectList for non-grid mode */}
            {!showGrids && (
              <MuiGrid container spacing={3} sx={{ mt: 3 }}>
                <Grid size={{ xs: 12 }}>
                  <InactiveProjectList
                    data={inactiveProjects || []}
                    totalCount={inactiveProjects?.[0]?.total_count || 0}
                    isLoading={isLoading}
                    hasMore={hasMoreInactiveProjects}
                    onLoadMore={loadMoreInactiveProjects}
                    isLoadingMore={isLoadingMoreInactive}
                  />
                </Grid>
              </MuiGrid>
            )}

            {/* Display grid data when toggle is enabled regardless of project/company selection */}
            {showGrids && dashboardStats && dashboardStats.active_grids_data && dashboardStats.active_grids_data.length > 0 && (
              <Card variant="outlined" sx={{ mt: 3, boxShadow: 1 }}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography variant="h5" fontWeight="600" mb={3} textAlign="center">
                    网格统计分析
                  </Typography>
                  <Typography variant="body2" color="text.secondary" mb={3}>
                    活跃网格数量: {dashboardStats?.active_grids || 0}
                  </Typography>
                  
                  {/* Grid Activity Trend */}
                  {dashboardStats.active_grids_trend && dashboardStats.active_grids_trend.length > 0 && (
                    <Box mt={4}>
                      <Typography variant="h6" gutterBottom>
                        网格活跃度趋势
                      </Typography>
                      <LineChart
                        data={dashboardStats.active_grids_trend}
                        xDataKey="day"
                        yDataKeys={[{ key: 'active_grids', name: '活跃网格数', color: '#e74c3c' }]}
                        title="活跃网格数趋势"
                        fileName="active-grids-trend"
                      />
                    </Box>
                  )}
                  
                  {/* Grid Statistics */}
                  {dashboardStats.active_grids_data && dashboardStats.active_grids_data.length > 0 && (
                    <Box mt={4}>
                      <Typography variant="h6" gutterBottom>
                        网格统计
                      </Typography>
                      <TableContainer component={Paper}>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>网格名称</TableCell>
                              <TableCell align="right">对话数</TableCell>
                              <TableCell align="right">工单数</TableCell>
                              <TableCell align="right">满意度</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dashboardStats.active_grids_data.slice(0, 10).map((grid) => (
                              <TableRow key={grid.grid_name}>
                                <TableCell>{grid.grid_name}</TableCell>
                                <TableCell align="right">{grid.conversation_count}</TableCell>
                                <TableCell align="right">{grid.work_order_count}</TableCell>
                                <TableCell align="right">{grid.satisfaction_rate}%</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 高级网格管家分析系统 */}
            <GridStewardAnalyticsSection 
              startDate={startDate}
              endDate={endDate}
              selectedGroup={selectedGroup}
              selectedArea={selectedArea}
              selectedCompany={selectedCompany}
              selectedProject={selectedProject}
            />


          </Box>
        )}
      </Box>
    </DashboardLayout>
  )
}

// 主组件，用Suspense包装DashboardContent
export default function DashboardPage() {
  return (
    <Suspense fallback={
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Skeleton variant="circular" width={40} height={40} />
      </Box>
    }>
      <DashboardContent />
    </Suspense>
  )
}
