import { NextResponse } from 'next/server'
import { cacheManager } from '../../../../lib/cache'

export async function POST() {
  try {
    console.log('🔄 收到强制刷新缓存请求')
    
    // 强制刷新缓存
    await cacheManager.refresh()
    
    // 获取刷新后的状态
    const status = cacheManager.getStatus()
    
    console.log('✅ 缓存刷新完成')
    
    return NextResponse.json({
      success: true,
      message: '缓存已成功刷新',
      status: {
        isInitialized: status.isInitialized,
        lastUpdated: status.lastUpdated,
        dataCount: status.dataCount
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ 缓存刷新失败:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: '缓存刷新失败',
        details: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  // GET请求也支持刷新，方便测试
  return POST()
} 