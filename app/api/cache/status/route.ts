import { NextResponse } from 'next/server'
import { getCacheStats } from '../../../../lib/cache'

export async function GET() {
  try {
    const status = getCacheStats()
    
    return NextResponse.json({
      success: true,
      data: status,
      isInitialized: status.isInitialized,
      lastUpdated: status.lastUpdated,
      dataCount: status.dataCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('获取缓存状态失败:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: '获取缓存状态失败',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
} 