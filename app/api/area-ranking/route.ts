import { NextResponse } from 'next/server'
import { getConnection } from '../../../lib/mysql-db'

// 执行MySQL查询的辅助函数
async function executeQuery(query: string, params: any[] = []): Promise<any[]> {
  const connection = await getConnection()
  try {
    const [rows] = await connection.execute(query, params)
    return rows as any[]
  } finally {
    connection.release()
  }
}

export async function GET(request: Request) {
  try {
    console.log('📊 Area-ranking API - 直接MySQL查询模式')
    
    const { searchParams } = new URL(request.url)
    const month = searchParams.get('month')
    const group = searchParams.get('group')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // 构建查询条件
    let whereConditions = ['cm.payload IS NOT NULL']
    const params: any[] = []
    
    // 时间过滤
    if (startDate && endDate) {
      whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ? AND FROM_UNIXTIME(cm.timestamp/1000) <= ?')
      params.push(startDate, `${endDate} 23:59:59`)
    } else if (month) {
      whereConditions.push('DATE_FORMAT(FROM_UNIXTIME(cm.timestamp/1000), "%Y-%m") = ?')
      params.push(month)
    }
    
    const whereClause = whereConditions.length > 1 ? 'WHERE ' + whereConditions.join(' AND ') : ''
    
    // 构建区域排名查询
    const query = `
      SELECT 
        cl.project_name AS area,
        COUNT(*) AS conversation_count,
        ROUND(AVG(\n          CASE WHEN JSON_VALID(cm.payload) THEN JSON_LENGTH(JSON_EXTRACT(cm.payload, '$.messages')) ELSE NULL END\n        ), 1) AS avg_messages,
        ROUND(\n          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) IN ('很满意', '满意') THEN 1 ELSE 0 END) * 100.0 / \n          NULLIF(SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) IN ('很满意', '满意', '一般', '不满', '很不满') THEN 1 ELSE 0 END), 0),\n          1\n        ) AS satisfaction_rate,
        ROUND(\n          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.work_order')) = 'true' THEN 1 ELSE 0 END) * 100.0 / \n          NULLIF(COUNT(*), 0),\n          1\n        ) AS work_order_rate,
        ROUND(\n          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.lead')) = 'true' THEN 1 ELSE 0 END) * 100.0 / \n          NULLIF(COUNT(*), 0),\n          1\n        ) AS lead_rate
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      GROUP BY cl.project_name
      HAVING COUNT(*) >= 5
      ORDER BY conversation_count DESC
      LIMIT 50
    `
    
    console.log('🔍 执行区域排名查询')
    const data = await executeQuery(query, params)
    
    // 添加排名信息
    const rankedData = data.map((item, index) => ({
      ...item,
      volume_rank: index + 1,
      satisfaction_rank: 0, // 可以后续计算
      work_order_rank: 0,
      lead_rank: 0,
      efficiency_rank: 0
    }))
    
    console.log(`✅ 返回 ${rankedData.length} 个区域的排名数据`)
    
    return NextResponse.json({
      data: rankedData,
      cached: false,
      source: 'mysql',
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    console.error('❌ Area-ranking API错误:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch area ranking data',
        details: error.message,
        cached: false,
        source: 'mysql'
      },
      { status: 500 }
    )
  }
}
