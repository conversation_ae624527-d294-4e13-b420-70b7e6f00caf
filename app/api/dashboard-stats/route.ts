import { NextResponse } from 'next/server'
import { getDashboardStats, QueryFilters } from '../../../lib/mysql-queries'

export async function GET(request: Request) {
  try {
    console.log('📊 Dashboard API - 直接MySQL查询模式')
    
    const { searchParams } = new URL(request.url)
    
    // 提取查询参数
    const filters: QueryFilters = {}
    
    if (searchParams.get('startDate')) {
      filters.startDate = searchParams.get('startDate')!
    }
    
    if (searchParams.get('endDate')) {
      filters.endDate = searchParams.get('endDate')!
    }
    
    if (searchParams.get('project')) {
      filters.projectId = searchParams.get('project')!
    }
    
    if (searchParams.get('month')) {
      filters.month = searchParams.get('month')!
    }
    
    if (searchParams.get('grid')) {
      filters.grid = searchParams.get('grid')!
    }
    
    if (searchParams.get('survey')) {
      filters.survey = searchParams.get('survey')!
    }
    
    console.log('🔍 查询过滤器:', filters)
    
    // 直接从MySQL获取统计数据
    const stats = await getDashboardStats(filters)
    
    console.log('✅ Dashboard统计查询完成')
    
    return NextResponse.json({ 
      data: stats,
      cached: false,
      source: 'mysql',
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ Dashboard API错误:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error',
        source: 'mysql'
      }, 
      { status: 500 }
    )
  }
}
