import { NextRequest, NextResponse } from 'next/server'
import { hotDataCache } from '../../../lib/hot-data-cache'

/**
 * 热数据缓存管理API端点
 * 第三阶段：提供热数据识别、预加载和监控功能
 */

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')
  
  try {
    switch (action) {
      case 'stats':
        return await getHotDataStats()
      
      case 'hot_queries':
        return await getHotQueries()
      
      case 'preload':
        return await preloadHotData()
      
      case 'cleanup':
        return await cleanupExpiredCache()
      
      default:
        return NextResponse.json({
          success: false,
          error: '无效的操作参数',
          available_actions: ['stats', 'hot_queries', 'preload', 'cleanup']
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ 热缓存API错误:', error)
    return NextResponse.json({
      success: false,
      error: '热缓存操作失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取热数据统计
 */
async function getHotDataStats() {
  console.log('📈 获取热数据统计...')
  
  const stats = await hotDataCache.getHotDataStats()
  
  return NextResponse.json({
    success: true,
    data: {
      timestamp: new Date().toISOString(),
      ...stats
    }
  })
}

/**
 * 获取热查询列表
 */
async function getHotQueries() {
  console.log('🔥 获取热查询列表...')
  
  const hotQueries = await hotDataCache.getHotQueries()
  
  return NextResponse.json({
    success: true,
    data: {
      timestamp: new Date().toISOString(),
      hot_queries: hotQueries,
      total_count: hotQueries.length
    }
  })
}

/**
 * 预加载热数据
 */
async function preloadHotData() {
  console.log('🚀 开始预加载热数据...')
  
  const startTime = Date.now()
  
  try {
    await hotDataCache.preloadHotData()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '热数据预加载完成',
      duration_ms: duration
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '热数据预加载失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 清理过期缓存
 */
async function cleanupExpiredCache() {
  console.log('🧹 清理过期热缓存...')
  
  const startTime = Date.now()
  
  try {
    await hotDataCache.cleanupExpiredHotCache()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '过期缓存清理完成',
      duration_ms: duration
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '过期缓存清理失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 