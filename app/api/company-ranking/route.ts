import { NextResponse } from 'next/server'
import { getConnection } from '../../../lib/mysql-db'

// 执行MySQL查询的辅助函数
async function executeQuery(query: string, params: any[] = []): Promise<any[]> {
  const connection = await getConnection()
  try {
    const [rows] = await connection.execute(query, params)
    return rows as any[]
  } finally {
    connection.release()
  }
}

export async function GET(request: Request) {
  try {
    console.log('📊 Company-ranking API - 直接MySQL查询模式')
    
    const { searchParams } = new URL(request.url)
    const month = searchParams.get('month')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // 构建查询条件
    let whereConditions = ['cm.payload IS NOT NULL']
    const params: any[] = []
    
    // 时间过滤
    if (startDate && endDate) {
      whereConditions.push('FROM_UNIXTIME(cm.timestamp/1000) >= ? AND FROM_UNIXTIME(cm.timestamp/1000) <= ?')
      params.push(startDate, `${endDate} 23:59:59`)
    } else if (month) {
      whereConditions.push('DATE_FORMAT(FROM_UNIXTIME(cm.timestamp/1000), "%Y-%m") = ?')
      params.push(month)
    }
    
    const whereClause = whereConditions.length > 1 ? 'WHERE ' + whereConditions.join(' AND ') : ''
    
    // 构建公司排名查询（基于项目名称作为公司标识）
    const query = `
      SELECT 
        SUBSTRING_INDEX(cl.project_name, '-', 1) AS company,
        COUNT(*) AS conversation_count,
        ROUND(AVG(
          CASE WHEN JSON_VALID(cm.payload) THEN JSON_LENGTH(JSON_EXTRACT(cm.payload, '$.messages')) ELSE NULL END
        ), 1) AS avg_messages,
        ROUND(
          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) IN ('很满意', '满意') THEN 1 ELSE 0 END) * 100.0 / 
          NULLIF(SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.survey')) IN ('很满意', '满意', '一般', '不满', '很不满') THEN 1 ELSE 0 END), 0),
          1
        ) AS satisfaction_rate,
        ROUND(
          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.work_order')) = 'true' THEN 1 ELSE 0 END) * 100.0 / 
          NULLIF(COUNT(*), 0),
          1
        ) AS work_order_rate,
        ROUND(
          SUM(CASE WHEN JSON_VALID(cm.payload) AND JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.lead')) = 'true' THEN 1 ELSE 0 END) * 100.0 / 
          NULLIF(COUNT(*), 0),
          1
        ) AS lead_rate,
        COUNT(DISTINCT cl.project_code) AS project_count
      FROM chat_msg cm
      LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
      LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
      ${whereClause}
      GROUP BY SUBSTRING_INDEX(cl.project_name, '-', 1)
      HAVING COUNT(*) >= 10
      ORDER BY conversation_count DESC
      LIMIT 30
    `
    
    console.log('🔍 执行公司排名查询')
    const data = await executeQuery(query, params)
    
    // 添加排名信息
    const rankedData = data.map((item, index) => ({
      ...item,
      volume_rank: index + 1,
      satisfaction_rank: 0,
      work_order_rank: 0,
      lead_rank: 0,
      efficiency_rank: 0
    }))
    
    console.log(`✅ 返回 ${rankedData.length} 个公司的排名数据`)
    
    return NextResponse.json({
      data: rankedData,
      cached: false,
      source: 'mysql',
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    console.error('❌ Company-ranking API错误:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch company ranking data',
        details: error.message,
        cached: false,
        source: 'mysql'
      },
      { status: 500 }
    )
  }
}
