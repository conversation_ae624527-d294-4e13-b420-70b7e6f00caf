import { NextResponse } from 'next/server'
import { getConversationStatsData, ConversationStatsFilters } from '../../../lib/mysql-conversation-stats'

export async function GET(request: Request) {
  try {
    console.log('📊 Conversation Stats API - 直接MySQL查询模式')
    
    const { searchParams } = new URL(request.url)
    
    // 提取查询参数
    const type = searchParams.get('type') || 'conversation_count'
    const filters: ConversationStatsFilters = {}
    
    if (searchParams.get('startDate')) {
      filters.startDate = searchParams.get('startDate')!
    }
    
    if (searchParams.get('endDate')) {
      filters.endDate = searchParams.get('endDate')!
    }
    
    if (searchParams.get('project')) {
      filters.projectId = searchParams.get('project')!
    }
    
    if (searchParams.get('month')) {
      filters.month = searchParams.get('month')!
    }
    
    if (searchParams.get('grid')) {
      filters.grid = searchParams.get('grid')!
    }
    
    if (searchParams.get('survey')) {
      filters.survey = searchParams.get('survey')!
    }
    
    if (searchParams.get('data_format')) {
      filters.data_format = searchParams.get('data_format')!
    }
    
    if (searchParams.get('category')) {
      filters.category = searchParams.get('category')!
    }
    
    console.log(`🔍 查询类型: ${type}`, filters)
    
    // 直接从MySQL获取对话统计数据
    const data = await getConversationStatsData(type, filters)
    
    console.log(`✅ 对话统计查询完成: ${type}, 返回 ${data.length} 条记录`)
    
    return NextResponse.json({ 
      data,
      cached: false,
      source: 'mysql',
      type,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ Conversation Stats API错误:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error',
        source: 'mysql'
      }, 
      { status: 500 }
    )
  }
}
