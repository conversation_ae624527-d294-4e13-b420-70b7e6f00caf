// 修复万科物业筛选问题的补丁
// 问题：当使用JOIN筛选时，可能丢失conversation表中存在但project表中缺失的记录

// 原来的筛选逻辑（可能丢失数据）:
const originalFilterLogic = `
// 需要按group/area/company筛选时才添加JOIN
if (group || area || company) {
  joinClause = ' JOIN project ON conversation.project = project.project'
  if (group) groupFilter += \` AND project."group" = '\${group}'\`
  if (area) groupFilter += \` AND project.area = '\${area}'\`
  if (company) groupFilter += \` AND project.company = '\${company}'\`
}
`;

// 修复后的筛选逻辑（使用LEFT JOIN + 子查询，不丢失数据）:
const fixedFilterLogic = `
// 修复：使用LEFT JOIN避免丢失数据，同时用子查询确保筛选精确
let joinClause = ''
let groupFilter = ''
let projectFilterSubquery = ''

if (group || area || company) {
  // 使用子查询而不是JOIN，避免丢失conversation表中的数据
  const projectConditions = []
  if (group) projectConditions.push(\`p."group" = '\${group}'\`)
  if (area) projectConditions.push(\`p.area = '\${area}'\`)
  if (company) projectConditions.push(\`p.company = '\${company}'\`)
  
  projectFilterSubquery = \`
    AND conversation.project IN (
      SELECT p.project 
      FROM project p 
      WHERE \${projectConditions.join(' AND ')}
    )\`
}
`;

// 应用到各个查询中：
const exampleFixedQuery = `
// 原查询
query = \`
  SELECT 
    TO_CHAR(conversation_created, 'YYYY-MM-DD') AS day,
    COUNT(*) AS conversation_count
  FROM conversation\${joinClause}
  WHERE \${filters_sql}\${groupFilter}\${gridFilter}
  GROUP BY TO_CHAR(conversation_created, 'YYYY-MM-DD')
  ORDER BY day
\`

// 修复后的查询
query = \`
  SELECT 
    TO_CHAR(conversation_created, 'YYYY-MM-DD') AS day,
    COUNT(*) AS conversation_count
  FROM conversation
  WHERE \${filters_sql}\${projectFilterSubquery}\${gridFilter}
  GROUP BY TO_CHAR(conversation_created, 'YYYY-MM-DD')
  ORDER BY day
\`
`;

// 需要修改的文件位置：
const filesToModify = [
  'app/api/conversation-stats/route.ts',
  'app/api/dashboard-stats/route.ts',
  'app/api/area-ranking/route.ts',
  'app/api/company-ranking/route.ts'
];

// 具体修改内容：
export const fixes = {
  // 1. 修复conversation-stats API的筛选逻辑
  conversationStatsFilter: {
    location: 'app/api/conversation-stats/route.ts',
    issue: '使用JOIN可能丢失万科物业对话数据',
    solution: '改用子查询筛选，保留所有conversation记录'
  },
  
  // 2. 修复缓存查询逻辑
  cacheQueryFilter: {
    location: 'lib/cached-queries.ts',
    issue: '缓存筛选可能不准确',
    solution: '确保缓存筛选逻辑与API一致'
  },
  
  // 3. 增加数据质量检查
  dataQualityCheck: {
    location: '新增数据质量检查API',
    issue: '缺乏数据质量监控',
    solution: '添加孤立数据检测和修复机制'
  }
};

// 测试用例：确保修复后万科物业数据不丢失
export const testCases = [
  {
    name: '万科物业总对话数',
    description: '检查万科物业筛选后的对话总数是否合理',
    expectedBehavior: '应该返回较大的数值，不应该接近0'
  },
  {
    name: '万科物业项目列表',
    description: '检查万科物业下的项目数量',
    expectedBehavior: '应该返回多个项目，不应该为空'
  },
  {
    name: '数据占比检查',
    description: '万科物业对话占总对话的比例',
    expectedBehavior: '应该占有合理的比例（如10%以上）'
  }
]; 