import { NextRequest, NextResponse } from 'next/server'
import { 
  getLeadConversionAnalysis,
  getLeadProfiles,
  getLeadSourceAnalysis,
  getLeadQualityScore,
  getLeadTrendAnalysis,
  LeadAnalysisFilters
} from '../../../lib/lead-analysis'

export async function GET(request: NextRequest) {
  console.log('🔍 Lead Analytics API - 线索分析查询')
  
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'conversion'
    
    // 构建过滤器
    const filters: LeadAnalysisFilters = {
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
      projectId: searchParams.get('projectId') || undefined,
      grid: searchParams.get('grid') || undefined,
      leadType: searchParams.get('leadType') || undefined
    }
    
    console.log(`🔍 查询类型: ${type}`, filters)
    
    let data;
    
    switch (type) {
      case 'conversion':
        data = await getLeadConversionAnalysis(filters)
        console.log(`✅ 线索转化分析完成，返回 ${data.length} 条记录`)
        break
        
      case 'profiles':
        data = await getLeadProfiles(filters)
        console.log(`✅ 线索档案查询完成，返回 ${data.length} 条记录`)
        break
        
      case 'sources':
        data = await getLeadSourceAnalysis(filters)
        console.log(`✅ 线索来源分析完成，返回 ${data.length} 条记录`)
        break
        
      case 'quality':
        data = await getLeadQualityScore(filters)
        console.log(`✅ 线索质量评分完成，总体评分 ${data.overall_score}`)
        break
        
      case 'trends':
        data = await getLeadTrendAnalysis(filters)
        console.log(`✅ 线索趋势分析完成，增长率 ${data.growth_rate}%`)
        break
        
      default:
        console.warn(`⚠️ 未知的查询类型: ${type}`)
        return NextResponse.json(
          { error: '未知的查询类型', type },
          { status: 400 }
        )
    }
    
    return NextResponse.json({
      success: true,
      data,
      type,
      filters,
      timestamp: new Date().toISOString(),
      source: 'mysql'
    })
    
  } catch (error: any) {
    console.error('❌ Lead Analytics API错误:', error)
    
    return NextResponse.json(
      { 
        error: 'Lead Analytics查询失败', 
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
} 