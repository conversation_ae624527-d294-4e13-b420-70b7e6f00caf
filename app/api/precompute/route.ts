import { NextRequest, NextResponse } from 'next/server'
import { precomputedCache } from '../../../lib/precomputed-cache'

/**
 * 预计算任务API端点
 * 第二阶段：提供手动触发和监控预计算任务的接口
 */

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')
  
  try {
    switch (action) {
      case 'status':
        return await getPrecomputeStatus()
      
      case 'run_all':
        return await runAllTasks()
      
      case 'run_daily':
        return await runDailySummary()
      
      case 'run_ranking':
        return await runProjectRanking()
      
      case 'run_hourly':
        return await runHourlyAnalysis()
      
      case 'run_conversion':
        return await runConversionTrends()
      
      default:
        return NextResponse.json({
          success: false,
          error: '无效的操作参数',
          available_actions: [
            'status', 'run_all', 'run_daily', 'run_ranking', 
            'run_hourly', 'run_conversion'
          ]
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ 预计算API错误:', error)
    return NextResponse.json({
      success: false,
      error: '预计算任务执行失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 获取预计算状态
 */
async function getPrecomputeStatus() {
  console.log('🔍 获取预计算状态...')
  
  const status = {
    timestamp: new Date().toISOString(),
    tasks: {
      daily_summary: await checkTaskStatus('daily_summary'),
      project_ranking: await checkTaskStatus('project_ranking_summary'),
      hourly_analysis: await checkTaskStatus('hourly_summary'),
      conversion_trends: await checkTaskStatus('conversion_trends')
    },
    system: {
      redis_connected: await checkRedisConnection(),
      mysql_connected: await checkMysqlConnection()
    }
  }
  
  return NextResponse.json({
    success: true,
    data: status
  })
}

/**
 * 运行所有预计算任务
 */
async function runAllTasks() {
  console.log('🚀 开始执行所有预计算任务...')
  
  const startTime = Date.now()
  
  try {
    await precomputedCache.runAllPrecomputeTasks()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '所有预计算任务执行完成',
      duration_ms: duration,
      tasks_completed: ['daily_summary', 'project_ranking', 'hourly_analysis', 'conversion_trends']
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '预计算任务执行失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 运行每日汇总任务
 */
async function runDailySummary() {
  console.log('🔄 开始执行每日汇总预计算...')
  
  const startTime = Date.now()
  
  try {
    await precomputedCache.precomputeDailySummary()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '每日汇总预计算完成',
      duration_ms: duration,
      task: 'daily_summary'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '每日汇总预计算失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 运行项目排名任务
 */
async function runProjectRanking() {
  console.log('🔄 开始执行项目排名预计算...')
  
  const startTime = Date.now()
  
  try {
    await precomputedCache.precomputeProjectRankingSummary()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '项目排名预计算完成',
      duration_ms: duration,
      task: 'project_ranking'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '项目排名预计算失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 运行时段分析任务
 */
async function runHourlyAnalysis() {
  console.log('🔄 开始执行时段分析预计算...')
  
  const startTime = Date.now()
  
  try {
    await precomputedCache.precomputeHourlyAnalysisSummary()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '时段分析预计算完成',
      duration_ms: duration,
      task: 'hourly_analysis'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '时段分析预计算失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 运行转化率趋势任务
 */
async function runConversionTrends() {
  console.log('🔄 开始执行转化率趋势预计算...')
  
  const startTime = Date.now()
  
  try {
    await precomputedCache.precomputeConversionTrends()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: '转化率趋势预计算完成',
      duration_ms: duration,
      task: 'conversion_trends'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '转化率趋势预计算失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * 检查任务状态
 */
async function checkTaskStatus(taskType: string): Promise<any> {
  try {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000) // 7天前
    
    const data = await precomputedCache.getPrecomputedData(
      taskType.replace('_summary', ''), 
      startDate, 
      endDate
    )
    
    return {
      cached: data !== null,
      record_count: data ? data.length : 0,
      last_updated: data ? new Date().toISOString() : null
    }
  } catch (error) {
    return {
      cached: false,
      error: error instanceof Error ? error.message : '检查失败'
    }
  }
}

/**
 * 检查Redis连接
 */
async function checkRedisConnection(): Promise<boolean> {
  try {
    const redis = await import('../../../lib/redis-client')
    const client = await redis.getRedisClient()
    await client.ping()
    return true
  } catch (error) {
    return false
  }
}

/**
 * 检查MySQL连接
 */
async function checkMysqlConnection(): Promise<boolean> {
  try {
    const mysql = await import('../../../lib/mysql-db')
    const connection = await mysql.getConnection()
    connection.release()
    return true
  } catch (error) {
    return false
  }
} 