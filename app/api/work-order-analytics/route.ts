import { NextResponse } from 'next/server'
import { getConnection } from '@/lib/mysql-db'

// Execute MySQL query
const executeQuery = async (query: string): Promise<any[]> => {
  let connection
  try {
    connection = await getConnection()
    const [rows] = await connection.execute(query)
    return rows as any[]
  } catch (error: any) {
    console.error('Error executing MySQL query:', error)
    throw error
  } finally {
    if (connection) {
      connection.release()
    }
  }
}

export async function GET(request: Request) {
  try {
    console.log('📊 Work Order Analytics API - MySQL查询模式')
    
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const project = searchParams.get('project')
    const group = searchParams.get('group')

    console.log(`🔍 查询类型: ${type}`, { startDate, endDate, project, group })

    // 构建基础WHERE条件
    let whereClause = '1=1'
    
    if (startDate && endDate) {
      whereClause += ` AND DATE(creation_time) BETWEEN '${startDate}' AND '${endDate}'`
    }
    
    if (project && project !== '') {
      whereClause += ` AND project = '${project}'`
    }
    if (group && group !== '') {
      whereClause += ` AND agent LIKE '%${group}%'`
    }

    let query = ''
    
    switch (type) {
      case 'overview':
        query = `
          SELECT 
            COUNT(*) as total_orders,
            COUNT(DISTINCT project) as unique_projects,
            COUNT(DISTINCT customer) as unique_customers,
            COUNT(DISTINCT agent) as unique_agents,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = '未分配' THEN 1 END) as unassigned_orders,
            COUNT(CASE WHEN status = '进行中' THEN 1 END) as in_progress_orders,
            COUNT(CASE WHEN Emergency_Level = '紧急' THEN 1 END) as urgent_orders,
            COUNT(CASE WHEN Emergency_Level = '不紧急' THEN 1 END) as normal_orders,
            COUNT(CASE WHEN Distribution = 'IOC' THEN 1 END) as ioc_orders,
            COUNT(CASE WHEN survey IS NOT NULL AND survey != '' THEN 1 END) as surveyed_orders
          FROM work_order 
          WHERE ${whereClause}
        `
        break

      case 'status_distribution':
        query = `
          SELECT 
            status,
            COUNT(*) as count,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM work_order WHERE ${whereClause})), 2) as percentage
          FROM work_order 
          WHERE ${whereClause}
          GROUP BY status
          ORDER BY count DESC
        `
        break

      case 'emergency_level_analysis':
        query = `
          SELECT 
            Emergency_Level as emergency_level,
            COUNT(*) as count,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM work_order WHERE ${whereClause})), 2) as percentage,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_count,
            ROUND((COUNT(CASE WHEN status = '已完成' THEN 1 END) * 100.0 / COUNT(*)), 2) as completion_rate
          FROM work_order 
          WHERE ${whereClause}
          GROUP BY Emergency_Level
          ORDER BY count DESC
        `
        break

      case 'type_analysis':
        query = `
          SELECT 
            current_type,
            COUNT(*) as count,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM work_order WHERE ${whereClause} AND current_type IS NOT NULL AND current_type != '')), 2) as percentage,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_count,
            ROUND((COUNT(CASE WHEN status = '已完成' THEN 1 END) * 100.0 / COUNT(*)), 2) as completion_rate
          FROM work_order 
          WHERE ${whereClause} AND current_type IS NOT NULL AND current_type != ''
          GROUP BY current_type
          ORDER BY count DESC
          LIMIT 20
        `
        break

      case 'project_ranking':
        query = `
          SELECT 
            project,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = '未分配' THEN 1 END) as unassigned_orders,
            COUNT(CASE WHEN Emergency_Level = '紧急' THEN 1 END) as urgent_orders,
            ROUND((COUNT(CASE WHEN status = '已完成' THEN 1 END) * 100.0 / COUNT(*)), 2) as completion_rate,
            ROUND((COUNT(CASE WHEN Emergency_Level = '紧急' THEN 1 END) * 100.0 / COUNT(*)), 2) as urgent_rate
          FROM work_order 
          WHERE ${whereClause} AND project IS NOT NULL AND project != ''
          GROUP BY project
          ORDER BY total_orders DESC
          LIMIT 50
        `
        break

      case 'agent_performance':
        query = `
          SELECT 
            agent,
            agent_phone,
            project,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN Emergency_Level = '紧急' THEN 1 END) as urgent_orders,
            COUNT(DISTINCT project) as projects_served,
            ROUND((COUNT(CASE WHEN status = '已完成' THEN 1 END) * 100.0 / COUNT(*)), 2) as completion_rate
          FROM work_order 
          WHERE ${whereClause} AND agent IS NOT NULL AND agent != ''
          GROUP BY agent, agent_phone, project
          ORDER BY total_orders DESC
          LIMIT 50
        `
        break

      case 'daily_trend':
        query = `
          SELECT 
            DATE(creation_time) as date,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = '未分配' THEN 1 END) as unassigned_orders,
            COUNT(CASE WHEN Emergency_Level = '紧急' THEN 1 END) as urgent_orders,
            ROUND((COUNT(CASE WHEN status = '已完成' THEN 1 END) * 100.0 / COUNT(*)), 2) as completion_rate
          FROM work_order 
          WHERE ${whereClause}
          GROUP BY DATE(creation_time)
          ORDER BY DATE(creation_time) DESC
          LIMIT 30
        `
        break

      case 'hourly_distribution':
        query = `
          SELECT 
            HOUR(creation_time) as hour,
            COUNT(*) as count
          FROM work_order 
          WHERE ${whereClause}
          GROUP BY HOUR(creation_time)
          ORDER BY hour
        `
        break

      default:
        return NextResponse.json(
          { error: `不支持的查询类型: ${type}` },
          { status: 400 }
        )
    }

    console.log('🔍 执行MySQL查询:', query.substring(0, 150) + '...')
    
    const data = await executeQuery(query)
    
    console.log(`✅ 工单分析查询完成: ${type}, 返回 ${data.length} 条记录`)
    
    return NextResponse.json({
      data,
      cached: false,
      source: 'mysql',
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    console.error(`❌ 工单分析查询失败:`, error)
    return NextResponse.json(
      { 
        error: '查询失败', 
        details: error.message,
        source: 'mysql'
      },
      { status: 500 }
    )
  }
} 