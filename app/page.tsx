'use client'

import { useState } from 'react'
import {
  Box,
  Snackbar,
  Alert,
  useTheme,
  alpha,
} from '@mui/material'
import { useRouter } from 'next/navigation'
import Navbar from '../components/landing/Navbar'
import HeroSection from '../components/landing/HeroSection'
import FeaturesSection from '../components/landing/FeaturesSection'

export default function LandingPage() {
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState('')
  const theme = useTheme()
  const router = useRouter()

  const handleWaitlistClick = () => {
    router.push('/waitlist')
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(to bottom right, ${alpha(theme.palette.primary.light, 0.1)}, ${alpha(theme.palette.primary.dark, 0.2)})`,
        overflow: 'hidden',
        position: 'relative',
      }}
    >
      {/* 导航栏 */}
      <Navbar onWaitlistClick={handleWaitlistClick} />

      {/* 英雄区域 */}
      <HeroSection onWaitlistClick={handleWaitlistClick} />

      {/* 特性详细介绍区域 */}
      <FeaturesSection />

      {/* 提交成功提示 */}
      <Snackbar
        open={submitted}
        autoHideDuration={6000}
        onClose={() => setSubmitted(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setSubmitted(false)} severity="success" sx={{ width: '100%' }}>
          感谢您的关注！我们会在产品上线时第一时间通知您。
        </Alert>
      </Snackbar>

      {/* 错误提示 */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  )
}
