'use client'

import { useState, useEffect } from 'react'
import { 
  Container, 
  Typography, 
  Button, 
  Box, 
  Paper, 
  CircularProgress,
  Alert,
  Divider
} from '@mui/material'
import CacheStatus from '../../components/CacheStatus'

export default function TestCachePage() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleRefreshCache = async () => {
    setLoading(true)
    setError('')
    setMessage('')
    
    try {
      const response = await fetch('/api/cache/refresh', {
        method: 'POST'
      })
      const result = await response.json()
      
      if (result.success) {
        setMessage(`缓存刷新成功！耗时: ${result.duration}`)
      } else {
        setError(result.error || '刷新失败')
      }
    } catch (err) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const testCachedAPI = async () => {
    setLoading(true)
    setError('')
    setMessage('')
    
    try {
      const startTime = Date.now()
      const response = await fetch('/api/cached-dashboard-stats')
      const duration = Date.now() - startTime
      const result = await response.json()
      
      if (result.success) {
        setMessage(`缓存API测试成功！耗时: ${duration}ms，数据量: ${result.data.totalConversations} 条对话`)
      } else {
        setError(result.error || '测试失败')
      }
    } catch (err) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  const testOriginalAPI = async () => {
    setLoading(true)
    setError('')
    setMessage('')
    
    try {
      const startTime = Date.now()
      const response = await fetch('/api/dashboard-stats')
      const duration = Date.now() - startTime
      const result = await response.json()
      
      if (result.data) {
        const cached = result.cached ? '是' : '否'
        setMessage(`原始API测试成功！耗时: ${duration}ms，使用缓存: ${cached}`)
      } else {
        setError(result.error || '测试失败')
      }
    } catch (err) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        缓存系统测试页面
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <CacheStatus />
      </Box>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          缓存操作
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            onClick={handleRefreshCache}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            手动刷新缓存
          </Button>
          
          <Button
            variant="outlined"
            onClick={testCachedAPI}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            测试缓存API
          </Button>
          
          <Button
            variant="outlined"
            onClick={testOriginalAPI}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            测试原始API
          </Button>
        </Box>

        {message && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {message}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Divider sx={{ my: 3 }} />

        <Typography variant="h6" gutterBottom>
          API端点
        </Typography>
        
        <Box sx={{ '& > *': { mb: 1 } }}>
          <Typography variant="body2">
            • 缓存状态: <code>GET /api/cache/status</code>
          </Typography>
          <Typography variant="body2">
            • 刷新缓存: <code>POST /api/cache/refresh</code>
          </Typography>
          <Typography variant="body2">
            • 缓存API: <code>GET /api/cached-dashboard-stats</code>
          </Typography>
          <Typography variant="body2">
            • 原始API: <code>GET /api/dashboard-stats</code>
          </Typography>
        </Box>
      </Paper>
    </Container>
  )
} 