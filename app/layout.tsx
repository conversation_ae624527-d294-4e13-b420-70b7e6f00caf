import type { Metadata } from 'next'
import './globals.css'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v14-appRouter'
import { ClerkProvider } from '@clerk/nextjs'
import { zhCN } from '@clerk/localizations'
import { ThemeProvider as MuiThemeProvider } from '../lib/theme-provider'

// 缓存系统已删除 - 现在使用直接MySQL查询
// import '../lib/cache-init' // 已删除

export const metadata: Metadata = {
  title: 'Chat Analytics',
  description: 'AI-powered conversation analysis platform',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider 
      localization={zhCN}
      waitlistUrl="/waitlist"
    >
      <html lang="zh-CN">
        <body>
          <AppRouterCacheProvider options={{ enableCssLayer: true }}>
            <MuiThemeProvider>
              {children}
            </MuiThemeProvider>
          </AppRouterCacheProvider>
        </body>
      </html>
    </ClerkProvider>
  )
}
