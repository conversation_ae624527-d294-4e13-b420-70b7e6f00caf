'use client'

import { Waitlist } from '@clerk/nextjs'
import { Box, Container, Paper, Typography } from '@mui/material'

export default function WaitlistPage() {
  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          py: 8,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: '100%',
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography variant="h4" component="h1" gutterBottom textAlign="center">
            加入 Chat Analytics 等待名单
          </Typography>
          <Typography variant="body1" color="text.secondary" mb={4} textAlign="center">
            我们正在为您准备最佳的对话分析体验。请加入等待名单，我们会在系统准备就绪时第一时间通知您。
          </Typography>
          
          {/* Clerk Waitlist 组件 */}
          <Box sx={{ width: '100%' }}>
            <Waitlist />
          </Box>
        </Paper>
      </Box>
    </Container>
  )
} 