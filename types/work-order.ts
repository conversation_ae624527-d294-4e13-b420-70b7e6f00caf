// 工单概览统计
export interface WorkOrderOverview {
  total_orders: number
  unique_projects: number
  unique_customers: number
  unique_agents: number
  completed_orders: number
  unassigned_orders: number
  in_progress_orders: number
  urgent_orders: number
  normal_orders: number
  ioc_orders: number
  surveyed_orders: number
}

// 工单状态分布
export interface WorkOrderStatusDistribution {
  status: string
  count: number
  percentage: number
}

// 紧急程度分析
export interface WorkOrderEmergencyAnalysis {
  emergency_level: string
  count: number
  percentage: number
  completed_count: number
  completion_rate: number
}

// 工单类型分析
export interface WorkOrderTypeAnalysis {
  current_type: string
  count: number
  percentage: number
  completed_count: number
  completion_rate: number
}

// 项目工单排名
export interface WorkOrderProjectRanking {
  project: string
  total_orders: number
  completed_orders: number
  unassigned_orders: number
  urgent_orders: number
  completion_rate: number
  urgent_rate: number
}

// 管家使用分析（原代理人绩效分析）
export interface WorkOrderAgentPerformance {
  agent: string
  agent_phone: string
  project: string
  total_orders: number
  completed_orders: number
  urgent_orders: number
  projects_served: number
  completion_rate: number
}

// 每日工单趋势
export interface WorkOrderDailyTrend {
  date: string
  total_orders: number
  completed_orders: number
  unassigned_orders: number
  urgent_orders: number
  completion_rate: number
}

// 小时分布分析（饼图格式）
export interface WorkOrderHourlyDistribution {
  name: string
  value: number
  percentage: number
}

// 客户分析（增加更多信息）
export interface WorkOrderCustomerAnalysis {
  customer: string
  customer_phone: string
  project: string
  total_orders: number
  completed_orders: number
  urgent_orders: number
  order_types_count: number
  last_order_time: string
  completion_rate: number
}

// 工单标题关键词分析
export interface WorkOrderTitleKeywords {
  keyword_category: string
  count: number
  percentage: number
  completed_count: number
  completion_rate: number
}

// 分发方式分析
export interface WorkOrderDistributionAnalysis {
  distribution_method: string
  count: number
  percentage: number
  completed_count: number
  completion_rate: number
} 