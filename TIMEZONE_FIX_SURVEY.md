# 满意度调查结果显示为0的问题修复

## 问题原因
满意度调查结果详情显示为0是由于时区问题导致的。在 `hooks/use-analytics-data.ts` 文件中，日期参数使用了 `toISOString().split('T')[0]` 方法，这会将本地时间转换为UTC时间，导致日期偏移。

## 受影响的代码位置
1. `hooks/use-analytics-data.ts` 第264-265行
2. `hooks/use-analytics-data.ts` 第351-354行

## 修复方法

### 1. 添加本地日期格式化函数
在文件开头添加：
```typescript
// 本地日期格式化函数，避免时区问题
function formatLocalDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
```

### 2. 替换问题代码
将以下代码：
```typescript
if (startDate) params.append('startDate', startDate.toISOString().split('T')[0])
if (endDate) params.append('endDate', endDate.toISOString().split('T')[0])
```

替换为：
```typescript
if (startDate) params.append('startDate', formatLocalDate(startDate))
if (endDate) params.append('endDate', formatLocalDate(endDate))
```

### 3. 修复其他位置
同样修复第351-354行的类似问题：
```typescript
// 修复前
dateParams.startDate = startDate.toISOString().split('T')[0]
dateParams.endDate = endDate.toISOString().split('T')[0]

// 修复后
dateParams.startDate = formatLocalDate(startDate)
dateParams.endDate = formatLocalDate(endDate)
```

## 验证修复
修复后，满意度调查结果详情应该显示正确的数据：
- 很满意：133, 182, 179, 183, 171, 201, 64
- 满意：70, 102, 86, 93, 75, 85, 35
- 一般：9, 13, 19, 19, 24, 9, 3
- 不满：4, 5, 4, 9, 11, 6, 5
- 很不满：5, 18, 18, 17, 24, 24, 7

## 影响范围
此修复将解决所有与日期过滤相关的数据显示问题，包括：
- 满意度调查结果详情
- 对话数量趋势
- 其他基于日期范围的统计数据 