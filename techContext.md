# Chat Analytics 技术上下文分析

## 🛠️ 当前技术栈
### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **UI**: React 18 + TypeScript  
- **样式**: CSS Modules + Tailwind CSS
- **图表**: Custom Chart Components
- **状态管理**: React Hooks + Context

### 后端技术栈
- **运行时**: Node.js (Next.js API Routes)
- **数据库**: PostgreSQL (Supabase)
- **缓存**: 内存缓存 (Map-based)
- **认证**: Clerk Authentication
- **部署**: Vercel Platform

## 🏗️ 当前架构分析
### 数据层架构
```
当前数据流:
Browser → Next.js API → cached-queries.ts → Supabase PostgreSQL
                           ↑
                    内存缓存 (Map对象)
```

### 关键技术文件结构
```
lib/
├── cached-queries.ts     (1585行 - 主要缓存逻辑)
├── cache.ts             (580行 - 缓存管理器)  
├── supabase-queries.ts  (720行 - 数据库查询)
├── db.ts                (数据库连接)
└── utils/
    ├── ranking-calculator.ts
    ├── cache-filter.ts
    └── date-utils.ts
```

## ⚠️ 技术债务分析
### 代码质量问题
1. **单文件过大**: `cached-queries.ts` 1585行，维护困难
2. **紧耦合**: 缓存逻辑与业务逻辑混合
3. **缺乏测试**: 核心缓存功能测试覆盖率低
4. **内存泄漏风险**: Map对象无限增长机制

### 架构缺陷
1. **单点故障**: 内存缓存重启后数据丢失
2. **水平扩展困难**: 内存缓存无法跨实例共享
3. **监控缺失**: 无性能监控和告警机制
4. **一致性问题**: 缓存与数据库数据可能不一致

## 🎯 技术改进方向
### 缓存架构升级
```typescript
// 目标架构设计
interface NewCacheArchitecture {
  // L1: 热数据内存缓存 (最近1小时)
  hotCache: Map<string, CachedData>
  
  // L2: Redis分布式缓存 (最近24小时) 
  redisCache: Redis
  
  // L3: PostgreSQL物化视图 (历史数据)
  materializedViews: MaterializedView[]
  
  // L4: 原始数据库
  database: PostgreSQL
}
```

### 数据库优化策略
```sql
-- 关键索引优化
CREATE INDEX CONCURRENTLY idx_conversations_composite 
ON conversations(project_id, company_id, created_at DESC);

-- 物化视图设计
CREATE MATERIALIZED VIEW mv_project_daily_stats AS
SELECT 
  DATE(created_at) as date,
  project_id,
  COUNT(*) as conversation_count,
  AVG(satisfaction_score) as avg_satisfaction
FROM conversations
GROUP BY DATE(created_at), project_id;
```

## 🚀 技术实施路径
### Phase 1: 监控和基线 (Week 1-2)
**技术组件**:
- Prometheus + Grafana 监控栈
- 自定义性能指标收集器
- 数据库查询性能分析工具

**开发任务**:
```typescript
// 性能监控中间件
export class PerformanceMonitor {
  trackQueryTime(queryName: string, duration: number)
  trackCacheHit(cacheKey: string, hit: boolean)  
  trackMemoryUsage(component: string, bytes: number)
}
```

### Phase 2: 数据库优化 (Week 3-4)
**技术重点**:
- PostgreSQL查询优化
- 索引策略重设计
- 物化视图实现

**关键改进**:
```sql
-- 查询性能优化
EXPLAIN ANALYZE SELECT ... FROM conversations 
WHERE project_id = $1 AND created_at >= $2;

-- 物化视图自动刷新
CREATE OR REPLACE FUNCTION refresh_mv_stats()
RETURNS void AS $$ 
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_project_daily_stats;
END;
$$ LANGUAGE plpgsql;
```

### Phase 3: 缓存重构 (Week 5-6)
**技术栈升级**:
- Redis Cluster 7.0+
- ioredis客户端
- 缓存策略重设计

**实现重点**:
```typescript
// 混合缓存管理器
export class HybridCacheManager {
  private hotCache: Map<string, any>
  private redisClient: Redis
  
  async get(key: string): Promise<any>
  async set(key: string, value: any, ttl?: number): Promise<void>
  async invalidate(pattern: string): Promise<void>
}
```

### Phase 4: 代码重构 (Week 7-8)
**重构目标**:
- 模块化拆分: 1585行 → 多个<200行文件
- 类型安全提升
- 测试覆盖率>90%

**新的文件结构**:
```
lib/
├── cache/
│   ├── HybridCacheManager.ts
│   ├── RedisCache.ts  
│   └── MemoryCache.ts
├── queries/
│   ├── ProjectQueries.ts
│   ├── CompanyQueries.ts
│   └── RankingQueries.ts
└── monitoring/
    ├── PerformanceMonitor.ts
    └── MetricsCollector.ts
```

## 🔧 技术约束和考虑
### 平台限制
- **Vercel**: 函数执行时间限制 (10s Hobby, 300s Pro)
- **内存限制**: 1GB (Hobby), 3008MB (Pro)
- **冷启动**: 需要优化首次加载时间

### 数据库约束
- **Supabase**: 连接数限制，需要连接池优化
- **查询复杂度**: 避免N+1查询问题
- **数据一致性**: 确保缓存与数据库同步

### 安全考虑
- **认证**: 继续使用Clerk，增加API速率限制
- **数据访问**: 实现细粒度权限控制
- **缓存安全**: Redis访问控制和数据加密 