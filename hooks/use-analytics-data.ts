import { useState, useEffect, useCallback, useMemo } from 'react'
import { AreaRanking } from '@/components/ui/area-ranking-table'
import { CompanyRanking } from '@/components/ui/company-ranking-table'

// 本地日期格式化函数，避免时区问题
function formatLocalDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// Define types
export interface ConversationCount {
  month: string
  day?: string
  conversation_count: number
}

export interface SurveyResult {
  month: string
  day?: string // Optional day field for daily data
  total_conversations: number
  total_survey: number // 总评价数
  very_satisfied: number
  satisfied: number
  neutral: number
  unsatisfied: number
  very_unsatisfied: number
  satisfaction_rate: number
}

export interface NPSResult {
  month?: string
  day?: string
  total_responses: number
  promoters: number
  passives: number
  detractors: number
  promoters_percentage: number
  passives_percentage: number
  detractors_percentage: number
  nps_score: number
}

export interface ProjectActivity {
  month: string
  day?: string
  top_projects?: string
  conversation_count?: number
  project?: string
}

export interface ConversionRate {
  month: string
  day?: string
  total_conversations: number
  work_order_count: number
  work_order_rate: number
  lead_count: number
  lead_rate: number
  avg_messages: number
}

// New interface for ActiveProjectTrend
export interface ActiveProjectTrend {
  month?: string
  day?: string
  active_projects: number
  cumulative_active_projects?: number // 累积活跃项目数
}

// New interface for InactiveProject
export interface InactiveProject {
  project_name: string
  company: string
  last_active: string
  conversation_count: number
}

// New interface for InactiveGrid
export interface InactiveGrid {
  grid_name: string
  grid_code: string
  project_name: string
  company: string
  last_active: string
  conversation_count: number
}

// EmotionDistribution interface removed

export interface TopicDistribution {
  topic_type: string
  count: number
  percentage: number
}

export interface DetailedTopicDistribution {
  月份: string
  日期?: string // Add optional 日期 field for daily data
  总对话数: number
  供水问题: number
  水费缴纳: number
  '卫浴/漏水问题': number
  送水服务: number
  停电咨询: number
  电费缴纳: number
  电器维修: number
  照明问题: number
  燃气问题: number
  物业费: number
  '车位费用/管理': number
  噪音投诉: number
  一般投诉: number
  车位被占: number
  充电桩: number
  电梯问题: number
  卫生清洁: number
  门窗维修: number
  热水器问题: number
  '安装/预约服务': number
  家具维修: number
  '车辆登记/变更': number
  '车辆通行/搬家': number
  '门禁/出入管理': number
  锁具问题: number
  钥匙问题: number
  快递服务: number
  '装修/施工': number
  暖气问题: number
  家政服务: number
  搬家服务: number
  打印服务: number
  物品借用: number
  租房咨询: number
  一般咨询: number
  转人工服务: number
  '满意/感谢反馈': number
  '问候/简单交流': number
  '无明确诉求/测试': number
  '紧急情况/安全问题': number
}

export interface ProjectRanking {
  project: string
  conversation_count: number
  avg_messages: number
  work_order_rate: number
  lead_rate: number
  satisfaction_rate: number
  volume_rank: number
  satisfaction_rank: number
  work_order_rank: number
  lead_rank: number
  efficiency_rank: number
}

// 添加一个新的接口，包含总数信息
export interface InactiveProjectsData {
  project_name: string
  company: string
  last_active: string
  conversation_count: number
  total_count: number
}

// 添加一个新的接口，包含网格总数信息
export interface InactiveGridsData {
  grid_name: string
  grid_code: string
  project_name: string
  company: string
  last_active: string
  conversation_count: number
  total_count: number
}

// 24小时分析接口
export interface HourlyAnalysis {
  hour: string // 格式：'09:00'
  hour_num: number // 数字小时：9
  conversation_count: number
  work_order_count: number
  lead_count: number
  work_order_rate: number
  lead_rate: number
  satisfaction_rate: number
}

// Custom hook for fetching analytics data
export function useAnalyticsData(
  selectedMonth: string | null,
  selectedProject: string | null,
  selectedGroup: string | null,
  selectedArea: string | null,
  selectedCompany: string | null,
  startDate?: Date | null,
  endDate?: Date | null,
  selectedGrid?: string | null,
  showGrids?: boolean
) {
  const [months, setMonths] = useState<string[]>([])
  const [groups, setGroups] = useState<string[]>([])
  const [areas, setAreas] = useState<string[]>([])
  const [companies, setCompanies] = useState<string[]>([])
  const [projects, setProjects] = useState<string[]>([])
  const [grids, setGrids] = useState<{ grid_name: string; grid_code: string }[]>([])
  const [conversationCounts, setConversationCounts] = useState<ConversationCount[]>([])
  const [surveyResults, setSurveyResults] = useState<SurveyResult[]>([])
  const [projectActivity, setProjectActivity] = useState<ProjectActivity[]>([])
  const [conversionRates, setConversionRates] = useState<ConversionRate[]>([])
  const [topicDistribution, setTopicDistribution] = useState<TopicDistribution[]>([])
  const [detailedTopicDistribution, setDetailedTopicDistribution] = useState<
    DetailedTopicDistribution[]
  >([])
  const [projectRankings, setProjectRankings] = useState<ProjectRanking[]>([])
  const [areaRankings, setAreaRankings] = useState<AreaRanking[]>([])
  const [companyRankings, setCompanyRankings] = useState<CompanyRanking[]>([])
  const [npsTrend, setNpsTrend] = useState<NPSResult[]>([])
  // Add new state for active project trend and inactive projects
  const [activeProjectsTrend, setActiveProjectsTrend] = useState<ActiveProjectTrend[]>([])
  const [hourlyAnalysis, setHourlyAnalysis] = useState<HourlyAnalysis[]>([])
  const [inactiveProjects, setInactiveProjects] = useState<InactiveProjectsData[]>([])
  const [isLoadingMoreInactive, setIsLoadingMoreInactive] = useState<boolean>(false)
  const [hasMoreInactiveProjects, setHasMoreInactiveProjects] = useState<boolean>(true)
  const [currentInactivePage, setCurrentInactivePage] = useState<number>(0)
  // Add state for inactive grids
  const [inactiveGrids, setInactiveGrids] = useState<InactiveGridsData[]>([])
  const [isLoadingMoreInactiveGrids, setIsLoadingMoreInactiveGrids] = useState<boolean>(false)
  const [hasMoreInactiveGrids, setHasMoreInactiveGrids] = useState<boolean>(true)
  const [currentInactiveGridsPage, setCurrentInactiveGridsPage] = useState<number>(0)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  // 添加特定功能的加载状态和错误
  const [areaRankingsLoading, setAreaRankingsLoading] = useState(false)
  const [areaRankingsError, setAreaRankingsError] = useState<string | null>(null)
  const [companyRankingsLoading, setCompanyRankingsLoading] = useState(false)
  const [companyRankingsError, setCompanyRankingsError] = useState<string | null>(null)

  // 添加一个通用的重试函数，支持API请求的重试
  const fetchWithRetry = async (url: string, retries = 3, delay = 2000) => {
    let lastError: any
    for (let i = 0; i < retries; i++) {
      try {
        const response = await fetch(url)
        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(errorText || `HTTP error ${response.status}`)
        }
        return response
      } catch (err) {
        lastError = err
        console.warn(`Fetch attempt ${i + 1} failed, retrying in ${delay}ms...`, err)
        // 超时错误多给一些时间重试
        if (err instanceof Error && err.message.includes('timeout')) {
          delay = delay * 2
        }
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    // 所有重试都失败
    throw lastError
  }

  // 对查询参数进行分页或时间段分割的函数
  const getOptimizedParams = (baseParams: URLSearchParams, queryType: string) => {
    const params = new URLSearchParams(baseParams)

    // 对于可能会导致超时的API，添加更细粒度的分页或时间段限制
    if (queryType === 'area-ranking' || queryType === 'company-ranking') {
      // 如果是区域排名或公司排名，添加结果限制
      params.append('limit', '50')
    }

    return params.toString()
  }

  // Fetch conversation stats data by type
  const getQueryParams = useCallback(
    (type: string, additionalParams: Record<string, string> = {}) => {
      // Build base query params
      const params = new URLSearchParams()
      params.append('type', type)

      // Always prioritize date range over month selection
      if (startDate) params.append('startDate', formatLocalDate(startDate))
      if (endDate) params.append('endDate', formatLocalDate(endDate))

      // Only use month if no date range is provided
      if (!startDate && !endDate && selectedMonth) {
        params.append('month', selectedMonth)
      }

      // Add filters if specified
      if (selectedGroup) params.append('group', selectedGroup)
      if (selectedArea) params.append('area', selectedArea)
      if (selectedCompany) params.append('company', selectedCompany)
      if (selectedProject) params.append('project', selectedProject)
      if (selectedGrid) params.append('grid', selectedGrid)
      if (showGrids) params.append('showGrids', 'true')

      // Add special data format parameter for trending data
      params.append('data_format', 'daily')

      // Add any additional params
      Object.entries(additionalParams).forEach(([key, value]) => {
        params.append(key, value)
      })

      return params.toString()
    },
    [
      selectedMonth,
      selectedGroup,
      selectedArea,
      selectedCompany,
      selectedProject,
      selectedGrid,
      showGrids,
      startDate,
      endDate,
    ]
  )

  // Fetch available months and projects (only once on initial load)
  useEffect(() => {
    const fetchFilters = async () => {
      try {
        // Fetch available months
        const monthsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('all_months')}`
        )
        const monthsData = await monthsResponse.json()
        setMonths(monthsData.data.map((item: { month: string }) => item.month))

        // Fetch available projects
        const projectsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('all_projects')}`
        )
        const projectsData = await projectsResponse.json()
        setProjects(projectsData.data.map((item: { project: string }) => item.project))

        // Fetch groups
        const groupsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('all_project_groups')}`
        )
        const groupsData = await groupsResponse.json()
        setGroups(groupsData.data.map((item: { group: string }) => item.group))
      } catch (err) {
        console.error('Error fetching filters:', err)
        setError('Failed to load filters. Please try refreshing the page.')
      }
    }

    fetchFilters()
  }, [getQueryParams])

  // Fetch data whenever filters change
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // 重置非活跃项目数据
        resetInactiveProjects()
        // 重置非活跃网格数据
        resetInactiveGrids()

        // 创建包含日期范围的参数
        const dateParams: Record<string, string> = {}
        if (startDate) {
          dateParams.startDate = formatLocalDate(startDate) // 只传递日期部分 YYYY-MM-DD
        }
        if (endDate) {
          dateParams.endDate = formatLocalDate(endDate) // 只传递日期部分 YYYY-MM-DD
        }

        // Fetch conversation counts
        const conversationResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('conversation_count', dateParams)}`
        )
        const conversationData = await conversationResponse.json()
        setConversationCounts(conversationData.data)

        // Fetch survey results
        const surveyResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('survey_results_daily', dateParams)}`
        )
        const surveyData = await surveyResponse.json()
        setSurveyResults(surveyData.data)

        // Fetch project activity
        const projectResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('project_activity', dateParams)}`
        )
        const projectData = await projectResponse.json()
        setProjectActivity(projectData.data)

        // Fetch conversion rates
        const conversionResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('conversion_rates', {
            ...dateParams,
            data_format: 'daily' // 明确指定按天格式
          })}`
        )
        const conversionData = await conversionResponse.json()
        setConversionRates(conversionData.data)

        // Fetch topic distribution
        const topicResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('topic_distribution', dateParams)}`
        )
        const topicData = await topicResponse.json()
        setTopicDistribution(topicData.data)

        // Fetch intelligent topic distribution (新的智能分类API)
        const detailedTopicResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('intelligent_topic_distribution', {
            ...dateParams,
            data_format: 'daily' // 明确指定按天格式
          })}`
        )
        const detailedTopicData = await detailedTopicResponse.json()
        setDetailedTopicDistribution(detailedTopicData.data)

        // Fetch project rankings
        const projectRankingsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('project_ranking', dateParams)}`
        )
        const projectRankingsData = await projectRankingsResponse.json()
        setProjectRankings(projectRankingsData.data)

        // Fetch NPS trend data
        const npsTrendResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('nps_trend', dateParams)}`
        )
        const npsTrendData = await npsTrendResponse.json()
        setNpsTrend(npsTrendData.data)

        // Fetch active projects trend data
        const activeProjectsTrendResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('active_projects_trend', dateParams)}`
        )
        const activeProjectsTrendData = await activeProjectsTrendResponse.json()
        setActiveProjectsTrend(activeProjectsTrendData.data)

        // Fetch hourly analysis data
        const hourlyAnalysisResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('hourly_analysis', dateParams)}`
        )
        const hourlyAnalysisData = await hourlyAnalysisResponse.json()
        setHourlyAnalysis(hourlyAnalysisData.data)

        // 加载第一页非活跃项目数据
        const params = {
          page: '0',
          pageSize: '500',
          ...dateParams,
        }
        const inactiveProjectsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('inactive_projects', params)}`
        )
        const inactiveProjectsData = await inactiveProjectsResponse.json()

        // 设置数据并更新页码
        if (inactiveProjectsData.data && inactiveProjectsData.data.length > 0) {
          setInactiveProjects(inactiveProjectsData.data)
          setCurrentInactivePage(0)

          // 检查是否有更多数据待加载
          const totalCount = inactiveProjectsData.data[0]?.total_count || 0
          setHasMoreInactiveProjects(inactiveProjectsData.data.length < totalCount)
        } else {
          setInactiveProjects([])
          setHasMoreInactiveProjects(false)
        }

        // 加载第一页非活跃网格数据
        if (showGrids) {
          const gridParams = {
            page: '0',
            pageSize: '500',
            ...dateParams,
          }
          const inactiveGridsResponse = await fetchWithRetry(
            `/api/conversation-stats?${getQueryParams('inactive_grids', gridParams)}`
          )
          const inactiveGridsData = await inactiveGridsResponse.json()

          // 设置数据并更新页码
          if (inactiveGridsData.data && inactiveGridsData.data.length > 0) {
            setInactiveGrids(inactiveGridsData.data)
            setCurrentInactiveGridsPage(0)

            // 检查是否有更多数据待加载
            const totalCount = inactiveGridsData.data[0]?.total_count || 0
            setHasMoreInactiveGrids(inactiveGridsData.data.length < totalCount)
          } else {
            setInactiveGrids([])
            setHasMoreInactiveGrids(false)
          }
        }

        // Fetch area rankings when 万科物业 is selected
        if (selectedGroup === '万科物业' || selectedGroup === '伯恩物业') {
          // 区域排名加载
          setAreaRankingsLoading(true)
          setAreaRankingsError(null)

          try {
            // Make sure type parameter is first in the query string for better caching/debugging
            const areaParams = new URLSearchParams()
            // 优先使用日期范围
            if (dateParams.startDate) areaParams.append('startDate', dateParams.startDate)
            if (dateParams.endDate) areaParams.append('endDate', dateParams.endDate)
            if (!dateParams.startDate && !dateParams.endDate && selectedMonth) {
              areaParams.append('month', selectedMonth)
            }
            if (selectedGroup) areaParams.append('group', selectedGroup)
            areaParams.append('data_format', 'daily')

            const areaRankingsResponse = await fetchWithRetry(
              `/api/area-ranking?${areaParams.toString()}`
            )
            const areaRankingsData = await areaRankingsResponse.json()
            setAreaRankings(areaRankingsData.data)
            setAreaRankingsLoading(false)
          } catch (err) {
            console.error('Error fetching area rankings:', err)
            setAreaRankingsError('无法加载区域排名数据')
            setAreaRankingsLoading(false)
          }

          // 公司排名加载
          setCompanyRankingsLoading(true)
          setCompanyRankingsError(null)

          try {
            // Make sure type parameter is first in the query string for better caching/debugging
            const companyParams = new URLSearchParams()
            // 优先使用日期范围
            if (dateParams.startDate) companyParams.append('startDate', dateParams.startDate)
            if (dateParams.endDate) companyParams.append('endDate', dateParams.endDate)
            if (!dateParams.startDate && !dateParams.endDate && selectedMonth) {
              companyParams.append('month', selectedMonth)
            }
            if (selectedGroup) companyParams.append('group', selectedGroup)
            companyParams.append('data_format', 'daily')

            const companyRankingsResponse = await fetchWithRetry(
              `/api/company-ranking?${companyParams.toString()}`
            )
            const companyRankingsData = await companyRankingsResponse.json()
            setCompanyRankings(companyRankingsData.data)
            setCompanyRankingsLoading(false)
          } catch (err) {
            console.error('Error fetching company rankings:', err)
            setCompanyRankingsError('无法加载公司排名数据')
            setCompanyRankingsLoading(false)
          }
        } else {
          setAreaRankings([])
          setCompanyRankings([])
        }
      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load data. Please try again later.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [
    selectedMonth,
    selectedProject,
    selectedGroup,
    selectedArea,
    selectedCompany,
    startDate,
    endDate,
    selectedGrid,
    showGrids,
  ])

  // 获取所有 group
  useEffect(() => {
    fetch('/api/conversation-stats?type=all_project_groups')
      .then(res => res.json())
      .then(res => setGroups(res.data.map((g: { group: string }) => g.group)))
  }, [])

  // 获取 area/company（万科物业和伯恩时）
  useEffect(() => {
    if (selectedGroup === '万科物业' || selectedGroup === '伯恩') {
      const params = new URLSearchParams()
      params.append('type', 'all_project_areas')
      params.append('group', selectedGroup)
      fetch(`/api/conversation-stats?${params.toString()}`)
        .then(res => res.json())
        .then(res => setAreas(res.data.map((a: { area: string }) => a.area)))

      const companyParams = new URLSearchParams()
      companyParams.append('type', 'all_project_companies')
      companyParams.append('group', selectedGroup)
      fetch(`/api/conversation-stats?${companyParams.toString()}`)
        .then(res => res.json())
        .then(res => setCompanies(res.data.map((c: { company: string }) => c.company)))
    } else {
      setAreas([])
      setCompanies([])
    }
  }, [selectedGroup])

  // 获取项目列表
  useEffect(() => {
    const params = new URLSearchParams()
    params.append('type', 'all_projects')
    if (selectedGroup) params.append('group', selectedGroup)
    if (selectedArea) params.append('area', selectedArea)
    if (selectedCompany) params.append('company', selectedCompany)
    fetch(`/api/conversation-stats?${params.toString()}`)
      .then(res => res.json())
      .then(res => setProjects(res.data.map((p: { project: string }) => p.project)))
  }, [selectedGroup, selectedArea, selectedCompany])

  // Transform detailed topic data for category-based trend charts
  const topicCategoryTrends = useMemo(() => {
    if (!detailedTopicDistribution || detailedTopicDistribution.length === 0) {
      return {
        基础设施类: [],
        物业服务类: [],
        维修类服务: [],
        车辆与出入类: [],
        社区服务类: [],
        一般交互类: [],
        其他: [],
      }
    }

    // Define categories and their fields (使用新的API字段格式)
    const categories = {
      基础设施类: [
        '供水问题',
        '水费缴纳',
        '卫浴_漏水问题',
        '送水服务',
        '停电咨询',
        '电费缴纳',
        '电器维修',
        '照明问题',
        '燃气问题',
      ],
      物业服务类: [
        '物业费',
        '车位费用_管理',
        '噪音投诉',
        '一般投诉',
        '车位被占',
        '充电桩',
        '电梯问题',
        '卫生清洁',
      ],
      维修类服务: ['门窗维修', '热水器问题', '安装_预约服务', '家具维修'],
      车辆与出入类: ['车辆登记_变更', '车辆通行_搬家', '门禁_出入管理', '锁具问题', '钥匙问题'],
      社区服务类: [
        '快递服务',
        '装修_施工',
        '暖气问题',
        '家政服务',
        '搬家服务',
        '打印服务',
        '物品借用',
        '租房咨询',
      ],
      一般交互类: [
        '一般咨询',
        '转人工服务',
        '满意_感谢反馈',
        '问候_简单交流',
        '无明确诉求_测试',
        '紧急情况_安全问题',
      ],
      其他: ['其他问题'],
    }

    const trends: Record<string, any[]> = {
      基础设施类: [],
      物业服务类: [],
      维修类服务: [],
      车辆与出入类: [],
      社区服务类: [],
      一般交互类: [],
      其他: [],
    }

    // 优先使用日期字段，确保所有数据点都有天级别的日期
    const processedData = detailedTopicDistribution.map(item => {
      return {
        ...item,
        // 新的API格式使用 'date' 字段
        _sortDate: (item as any).date || item.日期 || (item.月份 ? `${item.月份}-01` : ''),
      }
    });

    // 按日期排序数据
    processedData.sort((a, b) => a._sortDate.localeCompare(b._sortDate));

    Object.entries(categories).forEach(([category, fields]) => {
      // 创建一个日期到数据的映射，以便合并同一天的数据
      const dateToData = new Map<string, Record<string, any>>();
      
      processedData.forEach(item => {
        // 使用日期作为 x 轴值，优先使用新的API格式
        const dateValue = (item as any).date || item.日期 || item._sortDate;
        
        if (!dateValue) return; // 跳过没有日期的数据
        
        // 如果该日期还没有数据，初始化
        if (!dateToData.has(dateValue)) {
          dateToData.set(dateValue, {
            day: dateValue,
            total: 0,
          });
        }
        
        const dayData = dateToData.get(dateValue)!;
        
        // 计算该类别在当天的总和（使用新的API格式 field_count）
        fields.forEach(field => {
          const countField = `${field}_count`;
          if ((item as any)[countField]) {
            const value = Number((item as any)[countField]);
            dayData.total += value;
            
            // 保存字段值，如果已经有值则累加
            if (dayData[field]) {
              dayData[field] += value;
            } else {
              dayData[field] = value;
            }
          }
        });
      });
      
      // 将 Map 转换为数组并按日期排序
      trends[category] = Array.from(dateToData.values())
        .sort((a, b) => a.day.localeCompare(b.day));
    });

    return trends
  }, [detailedTopicDistribution])

  // 执行加载更多非活跃项目的方法
  const loadMoreInactiveProjects = useCallback(async () => {
    if (!hasMoreInactiveProjects || isLoadingMoreInactive) return

    try {
      setIsLoadingMoreInactive(true)
      const nextPage = currentInactivePage + 1

      // 使用分页参数获取下一页数据
      const params = {
        page: nextPage.toString(),
        pageSize: '500',
      }

      const inactiveProjectsResponse = await fetchWithRetry(
        `/api/conversation-stats?${getQueryParams('inactive_projects', params)}`
      )
      const inactiveProjectsData = await inactiveProjectsResponse.json()
      const newProjects = inactiveProjectsData.data

      // 检查是否还有更多数据
      if (newProjects.length === 0) {
        setHasMoreInactiveProjects(false)
      } else {
        // 检查返回的第一条记录中的总数信息
        const totalCount = newProjects[0]?.total_count || 0
        setCurrentInactivePage(nextPage)

        // 将新数据添加到现有数据中
        setInactiveProjects(prev => [...prev, ...newProjects])

        // 如果当前数据总量达到或超过total_count，则没有更多数据
        if (inactiveProjects.length + newProjects.length >= totalCount) {
          setHasMoreInactiveProjects(false)
        }
      }
    } catch (err) {
      console.error('Error loading more inactive projects:', err)
      setError('加载更多非活跃项目数据失败')
    } finally {
      setIsLoadingMoreInactive(false)
    }
  }, [
    currentInactivePage,
    getQueryParams,
    hasMoreInactiveProjects,
    inactiveProjects.length,
    isLoadingMoreInactive,
  ])

  // 重置非活跃项目数据，用于筛选条件变化时
  const resetInactiveProjects = useCallback(() => {
    setInactiveProjects([])
    setCurrentInactivePage(0)
    setHasMoreInactiveProjects(true)
  }, [])

  // 重置非活跃网格数据，用于筛选条件变化时
  const resetInactiveGrids = useCallback(() => {
    setInactiveGrids([])
    setCurrentInactiveGridsPage(0)
    setHasMoreInactiveGrids(true)
  }, [])

  // 执行加载更多非活跃网格的方法
  const loadMoreInactiveGrids = useCallback(async () => {
    if (!hasMoreInactiveGrids || isLoadingMoreInactiveGrids) return

    try {
      setIsLoadingMoreInactiveGrids(true)
      const nextPage = currentInactiveGridsPage + 1

      // 使用分页参数获取下一页数据
      const params = {
        page: nextPage.toString(),
        pageSize: '500',
      }

      const inactiveGridsResponse = await fetchWithRetry(
        `/api/conversation-stats?${getQueryParams('inactive_grids', params)}`
      )
      const inactiveGridsData = await inactiveGridsResponse.json()
      const newGrids = inactiveGridsData.data

      // 检查是否还有更多数据
      if (newGrids.length === 0) {
        setHasMoreInactiveGrids(false)
      } else {
        // 检查返回的第一条记录中的总数信息
        const totalCount = newGrids[0]?.total_count || 0
        setCurrentInactiveGridsPage(nextPage)

        // 将新数据添加到现有数据中
        setInactiveGrids(prev => [...prev, ...newGrids])

        // 如果当前数据总量达到或超过total_count，则没有更多数据
        if (inactiveGrids.length + newGrids.length >= totalCount) {
          setHasMoreInactiveGrids(false)
        }
      }
    } catch (err) {
      console.error('Error loading more inactive grids:', err)
      setError('加载更多非活跃网格数据失败')
    } finally {
      setIsLoadingMoreInactiveGrids(false)
    }
  }, [
    currentInactiveGridsPage,
    getQueryParams,
    hasMoreInactiveGrids,
    inactiveGrids.length,
    isLoadingMoreInactiveGrids,
  ])

  // Fetch grids when explicitly requested
  useEffect(() => {
    if (!showGrids) {
      setGrids([])
      return
    }

    const fetchGrids = async () => {
      try {
        const gridsResponse = await fetchWithRetry(
          `/api/conversation-stats?${getQueryParams('all_grids')}`
        )
        const gridsData = await gridsResponse.json()
        setGrids(gridsData.data)
      } catch (err) {
        console.error('Error fetching grids:', err)
      }
    }

    fetchGrids()
  }, [selectedProject, selectedCompany, showGrids, getQueryParams])

  return {
    months,
    groups,
    areas,
    companies,
    projects,
    grids,
    conversationCounts,
    surveyResults,
    projectActivity,
    conversionRates,
    topicDistribution,
    detailedTopicDistribution,
    projectRankings,
    areaRankings,
    companyRankings,
    topicCategoryTrends,
    npsTrend,
    activeProjectsTrend,
    hourlyAnalysis,
    inactiveProjects,
    loadMoreInactiveProjects,
    hasMoreInactiveProjects,
    isLoadingMoreInactive,
    inactiveGrids,
    loadMoreInactiveGrids,
    hasMoreInactiveGrids,
    isLoadingMoreInactiveGrids,
    isLoading,
    error,
    areaRankingsLoading,
    areaRankingsError,
    companyRankingsLoading,
    companyRankingsError,
  }
}
