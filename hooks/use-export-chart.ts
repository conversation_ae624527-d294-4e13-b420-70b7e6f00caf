import { useCallback } from 'react'

export function useExportChart() {
  const exportToExcel = useCallback((data: any[], fileName: string) => {
    // Create a downloadable link
    const createDownloadLink = (blob: Blob, fileName: string) => {
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()

      // Cleanup
      window.URL.revokeObjectURL(url)
      document.body.removeChild(link)
    }

    // Use xlsx library to convert data to Excel
    const exportWithXLSX = async (data: any[], fileName: string) => {
      try {
        // Dynamically import xlsx library
        const XLSX = await import('xlsx')

        // Convert data to worksheet
        const worksheet = XLSX.utils.json_to_sheet(data)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Data')

        // Generate Excel file and download
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
        const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
        createDownloadLink(blob, `${fileName}.xlsx`)
      } catch (error) {
        console.error('Error exporting to Excel:', error)
        alert('导出到Excel失败，请稍后再试')
      }
    }

    // Export the data
    if (data && data.length > 0) {
      exportWithXLSX(data, fileName)
    } else {
      alert('没有数据可导出')
    }
  }, [])

  return { exportToExcel }
}
