import { useState, useEffect, useCallback, useMemo } from 'react'
import {
  WorkOrderOverview,
  WorkOrderStatusDistribution,
  WorkOrderEmergencyAnalysis,
  WorkOrderTypeAnalysis,
  WorkOrderProjectRanking,
  WorkOrderAgentPerformance,
  WorkOrderDailyTrend,
  WorkOrderHourlyDistribution,
  WorkOrderCustomerAnalysis,
  WorkOrderTitleKeywords,
  WorkOrderDistributionAnalysis,
} from '@/types/work-order'

// 本地日期格式化函数，避免时区问题
function formatLocalDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 简单的内存缓存
const dataCache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 清除所有缓存，使用新的真实数据
dataCache.clear()

export function useWorkOrderAnalytics(
  startDate?: Date | null,
  endDate?: Date | null,
  selectedProject?: string | null,
  selectedGroup?: string | null
) {
  // 状态管理
  const [overview, setOverview] = useState<WorkOrderOverview | null>(null)
  const [statusDistribution, setStatusDistribution] = useState<WorkOrderStatusDistribution[]>([])
  const [emergencyAnalysis, setEmergencyAnalysis] = useState<WorkOrderEmergencyAnalysis[]>([])
  const [typeAnalysis, setTypeAnalysis] = useState<WorkOrderTypeAnalysis[]>([])
  const [projectRanking, setProjectRanking] = useState<WorkOrderProjectRanking[]>([])
  const [agentPerformance, setAgentPerformance] = useState<WorkOrderAgentPerformance[]>([])
  const [dailyTrend, setDailyTrend] = useState<WorkOrderDailyTrend[]>([])
  const [hourlyDistribution, setHourlyDistribution] = useState<WorkOrderHourlyDistribution[]>([])
  const [customerAnalysis, setCustomerAnalysis] = useState<WorkOrderCustomerAnalysis[]>([])
  const [titleKeywords, setTitleKeywords] = useState<WorkOrderTitleKeywords[]>([])
  const [distributionAnalysis, setDistributionAnalysis] = useState<WorkOrderDistributionAnalysis[]>([])
  
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // 获取所有数据
  const fetchAllData = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // 生成缓存键，现在包含日期参数
      const startDateStr = startDate ? formatLocalDate(startDate) : 'all'
      const endDateStr = endDate ? formatLocalDate(endDate) : 'all'
      const cacheKey = `workorder-${startDateStr}-${endDateStr}-${selectedProject || 'all'}-${selectedGroup || 'all'}`
      
      // 检查缓存
      const cached = dataCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        console.log('Using cached work order data for key:', cacheKey)
        const cachedData = cached.data
        setOverview(cachedData.overview)
        setStatusDistribution(cachedData.statusDistribution)
        setEmergencyAnalysis(cachedData.emergencyAnalysis)
        setTypeAnalysis(cachedData.typeAnalysis)
        setProjectRanking(cachedData.projectRanking)
        setAgentPerformance(cachedData.agentPerformance)
        setDailyTrend(cachedData.dailyTrend)
        setHourlyDistribution(cachedData.hourlyDistribution)
        setCustomerAnalysis(cachedData.customerAnalysis)
        setTitleKeywords(cachedData.titleKeywords)
        setDistributionAnalysis(cachedData.distributionAnalysis)
        setIsLoading(false)
        return
      }

      console.log('Fetching fresh work order analytics data for key:', cacheKey, {
        startDate: startDateStr,
        endDate: endDateStr,
        selectedProject,
        selectedGroup
      })

      // 构建查询参数的函数
      const buildQueryParams = (type: string) => {
        const params = new URLSearchParams()
        params.append('type', type)
        
        if (startDate) params.append('startDate', formatLocalDate(startDate))
        if (endDate) params.append('endDate', formatLocalDate(endDate))
        if (selectedProject) params.append('project', selectedProject)
        if (selectedGroup) params.append('group', selectedGroup)
        
        return params.toString()
      }

      // 通用的数据获取函数
      const fetchData = async (type: string) => {
        try {
          const url = `/api/work-order-analytics?${buildQueryParams(type)}`
          
          const response = await fetch(url)
          if (!response.ok) {
            const errorText = await response.text()
            console.error(`HTTP error ${response.status} for ${type}:`, errorText)
            throw new Error(`HTTP error ${response.status}: ${errorText}`)
          }
          const result = await response.json()
          return result.data
        } catch (err) {
          console.error(`Error fetching ${type}:`, err)
          throw err
        }
      }

      // 逐个获取数据
      const overviewData = await fetchData('overview')
      setOverview(overviewData[0] || null)

      const statusData = await fetchData('status_distribution')
      setStatusDistribution(statusData || [])

      const emergencyData = await fetchData('emergency_level_analysis')
      setEmergencyAnalysis(emergencyData || [])

      const typeData = await fetchData('type_analysis')
      setTypeAnalysis(typeData || [])

      const projectData = await fetchData('project_ranking')
      setProjectRanking(projectData || [])

      const agentData = await fetchData('agent_performance')
      setAgentPerformance(agentData || [])

      const dailyData = await fetchData('daily_trend')
      setDailyTrend(dailyData || [])

      const hourlyData = await fetchData('hourly_distribution')
      setHourlyDistribution(hourlyData || [])

      const customerData = await fetchData('customer_analysis')
      setCustomerAnalysis(customerData || [])

      const keywordData = await fetchData('title_keywords')
      setTitleKeywords(keywordData || [])

      const distributionData = await fetchData('distribution_analysis')
      setDistributionAnalysis(distributionData || [])

      console.log('All work order analytics data fetched successfully')

      // 缓存数据
      dataCache.set(cacheKey, {
        data: {
          overview: overviewData[0] || null,
          statusDistribution: statusData || [],
          emergencyAnalysis: emergencyData || [],
          typeAnalysis: typeData || [],
          projectRanking: projectData || [],
          agentPerformance: agentData || [],
          dailyTrend: dailyData || [],
          hourlyDistribution: hourlyData || [],
          customerAnalysis: customerData || [],
          titleKeywords: keywordData || [],
          distributionAnalysis: distributionData || [],
        },
        timestamp: Date.now()
      })

    } catch (err) {
      console.error('Error fetching work order analytics:', err)
      setError(`获取工单分析数据失败: ${err instanceof Error ? err.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }, [startDate, endDate, selectedProject, selectedGroup])

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    fetchAllData()
  }, [fetchAllData])

  // 刷新数据函数
  const refreshData = useCallback(() => {
    // 清除缓存
    const startDateStr = startDate ? formatLocalDate(startDate) : 'all'
    const endDateStr = endDate ? formatLocalDate(endDate) : 'all'
    const cacheKey = `workorder-${startDateStr}-${endDateStr}-${selectedProject || 'all'}-${selectedGroup || 'all'}`
    dataCache.delete(cacheKey)
    
    fetchAllData()
  }, [fetchAllData, startDate, endDate, selectedProject, selectedGroup])

  return {
    // 数据
    overview,
    statusDistribution,
    emergencyAnalysis,
    typeAnalysis,
    projectRanking,
    agentPerformance,
    dailyTrend,
    hourlyDistribution,
    customerAnalysis,
    titleKeywords,
    distributionAnalysis,
    
    // 状态
    isLoading,
    error,
    
    // 方法
    refreshData,
  }
} 