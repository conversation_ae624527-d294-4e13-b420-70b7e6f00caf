# MySQL Walrus 数据库到 Supabase PostgreSQL 映射分析

## 📊 数据库概况对比

### MySQL Walrus 数据库
- **总表数**: 87个表
- **总记录数**: 7,740,505条
- **主要数据**: 聊天对话、工单、项目管理、用户管理

### 原 Supabase PostgreSQL 数据库
- **核心表**: conversations (对话记录表)
- **预期数据量**: 400万条对话记录
- **主要分析维度**: 项目、公司、区域、满意度、工单等

## 🔄 核心表映射关系

### 1. 对话记录映射 (conversations)

#### MySQL Source: `chat_msg` (28,185条记录)
```sql
-- 主要字段映射
chat_msg.id                    → conversations.id
chat_msg.chat_id              → conversations.chat_id (会话ID)
chat_msg.bot_id               → conversations.bot_id
chat_msg.external_user_id     → conversations.user_id
chat_msg.contact_name         → conversations.user_name
chat_msg.payload              → conversations.message_content (JSON解析text字段)
chat_msg.timestamp            → conversations.created_at
chat_msg.create_time          → conversations.created_at (备用)
chat_msg.emotion_type         → conversations.emotion_type
```

#### MySQL Source: `chat_list` (2,425条记录)
```sql
-- 会话级别信息映射
chat_list.chat_id             → conversations.chat_id
chat_list.project_name        → conversations.project_name  
chat_list.project_code        → conversations.project_id
chat_list.satisfaction        → conversations.survey
chat_list.summary             → conversations.summary
chat_list.emotion_type        → conversations.emotion_type
chat_list.current_progress    → conversations.progress_status
```

#### MySQL Source: `chat_msg_event` (2,346,389条记录)
```sql
-- 事件信息映射 (包含工单等业务事件)
chat_msg_event.message_id     → 关联到 conversations
chat_msg_event.biz_type       → conversations.event_type
chat_msg_event.biz_result     → conversations.work_order_info
```

### 2. 项目信息映射

#### MySQL Source: `sys_project_config` (52条记录)
```sql
sys_project_config.project_code    → conversations.project_id
sys_project_config.project_name    → conversations.project_name
```

#### MySQL Source: `announcement_project` (15,713条记录)
```sql
announcement_project.project_code  → conversations.project_id
announcement_project.project_name  → conversations.project_name
announcement_project.grids         → conversations.grid_name
```

### 3. 用户和网格映射

#### MySQL Source: `sys_user` (57条记录)
```sql
sys_user.user_id              → conversations.steward_id
sys_user.full_name            → conversations.steward_name
```

#### MySQL Source: `sys_bot` (71条记录)
```sql
sys_bot.grid_code             → conversations.grid_id
sys_bot.grid_name             → conversations.grid_name
sys_bot.project_code          → conversations.project_id
```

## 📋 字段映射详细分析

### conversations 表核心字段构建方案

```sql
-- 基于MySQL数据构建conversations表的映射SQL
SELECT 
  -- 基础ID和时间
  cm.id AS id,
  cm.chat_id AS chat_id,
  FROM_UNIXTIME(cm.timestamp/1000) AS created_at,
  
  -- 用户信息
  cm.external_user_id AS user_id,
  cm.contact_name AS user_name,
  
  -- 项目和网格信息
  cl.project_code AS project_id,
  cl.project_name AS project_name,
  sb.grid_code AS grid_id,
  sb.grid_name AS grid_name,
  
  -- 消息内容 (需要JSON解析)
  JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.text')) AS message_content,
  
  -- 情感和满意度
  COALESCE(cm.emotion_type, cl.emotion_type) AS emotion_type,
  cl.satisfaction AS survey,
  
  -- 工单信息 (从事件表获取)
  CASE 
    WHEN cme.biz_type = 'WORK_ORDER' THEN 1 
    ELSE 0 
  END AS work_order,
  
  -- 其他分析字段
  cl.summary AS conversation_summary,
  cl.current_progress AS progress_status,
  cl.trigger_buz_chance AS business_trigger

FROM chat_msg cm
LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
WHERE cm.deleted = 0 AND cl.deleted = 0
```

## 🔧 数据转换挑战和解决方案

### 1. 时间戳转换
**问题**: MySQL使用毫秒级timestamp，PostgreSQL期望datetime
```sql
-- 转换方案
FROM_UNIXTIME(timestamp/1000) AS created_at
```

### 2. JSON字段解析
**问题**: payload字段是JSON格式，需要提取text内容
```sql
-- 解析方案
JSON_UNQUOTE(JSON_EXTRACT(payload, '$.text')) AS message_content
```

### 3. 满意度标准化
**问题**: satisfaction字段可能是多种格式
```sql
-- 标准化方案
CASE 
  WHEN satisfaction IN ('很满意', '满意') THEN '满意'
  WHEN satisfaction = '一般' THEN '一般'
  WHEN satisfaction IN ('不满意', '很不满意') THEN '不满意'
  ELSE NULL
END AS survey
```

### 4. 工单信息提取
**问题**: 工单信息散布在chat_msg_event表中
```sql
-- 提取方案
SELECT 
  cm.id,
  COUNT(CASE WHEN cme.biz_type = 'WORK_ORDER' THEN 1 END) AS work_order_count,
  GROUP_CONCAT(cme.biz_result) AS work_order_details
FROM chat_msg cm
LEFT JOIN chat_msg_event cme ON cm.message_id = cme.message_id
WHERE cme.biz_type = 'WORK_ORDER'
GROUP BY cm.id
```

## 📊 数据质量分析

### 数据完整性评估
```sql
-- 评估各关键字段的完整性
SELECT 
  COUNT(*) as total_records,
  COUNT(chat_id) as has_chat_id,
  COUNT(external_user_id) as has_user_id,
  COUNT(payload) as has_content,
  COUNT(timestamp) as has_timestamp,
  
  -- 计算完整性百分比
  ROUND(COUNT(chat_id)/COUNT(*)*100, 2) as chat_id_completeness,
  ROUND(COUNT(external_user_id)/COUNT(*)*100, 2) as user_id_completeness,
  ROUND(COUNT(payload)/COUNT(*)*100, 2) as content_completeness
FROM chat_msg 
WHERE deleted = 0;
```

### 时间范围分析
```sql
-- 分析数据的时间分布
SELECT 
  DATE(FROM_UNIXTIME(timestamp/1000)) as date,
  COUNT(*) as message_count
FROM chat_msg 
WHERE deleted = 0 AND timestamp IS NOT NULL
GROUP BY DATE(FROM_UNIXTIME(timestamp/1000))
ORDER BY date DESC
LIMIT 30;
```

## 🚀 迁移实施方案

### Phase 1: 数据清洗和验证
1. **清理无效数据**: 删除deleted=1的记录
2. **验证关联关系**: 确保chat_msg与chat_list的关联完整性
3. **标准化字段**: 统一时间格式、满意度等级等

### Phase 2: 增量数据提取
```sql
-- 创建视图用于数据提取
CREATE VIEW conversations_extract AS
SELECT 
  cm.id,
  cm.chat_id,
  FROM_UNIXTIME(cm.timestamp/1000) AS created_at,
  cm.external_user_id AS user_id,
  cm.contact_name AS user_name,
  JSON_UNQUOTE(JSON_EXTRACT(cm.payload, '$.text')) AS content,
  cl.project_code AS project_id,
  cl.project_name AS project_name,
  sb.grid_name AS grid_name,
  COALESCE(cm.emotion_type, cl.emotion_type) AS emotion_type,
  CASE 
    WHEN cl.satisfaction IN ('很满意', '满意') THEN '满意'
    WHEN cl.satisfaction = '一般' THEN '一般'
    WHEN cl.satisfaction IN ('不满意', '很不满意') THEN '不满意'
    ELSE NULL
  END AS survey,
  CASE 
    WHEN EXISTS(
      SELECT 1 FROM chat_msg_event cme 
      WHERE cme.message_id = cm.message_id 
      AND cme.biz_type = 'WORK_ORDER'
    ) THEN 1 
    ELSE 0 
  END AS work_order

FROM chat_msg cm
LEFT JOIN chat_list cl ON cm.chat_id = cl.chat_id
LEFT JOIN sys_bot sb ON cm.bot_id = sb.im_bot_id
WHERE cm.deleted = 0 
  AND cm.timestamp IS NOT NULL
  AND cm.payload IS NOT NULL;
```

### Phase 3: 数据同步机制
1. **ETL Pipeline**: 使用Node.js创建ETL流程
2. **增量同步**: 基于update_time字段进行增量更新
3. **数据验证**: 对比迁移前后的统计数据

## 📈 预期迁移结果

### 数据规模预估
- **conversations表**: ~30,000条记录 (来自chat_msg)
- **项目维度**: ~50个项目
- **时间跨度**: 2024年至今
- **网格信息**: ~70个网格

### 性能提升预期
- **查询响应时间**: 从1-5秒提升到<100ms
- **数据分析能力**: 支持复杂的多维度分析
- **实时性**: 准实时数据更新 (<5分钟延迟) 